{"format": 1, "restore": {"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Web\\Application.Web.csproj": {}}, "projects": {"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Web\\Application.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Web\\Application.Web.csproj", "projectName": "Application.Web", "projectPath": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Web\\Application.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"target": "Package", "version": "[8.7.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.0, )"}, "OpenIddict.Validation.AspNetCore": {"target": "Package", "version": "[6.1.1, )"}, "OpenIddict.Validation.ServerIntegration": {"target": "Package", "version": "[5.2.0, )"}, "OpenIddict.Validation.SystemNetHttp": {"target": "Package", "version": "[6.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}