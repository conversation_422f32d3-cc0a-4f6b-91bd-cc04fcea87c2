{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Run API",
            "type": "shell",
            "command": "dotnet",
            "args": ["run", "--launch-profile", "Application"],
            "options": {
                "cwd": "${workspaceFolder}/cuckhqs/Application.API"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [],
            "runOptions": {
                "runOn": "folderOpen"
            }
        },
        {
            "label": "Run OpenIddict Server",
            "type": "shell",
            "command": "dotnet",
            "args": ["run", "--launch-profile", "OpeniddictServer"],
            "options": {
                "cwd": "${workspaceFolder}/cuckhqs/OpeniddictServer"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [],
            "runOptions": {
                "runOn": "folderOpen"
            }
        },
        {
            "label": "Run Angular Frontend",
            "type": "shell",
            "command": "yarn",
            "args": ["start"],
            "options": {
                "cwd": "${workspaceFolder}/cuckhqs_fe"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [],
            "runOptions": {
                "runOn": "folderOpen"
            }
        },
        {
            "label": "Start All Services",
            "dependsOrder": "parallel",
            "dependsOn": [
                "Run OpenIddict Server",
                "Run API",
                "Run Angular Frontend"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": true,
                "clear": false
            }
        }
    ]
}