{"GlobalPropertiesHash": "PvAhZaRYyoPVgi6b1OCcrnPAsm6NYYvWE2kyx2pUyws=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["8KmD4dEWfMb11Dt6Ei9vIFa/H2n9gg8HlmBdApE/+Ek=", "dIv17AT+41Y3EsrtKBKp0IENGWwje2KSj71nAxt5XeA=", "yK9q6Z8KxF9iV/lLJ8Nvl9lJCHUGVFbWmh7b0zuH96o=", "PKA4IIHHItWSZ8lPk8x3bqECX3p3hK4MdE/SGy5SEf4=", "LbmOvdBbDHAupqjm61Uo0o/IJlfaDtkhqlaYdIWvr/w=", "L4tUWZW2rElWa0WOb9Bd+ACU/XFVLG046KU5MheW/tM="], "CachedAssets": {"8KmD4dEWfMb11Dt6Ei9vIFa/H2n9gg8HlmBdApE/+Ek=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\App_Data\\times.ttf", "SourceId": "Application.API", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\", "BasePath": "_content/Application.API", "RelativePath": "App_Data/times#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nzcq01ozbz", "Integrity": "Q6iQeVrRM9c1aNFpo1kk9AQWvoppUKGgMmMFLsjpW4o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\App_Data\\times.ttf", "FileLength": 1206884, "LastWriteTime": "2025-07-21T14:54:49.5001228+00:00"}, "dIv17AT+41Y3EsrtKBKp0IENGWwje2KSj71nAxt5XeA=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\Resources\\Mau_bao_cao_tuan.rdlc", "SourceId": "Application.API", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\", "BasePath": "_content/Application.API", "RelativePath": "Resources/Mau_bao_cao_tuan#[.{fingerprint}]?.rdlc", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56cxbsj16x", "Integrity": "3QbCbtLMWn41naSC3535X99tPkj2jnea5fToLoI9t3c=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Resources\\Mau_bao_cao_tuan.rdlc", "FileLength": 57304, "LastWriteTime": "2025-07-21T14:54:49.5011198+00:00"}, "yK9q6Z8KxF9iV/lLJ8Nvl9lJCHUGVFbWmh7b0zuH96o=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\Templates\\WorkingScheduleTemplate.docx", "SourceId": "Application.API", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\", "BasePath": "_content/Application.API", "RelativePath": "Templates/WorkingScheduleTemplate#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwd10xbcw3", "Integrity": "ZC9DxML8wJOrB9h31RKfEjNikKL0Z628zaYt/BAT4R4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\WorkingScheduleTemplate.docx", "FileLength": 15445, "LastWriteTime": "2025-07-21T14:54:49.5011198+00:00"}}, "CachedCopyCandidates": {}}