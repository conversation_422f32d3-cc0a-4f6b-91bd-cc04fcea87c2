{"ConnectionStrings": {"DefaultConnection": "Server=*************;Database=msi_core;User Id=sa;Password=********;TrustServerCertificate=True;MultipleActiveResultSets=true;Connect Timeout=100"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Keycloak": {"ServerRealm": "http://*************:8080/realms/master", "Metadata": "http://*************:8080/realms/master/.well-known/openid_configuration", "ClientId": "sonnguyen-dev-client", "ClientSecret": "your-dev-secret"}, "EmailSettings": {"Host": "localhost", "Port": 1025, "Username": "", "Password": "", "SenderEmail": "<EMAIL>", "SenderName": "<PERSON> - Auth Server"}}