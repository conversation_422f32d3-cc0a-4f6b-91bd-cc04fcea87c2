﻿"restore":{"projectUniqueName":"D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\OpeniddictServer.csproj","projectName":"OpeniddictServer","projectPath":"D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\OpeniddictServer.csproj","outputPath":"D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net9.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Infrastructure\\Application.Infrastructure.csproj":{"projectPath":"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Infrastructure\\Application.Infrastructure.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Fido2":{"target":"Package","version":"[3.0.1, )"},"Microsoft.AspNetCore.Authentication.OpenIdConnect":{"target":"Package","version":"[9.0.0, )"},"Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore":{"target":"Package","version":"[9.0.0, )"},"Microsoft.AspNetCore.Identity.EntityFrameworkCore":{"target":"Package","version":"[9.0.0, )"},"Microsoft.AspNetCore.Identity.UI":{"target":"Package","version":"[9.0.0, )"},"Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation":{"target":"Package","version":"[9.0.0, )"},"Microsoft.EntityFrameworkCore.SqlServer":{"target":"Package","version":"[9.0.0, )"},"Microsoft.EntityFrameworkCore.Tools":{"suppressParent":"All","target":"Package","version":"[9.0.0, )"},"Microsoft.VisualStudio.Web.CodeGeneration.Design":{"target":"Package","version":"[9.0.0, )"},"OpenIddict.AspNetCore":{"target":"Package","version":"[6.1.1, )"},"OpenIddict.EntityFrameworkCore":{"target":"Package","version":"[6.1.1, )"},"OpenIddict.Quartz":{"target":"Package","version":"[6.1.1, )"},"Quartz.Extensions.Hosting":{"target":"Package","version":"[3.13.1, )"},"Serilog":{"target":"Package","version":"[4.2.0, )"},"Serilog.AspNetCore":{"target":"Package","version":"[9.0.0, )"},"Serilog.Extensions.Logging":{"target":"Package","version":"[9.0.0, )"},"Serilog.Settings.Configuration":{"target":"Package","version":"[9.0.0, )"},"Serilog.Sinks.Console":{"target":"Package","version":"[6.0.0, )"},"Serilog.Sinks.File":{"target":"Package","version":"[6.0.0, )"},"Serilog.Sinks.Seq":{"target":"Package","version":"[8.0.0, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[7.2.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Runtime.win-x64","version":"[9.0.7, 9.0.7]"},{"name":"Microsoft.NETCore.App.Runtime.win-x64","version":"[9.0.7, 9.0.7]"},{"name":"Microsoft.WindowsDesktop.App.Runtime.win-x64","version":"[9.0.7, 9.0.7]"}],"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}"runtimes":{"win-x64":{"#import":[]}}