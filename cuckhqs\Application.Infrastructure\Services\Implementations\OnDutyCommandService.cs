﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.OnDutyCommand;
using Application.Infrastructure.Models.Request.OnDutyCommand;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;

namespace Application.Infrastructure.Services.Implementations
{
    public class OnDutyCommandService : IOnDutyCommandService
    {
        private readonly IUnitOfWork _unitOfWork;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public OnDutyCommandService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<BaseSearchResponse<OnDutyCommandResponse>> SearchOnDutyCommandAsync(SearchOnDutyCommandRequest request)
        {
            try
            {
                IQueryable<OnDutyCommandResponse> query = _unitOfWork.OnDutyCommand.AsQueryable().AsNoTracking()
                    .Include(x => x.Employee)
                    .Where(x => (!request.year.HasValue || x.Year == request.year) && (!request.week.HasValue || x.Week == request.week))
                    .Select(s => new OnDutyCommandResponse()
                    {
                        Id = s.Id,
                        Year = s.Year,
                        Week = s.Week,
                        Date = s.Date,
                        EmployeeId_H = s.EmployeeId_H,
                        Description = s.Description,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                        Employee = EmployeeCResponse.Create(s.Employee)
                    });

                return await BaseSearchResponse<OnDutyCommandResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchOnDutyCommandAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<OnDutyCommandResponse> CreateOnDutyCommandAsync(CreateOnDutyCommandRequest request)
        {
            try
            {
                var s = CreateOnDutyCommandRequest.Create(request);

                await _unitOfWork.OnDutyCommand.AddAsync(s);
                await _unitOfWork.CommitChangesAsync();
                return OnDutyCommandResponse.Create(s);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchOnDutyCommandAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateOnDutyCommandAsync(UpdateOnDutyCommandRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.OnDutyCommand.AsQueryable().AsNoTracking().FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateOnDutyCommandRequest.Create(request);

                await _unitOfWork.OnDutyCommand.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateOnDutyCommandAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
