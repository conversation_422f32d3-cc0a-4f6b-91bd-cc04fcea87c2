{"version": 3, "targets": {"net9.0": {"Microsoft.AspNetCore.Authentication.JwtBearer/9.0.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.OpenApi/9.0.3": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.6.17"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.Extensions.AmbientMetadata.Application/9.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Compliance.Abstractions/9.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Http/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Diagnostics": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Http.Diagnostics/9.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.2.0", "Microsoft.Extensions.Http": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2", "Microsoft.Extensions.Telemetry": "9.2.0", "System.IO.Pipelines": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Http.Polly/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Http": "9.0.2", "Polly": "7.2.4", "Polly.Extensions.Http": "3.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Http.Resilience/9.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.Http.Diagnostics": "9.2.0", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Resilience": "9.2.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Http.Resilience.targets": {}}}, "Microsoft.Extensions.Logging/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.ObjectPool/9.0.2": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Primitives/9.0.2": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Resilience/9.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.2", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2", "Microsoft.Extensions.Telemetry.Abstractions": "9.2.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Telemetry/9.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.2.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.2.0", "Microsoft.Extensions.Logging.Configuration": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Telemetry.Abstractions": "9.2.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.2.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.props": {}, "buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.targets": {}}}, "Microsoft.IdentityModel.Abstractions/8.7.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.7.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.7.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/8.7.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.7.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/8.7.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.7.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.7.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "8.7.0", "System.IdentityModel.Tokens.Jwt": "8.7.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/8.7.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.IdentityModel.Logging": "8.7.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.OpenApi/1.6.17": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {"type": "package", "build": {"build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props": {}, "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets": {}}}, "OpenIddict.Abstractions/6.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2", "Microsoft.IdentityModel.Tokens": "8.4.0"}, "compile": {"lib/net9.0/OpenIddict.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/OpenIddict.Abstractions.dll": {"related": ".xml"}}}, "OpenIddict.Server/5.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Microsoft.IdentityModel.JsonWebTokens": "7.2.0", "OpenIddict.Abstractions": "5.2.0"}, "compile": {"lib/net8.0/OpenIddict.Server.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenIddict.Server.dll": {"related": ".xml"}}}, "OpenIddict.Validation/6.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Protocols": "8.4.0", "OpenIddict.Abstractions": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Validation.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/OpenIddict.Validation.dll": {"related": ".xml"}}}, "OpenIddict.Validation.AspNetCore/6.1.1": {"type": "package", "dependencies": {"OpenIddict.Validation": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Validation.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/OpenIddict.Validation.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "OpenIddict.Validation.ServerIntegration/5.2.0": {"type": "package", "dependencies": {"OpenIddict.Server": "5.2.0", "OpenIddict.Validation": "5.2.0"}, "compile": {"lib/net8.0/OpenIddict.Validation.ServerIntegration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenIddict.Validation.ServerIntegration.dll": {"related": ".xml"}}}, "OpenIddict.Validation.SystemNetHttp/6.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Http.Polly": "9.0.2", "Microsoft.Extensions.Http.Resilience": "9.2.0", "OpenIddict.Validation": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Validation.SystemNetHttp.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/OpenIddict.Validation.SystemNetHttp.dll": {"related": ".xml"}}}, "Polly/7.2.4": {"type": "package", "compile": {"lib/netstandard2.0/Polly.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Polly.dll": {"related": ".pdb;.xml"}}}, "Polly.Core/8.4.2": {"type": "package", "compile": {"lib/net8.0/Polly.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.Core.dll": {"related": ".pdb;.xml"}}}, "Polly.Extensions/8.4.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Polly.Core": "8.4.2"}, "compile": {"lib/net8.0/Polly.Extensions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"related": ".pdb;.xml"}}}, "Polly.Extensions.Http/3.0.0": {"type": "package", "dependencies": {"Polly": "7.1.0"}, "compile": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"related": ".xml"}}}, "Polly.RateLimiting/8.4.2": {"type": "package", "dependencies": {"Polly.Core": "8.4.2", "System.Threading.RateLimiting": "8.0.0"}, "compile": {"lib/net8.0/Polly.RateLimiting.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"related": ".pdb;.xml"}}}, "System.IdentityModel.Tokens.Jwt/8.7.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.7.0", "Microsoft.IdentityModel.Tokens": "8.7.0"}, "compile": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO.Pipelines/9.0.2": {"type": "package", "compile": {"lib/net9.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Threading.RateLimiting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Threading.RateLimiting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}}}, "libraries": {"Microsoft.AspNetCore.Authentication.JwtBearer/9.0.1": {"sha512": "R/ZG9llsAOn/E8WOtg+PyLCB599lxzaIGy/NbwV1xG+smkyBF4w5/AJXNDq6tVw7/qbqvd+4xLAdWQiiVj1DXw==", "type": "package", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll", "lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.xml", "microsoft.aspnetcore.authentication.jwtbearer.9.0.1.nupkg.sha512", "microsoft.aspnetcore.authentication.jwtbearer.nuspec"]}, "Microsoft.AspNetCore.OpenApi/9.0.3": {"sha512": "fKh0UyGMUE+lhbovMhh3g88b9bT+y2jfZIuJ8ljY7rcCaSJ9m2Qqqbh66oULFfzWE2BUAmimzTGcPcq3jXi/Ew==", "type": "package", "path": "microsoft.aspnetcore.openapi/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.AspNetCore.OpenApi.dll", "lib/net9.0/Microsoft.AspNetCore.OpenApi.xml", "microsoft.aspnetcore.openapi.9.0.3.nupkg.sha512", "microsoft.aspnetcore.openapi.nuspec"]}, "Microsoft.Extensions.AmbientMetadata.Application/9.2.0": {"sha512": "GMCX3zybUB22aAADjYPXrWhhd1HNMkcY5EcFAJnXy/4k5pPpJ6TS4VRl37xfrtosNyzbpO2SI7pd2Q5PvggSdg==", "type": "package", "path": "microsoft.extensions.ambientmetadata.application/9.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.AmbientMetadata.Application.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.AmbientMetadata.Application.dll", "lib/net462/Microsoft.Extensions.AmbientMetadata.Application.xml", "lib/net8.0/Microsoft.Extensions.AmbientMetadata.Application.dll", "lib/net8.0/Microsoft.Extensions.AmbientMetadata.Application.xml", "lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll", "lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.xml", "microsoft.extensions.ambientmetadata.application.9.2.0.nupkg.sha512", "microsoft.extensions.ambientmetadata.application.nuspec"]}, "Microsoft.Extensions.Compliance.Abstractions/9.2.0": {"sha512": "Te+N4xphDlGIS90lKJMZyezFiMWKLAtYV2/M8gGJG4thH6xyC7LWhMzgz2+tWMehxwZlBUq2D9DvVpjKBZFTPQ==", "type": "package", "path": "microsoft.extensions.compliance.abstractions/9.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net8.0/Microsoft.Extensions.Compliance.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Compliance.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Compliance.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Compliance.Abstractions.xml", "microsoft.extensions.compliance.abstractions.9.2.0.nupkg.sha512", "microsoft.extensions.compliance.abstractions.nuspec"]}, "Microsoft.Extensions.Configuration/9.0.2": {"sha512": "EBZW+u96tApIvNtjymXEIS44tH0I/jNwABHo4c33AchWOiDWCq2rL3klpnIo+xGrxoVGJzPDISV6hZ+a9C9SzQ==", "type": "package", "path": "microsoft.extensions.configuration/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.2.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"sha512": "I0O/270E/lUNqbBxlRVjxKOMZyYjP88dpEgQTveml+h2lTzAP4vbawLVwjS9SC7lKaU893bwyyNz0IVJYsm9EA==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.2": {"sha512": "krJ04xR0aPXrOf5dkNASg6aJjsdzexvsMRL6UNOUjiTzqBvRr95sJ1owoKEm89bSONQCfZNhHrAFV9ahDqIPIw==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.2.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"sha512": "ZffbJrskOZ40JTzcTyKwFHS5eACSWp2bUQBBApIgGV+es8RaTD4OxUG7XxFr3RIPLXtYQ1jQzF2DjKB5fZn7Qg==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {"sha512": "MNe7GSTBf3jQx5vYrXF0NZvn6l7hUKF6J54ENfAgCO8y6xjN1XUmKKWG464LP2ye6QqDiA1dkaWEZBYnhoZzjg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.2.0": {"sha512": "WcwfTpl3IcPcaahTVEaJwMUg1eWog1SkIA6jQZZFqMXiMX9/tVkhNB6yzUQmBdGWdlWDDRKpOmK7T7x1Uu05pQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.DependencyInjection.AutoActivation.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.DependencyInjection.AutoActivation.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.AutoActivation.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.AutoActivation.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.xml", "microsoft.extensions.dependencyinjection.autoactivation.9.2.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.autoactivation.nuspec"]}, "Microsoft.Extensions.Diagnostics/9.0.2": {"sha512": "kwFWk6DPaj1Roc0CExRv+TTwjsiERZA730jQIPlwCcS5tMaCAQtaGfwAK0z8CMFpVTiT+MgKXpd/P50qVCuIgg==", "type": "package", "path": "microsoft.extensions.diagnostics/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.9.0.2.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.2": {"sha512": "kFwIZEC/37cwKuEm/nXvjF7A/Myz9O7c7P9Csgz6AOiiDE62zdOG5Bu7VkROu1oMYaX0wgijPJ5LqVt6+JKjVg==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.2.0": {"sha512": "et5JevHsLv1w1O1Zhb6LiUfai/nmDRzIHnbrZJdzLsIbbMCKTZpeHuANYIppAD//n12KvgOne05j4cu0GhG9gw==", "type": "package", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Diagnostics.ExceptionSummarization.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll", "lib/net462/Microsoft.Extensions.Diagnostics.ExceptionSummarization.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.xml", "microsoft.extensions.diagnostics.exceptionsummarization.9.2.0.nupkg.sha512", "microsoft.extensions.diagnostics.exceptionsummarization.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"sha512": "IcOBmTlr2jySswU+3x8c3ql87FRwTVPQgVKaV5AXzPT5u0VItfNU8SMbESpdSp5STwxT/1R99WYszgHWsVkzhg==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/9.0.2": {"sha512": "PvjZW6CMdZbPbOwKsQXYN5VPtIWZQqdTRuBPZiW3skhU3hymB17XSlLVC4uaBbDZU+/3eHG3p80y+MzZxZqR7Q==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http/9.0.2": {"sha512": "34+kcwxPZr3Owk9eZx268+gqGNB8G/8Y96gZHomxam0IOH08FhPBjPrLWDtKdVn4+sVUUJnJMpECSTJi4XXCcg==", "type": "package", "path": "microsoft.extensions.http/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Http.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Http.targets", "lib/net462/Microsoft.Extensions.Http.dll", "lib/net462/Microsoft.Extensions.Http.xml", "lib/net8.0/Microsoft.Extensions.Http.dll", "lib/net8.0/Microsoft.Extensions.Http.xml", "lib/net9.0/Microsoft.Extensions.Http.dll", "lib/net9.0/Microsoft.Extensions.Http.xml", "lib/netstandard2.0/Microsoft.Extensions.Http.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.xml", "microsoft.extensions.http.9.0.2.nupkg.sha512", "microsoft.extensions.http.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http.Diagnostics/9.2.0": {"sha512": "Eeup1LuD5hVk5SsKAuX1D7I9sF380MjrNG10IaaauRLOmrRg8rq2TA8PYTXVBXf3MLkZ6m2xpBqRbZdxf8ygkg==", "type": "package", "path": "microsoft.extensions.http.diagnostics/9.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Http.Diagnostics.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Http.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Http.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Http.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Http.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Http.Diagnostics.xml", "microsoft.extensions.http.diagnostics.9.2.0.nupkg.sha512", "microsoft.extensions.http.diagnostics.nuspec"]}, "Microsoft.Extensions.Http.Polly/9.0.2": {"sha512": "2y5a9Iijc9iTUN1M7rH2+kUMJPuuxTgfUyL9iAOqe4ueuWtTfG1SVX/oAj35q46OV4kSgCeJC82dLQ96xOo/RQ==", "type": "package", "path": "microsoft.extensions.http.polly/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.Polly.xml", "microsoft.extensions.http.polly.9.0.2.nupkg.sha512", "microsoft.extensions.http.polly.nuspec"]}, "Microsoft.Extensions.Http.Resilience/9.2.0": {"sha512": "Km+YyCuk1IaeOsAzPDygtgsUOh3Fi89hpA18si0tFJmpSBf9aKzP9ffV5j7YOoVDvRWirpumXAPQzk1inBsvKw==", "type": "package", "path": "microsoft.extensions.http.resilience/9.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Http.Resilience.net462.targets", "buildTransitive/net462/Microsoft.Extensions.Http.Resilience.targets", "buildTransitive/net8.0/Microsoft.Extensions.Http.Resilience.targets", "lib/net462/Microsoft.Extensions.Http.Resilience.dll", "lib/net462/Microsoft.Extensions.Http.Resilience.xml", "lib/net8.0/Microsoft.Extensions.Http.Resilience.dll", "lib/net8.0/Microsoft.Extensions.Http.Resilience.xml", "lib/net9.0/Microsoft.Extensions.Http.Resilience.dll", "lib/net9.0/Microsoft.Extensions.Http.Resilience.xml", "microsoft.extensions.http.resilience.9.2.0.nupkg.sha512", "microsoft.extensions.http.resilience.nuspec"]}, "Microsoft.Extensions.Logging/9.0.2": {"sha512": "loV/0UNpt2bD+6kCDzFALVE63CDtqzPeC0LAetkdhiEr/tTNbvOlQ7CBResH7BQBd3cikrwiBfaHdyHMFUlc2g==", "type": "package", "path": "microsoft.extensions.logging/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.2.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"sha512": "dV9s2Lamc8jSaqhl2BQSPn/AryDIH2sSbQUyLitLXV0ROmsb+SROnn2cH939JFbsNrnf3mIM3GNRKT7P0ldwLg==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/9.0.2": {"sha512": "pnwYZE7U6d3Y6iMVqADOAUUMMBGYAQPsT3fMwVr/V1Wdpe5DuVGFcViZavUthSJ5724NmelIl1cYy+kRfKfRPQ==", "type": "package", "path": "microsoft.extensions.logging.configuration/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.9.0.2.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/9.0.2": {"sha512": "nWx7uY6lfkmtpyC2dGc0IxtrZZs/LnLCQHw3YYQucbqWj8a27U/dZ+eh72O3ZiolqLzzLkVzoC+w/M8dZwxRTw==", "type": "package", "path": "microsoft.extensions.objectpool/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.ObjectPool.dll", "lib/net462/Microsoft.Extensions.ObjectPool.xml", "lib/net9.0/Microsoft.Extensions.ObjectPool.dll", "lib/net9.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.9.0.2.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/9.0.2": {"sha512": "zr98z+AN8+isdmDmQRuEJ/DAKZGUTHmdv3t0ZzjHvNqvA44nAgkXE9kYtfoN6581iALChhVaSw2Owt+Z2lVbkQ==", "type": "package", "path": "microsoft.extensions.options/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.2.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.2": {"sha512": "OPm1NXdMg4Kb4Kz+YHdbBQfekh7MqQZ7liZ5dYUd+IbJakinv9Fl7Ck6Strbgs0a6E76UGbP/jHR532K/7/feQ==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.9.0.2.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.2": {"sha512": "puBMtKe/wLuYa7H6docBkLlfec+h8L35DXqsDKKJgW0WY5oCwJ3cBJKcDaZchv6knAyqOMfsl6VUbaR++E5LXA==", "type": "package", "path": "microsoft.extensions.primitives/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.2.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Resilience/9.2.0": {"sha512": "dyaM+Jeznh/i21bOrrRs3xceFfn0571EOjOq95dRXmL1rHDLC4ExhACJ2xipRBP6g1AgRNqmryi+hMrVWWgmlg==", "type": "package", "path": "microsoft.extensions.resilience/9.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Resilience.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Resilience.dll", "lib/net462/Microsoft.Extensions.Resilience.xml", "lib/net8.0/Microsoft.Extensions.Resilience.dll", "lib/net8.0/Microsoft.Extensions.Resilience.xml", "lib/net9.0/Microsoft.Extensions.Resilience.dll", "lib/net9.0/Microsoft.Extensions.Resilience.xml", "microsoft.extensions.resilience.9.2.0.nupkg.sha512", "microsoft.extensions.resilience.nuspec"]}, "Microsoft.Extensions.Telemetry/9.2.0": {"sha512": "4+bw7W4RrAMrND9TxonnSmzJOdXiPxljoda8OPJiReIN607mKCc0t0Mf28sHNsTujO1XQw28wsI0poxeeQxohw==", "type": "package", "path": "microsoft.extensions.telemetry/9.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Telemetry.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Telemetry.dll", "lib/net462/Microsoft.Extensions.Telemetry.xml", "lib/net8.0/Microsoft.Extensions.Telemetry.dll", "lib/net8.0/Microsoft.Extensions.Telemetry.xml", "lib/net9.0/Microsoft.Extensions.Telemetry.dll", "lib/net9.0/Microsoft.Extensions.Telemetry.xml", "microsoft.extensions.telemetry.9.2.0.nupkg.sha512", "microsoft.extensions.telemetry.nuspec"]}, "Microsoft.Extensions.Telemetry.Abstractions/9.2.0": {"sha512": "kEl+5G3RqS20XaEhHh/nOugcjKEK+rgVtMJra1iuwNzdzQXElelf3vu8TugcT7rIZ/T4T76EKW1OX/fmlxz4hw==", "type": "package", "path": "microsoft.extensions.telemetry.abstractions/9.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "analyzers/dotnet/cs/Microsoft.Gen.Logging.dll", "analyzers/dotnet/cs/Microsoft.Gen.Metrics.dll", "buildTransitive/net462/Microsoft.Extensions.Telemetry.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Telemetry.Abstractions.props", "buildTransitive/net6.0/Microsoft.Extensions.Telemetry.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.props", "buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.targets", "lib/net462/Microsoft.Extensions.Telemetry.Abstractions.dll", "lib/net462/Microsoft.Extensions.Telemetry.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Telemetry.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Telemetry.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.xml", "microsoft.extensions.telemetry.abstractions.9.2.0.nupkg.sha512", "microsoft.extensions.telemetry.abstractions.nuspec"]}, "Microsoft.IdentityModel.Abstractions/8.7.0": {"sha512": "OQd5aVepYvh5evOmBMeAYjMIpEcTf1ZCBZaU7Nh/RlhhdXefjFDJeP1L2F2zeNT1unFr+wUu/h3Ac2Xb4BXU6w==", "type": "package", "path": "microsoft.identitymodel.abstractions/8.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net9.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net9.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.8.7.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/8.7.0": {"sha512": "uzsSAWhNhbrkWbQKBTE8QhzviU6sr3bJ1Bkv7gERlhswfSKOp7HsxTRLTPBpx/whQ/GRRHEwMg8leRIPbMrOgw==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/8.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.8.7.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/8.7.0": {"sha512": "Bs0TznPAu+nxa9rAVHJ+j3CYECHJkT3tG8AyBfhFYlT5ldsDhoxFT7J+PKxJHLf+ayqWfvDZHHc4639W2FQCxA==", "type": "package", "path": "microsoft.identitymodel.logging/8.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/net9.0/Microsoft.IdentityModel.Logging.dll", "lib/net9.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.8.7.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/8.7.0": {"sha512": "4r4H8LCCoNFlJHRrWCqaNtzJPM4Bfi9ARdl7Gd+OoIZqc1rsp9z60USIf00o5YwAwXwKffPUPrvufkbgR69jYA==", "type": "package", "path": "microsoft.identitymodel.protocols/8.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/net9.0/Microsoft.IdentityModel.Protocols.dll", "lib/net9.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.8.7.0.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.7.0": {"sha512": "nUifCAs2E9cvBiYBC3/L9PoftSxTVpdUdoIu7VV9M9aw7mogsdFRUn5v23c5Jl9u93jdUc0PCagrItLHncG8Qg==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/8.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.8.7.0.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/8.7.0": {"sha512": "5Z6voXjRXAnGklhmZd1mKz89UhcF5ZQQZaZc2iKrOuL4Li1UihG2vlJx8IbiFAOIxy/xdbsAm0A+WZEaH5fxng==", "type": "package", "path": "microsoft.identitymodel.tokens/8.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/net9.0/Microsoft.IdentityModel.Tokens.dll", "lib/net9.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.8.7.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.OpenApi/1.6.17": {"sha512": "Le+kehlmrlQfuDFUt1zZ2dVwrhFQtKREdKBo+rexOwaCoYP0/qpgT9tLxCsZjsgR5Itk1UKPcbgO+FyaNid/bA==", "type": "package", "path": "microsoft.openapi/1.6.17", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.6.17.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {"sha512": "8NudeHOE56YsY59HYY89akRMup8Ho+7Y3cADTGjajjWroXVU9RQai2nA6PfteB8AuzmRHZ5NZQB2BnWhQEul5g==", "type": "package", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.21.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "EULA.md", "ThirdPartyNotices.txt", "build/Container.props", "build/Container.targets", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets", "build/Rules/GeneralBrowseObject.xaml", "build/Rules/cs-CZ/GeneralBrowseObject.xaml", "build/Rules/de-DE/GeneralBrowseObject.xaml", "build/Rules/es-ES/GeneralBrowseObject.xaml", "build/Rules/fr-FR/GeneralBrowseObject.xaml", "build/Rules/it-IT/GeneralBrowseObject.xaml", "build/Rules/ja-JP/GeneralBrowseObject.xaml", "build/Rules/ko-KR/GeneralBrowseObject.xaml", "build/Rules/pl-PL/GeneralBrowseObject.xaml", "build/Rules/pt-BR/GeneralBrowseObject.xaml", "build/Rules/ru-RU/GeneralBrowseObject.xaml", "build/Rules/tr-TR/GeneralBrowseObject.xaml", "build/Rules/zh-CN/GeneralBrowseObject.xaml", "build/Rules/zh-TW/GeneralBrowseObject.xaml", "build/ToolsTarget.props", "build/ToolsTarget.targets", "icon.png", "microsoft.visualstudio.azure.containers.tools.targets.1.21.0.nupkg.sha512", "microsoft.visualstudio.azure.containers.tools.targets.nuspec", "tools/Microsoft.VisualStudio.Containers.Tools.Common.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Shared.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Tasks.dll", "tools/Newtonsoft.Json.dll", "tools/System.Security.Principal.Windows.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll"]}, "OpenIddict.Abstractions/6.1.1": {"sha512": "iNc3ox0tuR+mY2RPDqtTFhnPfxG5KHUVy4fakCbf9U8/bLhavfG3R6g/8O1SG/TIIHXzeDMmsubwCoWn9CM4Sg==", "type": "package", "path": "openiddict.abstractions/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net462/OpenIddict.Abstractions.dll", "lib/net462/OpenIddict.Abstractions.xml", "lib/net472/OpenIddict.Abstractions.dll", "lib/net472/OpenIddict.Abstractions.xml", "lib/net48/OpenIddict.Abstractions.dll", "lib/net48/OpenIddict.Abstractions.xml", "lib/net6.0/OpenIddict.Abstractions.dll", "lib/net6.0/OpenIddict.Abstractions.xml", "lib/net8.0/OpenIddict.Abstractions.dll", "lib/net8.0/OpenIddict.Abstractions.xml", "lib/net9.0/OpenIddict.Abstractions.dll", "lib/net9.0/OpenIddict.Abstractions.xml", "lib/netstandard2.0/OpenIddict.Abstractions.dll", "lib/netstandard2.0/OpenIddict.Abstractions.xml", "lib/netstandard2.1/OpenIddict.Abstractions.dll", "lib/netstandard2.1/OpenIddict.Abstractions.xml", "openiddict.abstractions.6.1.1.nupkg.sha512", "openiddict.abstractions.nuspec"]}, "OpenIddict.Server/5.2.0": {"sha512": "sEQP9crg2I7qgqS6FBO8eeZOvrreJG7UPKBXs0ZY0BKTy4tsYG8Y0abF9oSW4Wfvl87tE+/sOtp+IdjSGuJrvQ==", "type": "package", "path": "openiddict.server/5.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net461/OpenIddict.Server.dll", "lib/net461/OpenIddict.Server.xml", "lib/net472/OpenIddict.Server.dll", "lib/net472/OpenIddict.Server.xml", "lib/net48/OpenIddict.Server.dll", "lib/net48/OpenIddict.Server.xml", "lib/net6.0/OpenIddict.Server.dll", "lib/net6.0/OpenIddict.Server.xml", "lib/net7.0/OpenIddict.Server.dll", "lib/net7.0/OpenIddict.Server.xml", "lib/net8.0/OpenIddict.Server.dll", "lib/net8.0/OpenIddict.Server.xml", "lib/netstandard2.0/OpenIddict.Server.dll", "lib/netstandard2.0/OpenIddict.Server.xml", "lib/netstandard2.1/OpenIddict.Server.dll", "lib/netstandard2.1/OpenIddict.Server.xml", "openiddict.server.5.2.0.nupkg.sha512", "openiddict.server.nuspec"]}, "OpenIddict.Validation/6.1.1": {"sha512": "EswfSriq61xV4wru48RLCunSOCIULoTUqFcEG6JEstnWQoptcuppUfEWpbnpvBFO3htU9WCC9ybZXEUIvNEkAQ==", "type": "package", "path": "openiddict.validation/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net462/OpenIddict.Validation.dll", "lib/net462/OpenIddict.Validation.xml", "lib/net472/OpenIddict.Validation.dll", "lib/net472/OpenIddict.Validation.xml", "lib/net48/OpenIddict.Validation.dll", "lib/net48/OpenIddict.Validation.xml", "lib/net6.0/OpenIddict.Validation.dll", "lib/net6.0/OpenIddict.Validation.xml", "lib/net8.0/OpenIddict.Validation.dll", "lib/net8.0/OpenIddict.Validation.xml", "lib/net9.0/OpenIddict.Validation.dll", "lib/net9.0/OpenIddict.Validation.xml", "lib/netstandard2.0/OpenIddict.Validation.dll", "lib/netstandard2.0/OpenIddict.Validation.xml", "lib/netstandard2.1/OpenIddict.Validation.dll", "lib/netstandard2.1/OpenIddict.Validation.xml", "openiddict.validation.6.1.1.nupkg.sha512", "openiddict.validation.nuspec"]}, "OpenIddict.Validation.AspNetCore/6.1.1": {"sha512": "t6bjzcQUI80ViTlJ8GZgh7NoTuDRZnYOKoR3HoN+z8oqsvdaDPlxtODehGBIBDm+RvfwD2FUQQWLa1+uYNGPCw==", "type": "package", "path": "openiddict.validation.aspnetcore/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net462/OpenIddict.Validation.AspNetCore.dll", "lib/net462/OpenIddict.Validation.AspNetCore.xml", "lib/net472/OpenIddict.Validation.AspNetCore.dll", "lib/net472/OpenIddict.Validation.AspNetCore.xml", "lib/net48/OpenIddict.Validation.AspNetCore.dll", "lib/net48/OpenIddict.Validation.AspNetCore.xml", "lib/net6.0/OpenIddict.Validation.AspNetCore.dll", "lib/net6.0/OpenIddict.Validation.AspNetCore.xml", "lib/net8.0/OpenIddict.Validation.AspNetCore.dll", "lib/net8.0/OpenIddict.Validation.AspNetCore.xml", "lib/net9.0/OpenIddict.Validation.AspNetCore.dll", "lib/net9.0/OpenIddict.Validation.AspNetCore.xml", "openiddict.validation.aspnetcore.6.1.1.nupkg.sha512", "openiddict.validation.aspnetcore.nuspec"]}, "OpenIddict.Validation.ServerIntegration/5.2.0": {"sha512": "uCDlKqH+9pZF/IxjJ3s4yXW9Y5YRFBssesz8J0pOjFIjIDx9+JLT+gzl72b3bEAvPnhl41X6jKmU4bJ1YHlUFQ==", "type": "package", "path": "openiddict.validation.serverintegration/5.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net461/OpenIddict.Validation.ServerIntegration.dll", "lib/net461/OpenIddict.Validation.ServerIntegration.xml", "lib/net472/OpenIddict.Validation.ServerIntegration.dll", "lib/net472/OpenIddict.Validation.ServerIntegration.xml", "lib/net48/OpenIddict.Validation.ServerIntegration.dll", "lib/net48/OpenIddict.Validation.ServerIntegration.xml", "lib/net6.0/OpenIddict.Validation.ServerIntegration.dll", "lib/net6.0/OpenIddict.Validation.ServerIntegration.xml", "lib/net7.0/OpenIddict.Validation.ServerIntegration.dll", "lib/net7.0/OpenIddict.Validation.ServerIntegration.xml", "lib/net8.0/OpenIddict.Validation.ServerIntegration.dll", "lib/net8.0/OpenIddict.Validation.ServerIntegration.xml", "lib/netstandard2.0/OpenIddict.Validation.ServerIntegration.dll", "lib/netstandard2.0/OpenIddict.Validation.ServerIntegration.xml", "lib/netstandard2.1/OpenIddict.Validation.ServerIntegration.dll", "lib/netstandard2.1/OpenIddict.Validation.ServerIntegration.xml", "openiddict.validation.serverintegration.5.2.0.nupkg.sha512", "openiddict.validation.serverintegration.nuspec"]}, "OpenIddict.Validation.SystemNetHttp/6.1.1": {"sha512": "ShwUslQA5wFyd28BkH3LBQTU6Veow4Abl7TdhUhW3VdiHqS4aGVJSiUoMZnJljxDx8DA/w5UD8WO2S+6GTfXYw==", "type": "package", "path": "openiddict.validation.systemnethttp/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net462/OpenIddict.Validation.SystemNetHttp.dll", "lib/net462/OpenIddict.Validation.SystemNetHttp.xml", "lib/net472/OpenIddict.Validation.SystemNetHttp.dll", "lib/net472/OpenIddict.Validation.SystemNetHttp.xml", "lib/net48/OpenIddict.Validation.SystemNetHttp.dll", "lib/net48/OpenIddict.Validation.SystemNetHttp.xml", "lib/net6.0/OpenIddict.Validation.SystemNetHttp.dll", "lib/net6.0/OpenIddict.Validation.SystemNetHttp.xml", "lib/net8.0/OpenIddict.Validation.SystemNetHttp.dll", "lib/net8.0/OpenIddict.Validation.SystemNetHttp.xml", "lib/net9.0/OpenIddict.Validation.SystemNetHttp.dll", "lib/net9.0/OpenIddict.Validation.SystemNetHttp.xml", "lib/netstandard2.0/OpenIddict.Validation.SystemNetHttp.dll", "lib/netstandard2.0/OpenIddict.Validation.SystemNetHttp.xml", "lib/netstandard2.1/OpenIddict.Validation.SystemNetHttp.dll", "lib/netstandard2.1/OpenIddict.Validation.SystemNetHttp.xml", "openiddict.validation.systemnethttp.6.1.1.nupkg.sha512", "openiddict.validation.systemnethttp.nuspec"]}, "Polly/7.2.4": {"sha512": "bw00Ck5sh6ekduDE3mnCo1ohzuad946uslCDEENu3091+6UKnBuKLo4e+yaNcCzXxOZCXWY2gV4a35+K1d4LDA==", "type": "package", "path": "polly/7.2.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net461/Polly.dll", "lib/net461/Polly.pdb", "lib/net461/Polly.xml", "lib/net472/Polly.dll", "lib/net472/Polly.pdb", "lib/net472/Polly.xml", "lib/netstandard1.1/Polly.dll", "lib/netstandard1.1/Polly.pdb", "lib/netstandard1.1/Polly.xml", "lib/netstandard2.0/Polly.dll", "lib/netstandard2.0/Polly.pdb", "lib/netstandard2.0/Polly.xml", "package-icon.png", "polly.7.2.4.nupkg.sha512", "polly.nuspec"]}, "Polly.Core/8.4.2": {"sha512": "BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "type": "package", "path": "polly.core/8.4.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.Core.dll", "lib/net462/Polly.Core.pdb", "lib/net462/Polly.Core.xml", "lib/net472/Polly.Core.dll", "lib/net472/Polly.Core.pdb", "lib/net472/Polly.Core.xml", "lib/net6.0/Polly.Core.dll", "lib/net6.0/Polly.Core.pdb", "lib/net6.0/Polly.Core.xml", "lib/net8.0/Polly.Core.dll", "lib/net8.0/Polly.Core.pdb", "lib/net8.0/Polly.Core.xml", "lib/netstandard2.0/Polly.Core.dll", "lib/netstandard2.0/Polly.Core.pdb", "lib/netstandard2.0/Polly.Core.xml", "package-icon.png", "package-readme.md", "polly.core.8.4.2.nupkg.sha512", "polly.core.nuspec"]}, "Polly.Extensions/8.4.2": {"sha512": "GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "type": "package", "path": "polly.extensions/8.4.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.Extensions.dll", "lib/net462/Polly.Extensions.pdb", "lib/net462/Polly.Extensions.xml", "lib/net472/Polly.Extensions.dll", "lib/net472/Polly.Extensions.pdb", "lib/net472/Polly.Extensions.xml", "lib/net6.0/Polly.Extensions.dll", "lib/net6.0/Polly.Extensions.pdb", "lib/net6.0/Polly.Extensions.xml", "lib/net8.0/Polly.Extensions.dll", "lib/net8.0/Polly.Extensions.pdb", "lib/net8.0/Polly.Extensions.xml", "lib/netstandard2.0/Polly.Extensions.dll", "lib/netstandard2.0/Polly.Extensions.pdb", "lib/netstandard2.0/Polly.Extensions.xml", "package-icon.png", "package-readme.md", "polly.extensions.8.4.2.nupkg.sha512", "polly.extensions.nuspec"]}, "Polly.Extensions.Http/3.0.0": {"sha512": "drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "type": "package", "path": "polly.extensions.http/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.1/Polly.Extensions.Http.dll", "lib/netstandard1.1/Polly.Extensions.Http.xml", "lib/netstandard2.0/Polly.Extensions.Http.dll", "lib/netstandard2.0/Polly.Extensions.Http.xml", "polly.extensions.http.3.0.0.nupkg.sha512", "polly.extensions.http.nuspec"]}, "Polly.RateLimiting/8.4.2": {"sha512": "ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "type": "package", "path": "polly.ratelimiting/8.4.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.RateLimiting.dll", "lib/net462/Polly.RateLimiting.pdb", "lib/net462/Polly.RateLimiting.xml", "lib/net472/Polly.RateLimiting.dll", "lib/net472/Polly.RateLimiting.pdb", "lib/net472/Polly.RateLimiting.xml", "lib/net6.0/Polly.RateLimiting.dll", "lib/net6.0/Polly.RateLimiting.pdb", "lib/net6.0/Polly.RateLimiting.xml", "lib/net8.0/Polly.RateLimiting.dll", "lib/net8.0/Polly.RateLimiting.pdb", "lib/net8.0/Polly.RateLimiting.xml", "lib/netstandard2.0/Polly.RateLimiting.dll", "lib/netstandard2.0/Polly.RateLimiting.pdb", "lib/netstandard2.0/Polly.RateLimiting.xml", "package-icon.png", "package-readme.md", "polly.ratelimiting.8.4.2.nupkg.sha512", "polly.ratelimiting.nuspec"]}, "System.IdentityModel.Tokens.Jwt/8.7.0": {"sha512": "8dKL3A9pVqYCJIXHd4H2epQqLxSvKeNxGonR0e5g89yMchyvsM/NLuB06otx29BicUd6+LUJZgNZmvYjjPsPGg==", "type": "package", "path": "system.identitymodel.tokens.jwt/8.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net9.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net9.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.8.7.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO.Pipelines/9.0.2": {"sha512": "UIBaK7c/A3FyQxmX/747xw4rCUkm1BhNiVU617U5jweNJssNjLJkPUGhBsrlDG0BpKWCYKsncD+Kqpy4KmvZZQ==", "type": "package", "path": "system.io.pipelines/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/net9.0/System.IO.Pipelines.dll", "lib/net9.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.9.0.2.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.RateLimiting/8.0.0": {"sha512": "7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "type": "package", "path": "system.threading.ratelimiting/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.RateLimiting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.RateLimiting.targets", "lib/net462/System.Threading.RateLimiting.dll", "lib/net462/System.Threading.RateLimiting.xml", "lib/net6.0/System.Threading.RateLimiting.dll", "lib/net6.0/System.Threading.RateLimiting.xml", "lib/net7.0/System.Threading.RateLimiting.dll", "lib/net7.0/System.Threading.RateLimiting.xml", "lib/net8.0/System.Threading.RateLimiting.dll", "lib/net8.0/System.Threading.RateLimiting.xml", "lib/netstandard2.0/System.Threading.RateLimiting.dll", "lib/netstandard2.0/System.Threading.RateLimiting.xml", "system.threading.ratelimiting.8.0.0.nupkg.sha512", "system.threading.ratelimiting.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["Microsoft.AspNetCore.Authentication.JwtBearer >= 9.0.1", "Microsoft.AspNetCore.OpenApi >= 9.0.3", "Microsoft.IdentityModel.Protocols.OpenIdConnect >= 8.7.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets >= 1.21.0", "OpenIddict.Validation.AspNetCore >= 6.1.1", "OpenIddict.Validation.ServerIntegration >= 5.2.0", "OpenIddict.Validation.SystemNetHttp >= 6.1.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Web\\Application.Web.csproj", "projectName": "Application.Web", "projectPath": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Web\\Application.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"target": "Package", "version": "[8.7.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.0, )"}, "OpenIddict.Validation.AspNetCore": {"target": "Package", "version": "[6.1.1, )"}, "OpenIddict.Validation.ServerIntegration": {"target": "Package", "version": "[5.2.0, )"}, "OpenIddict.Validation.SystemNetHttp": {"target": "Package", "version": "[6.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}