﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.Schedule;
using Application.Infrastructure.Models.Request.Schedule;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;

namespace Application.Infrastructure.Services.Implementations
{
    public class ScheduleService : IScheduleService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public ScheduleService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<BaseSearchResponse<ScheduleResponse>> SearchScheduleAsync(SearchScheduleRequest request)
        {
            try
            {
                IQueryable<ScheduleResponse> query = _unitOfWork.Schedule.AsQueryable().AsNoTracking()
                    .Where(x => (request.year == x.Year.ToString()) && (request.week == x.Week.ToString()))
                    .Select(s => new ScheduleResponse()
                    {
                        Id = s.Id,
                        Year = s.Year,
                        Week = s.Week,
                        Date = s.Date,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                        Rank = s.Rank,
                        Name = s.Name
                    });

                return await BaseSearchResponse<ScheduleResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                    log.Error("Lỗi tại SearchScheduleAsync: " + ex.ToString());
                    throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<ScheduleResponse> CreateScheduleAsync(CreateScheduleRequest request)
        {
            try
            {
                var entity = CreateScheduleRequest.Create(request);

                await _unitOfWork.Schedule.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return ScheduleResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateScheduleAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteScheduleAsync(DeleteScheduleRequest request)
        {
            try
            {
                var record = await _unitOfWork.Schedule.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.Schedule.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteScheduleAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateScheduleAsync(UpdateScheduleRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.Schedule.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateScheduleRequest.Create(request);

                await _unitOfWork.Schedule.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateScheduleAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
