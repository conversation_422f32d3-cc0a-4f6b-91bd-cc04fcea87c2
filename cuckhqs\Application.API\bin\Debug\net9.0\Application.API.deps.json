{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Application.API/1.0.0": {"dependencies": {"Application.Infrastructure": "1.0.0", "Aspose.Words": "25.4.0", "BoldReports.Net.Core": "6.2.39", "ClosedXML": "0.104.1", "EPPlus": "7.5.2", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.2", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "9.0.0", "Microsoft.AspNetCore.OpenApi": "9.0.0", "Microsoft.AspNetCore.SignalR": "1.2.0", "Microsoft.EntityFrameworkCore": "9.0.2", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.0", "Microsoft.Extensions.ApiDescription.Server": "9.0.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.7.0", "NetEscapades.AspNetCore.SecurityHeaders": "1.0.0-preview.3", "OpenIddict.Server": "6.3.0", "OpenIddict.Server.AspNetCore": "6.3.0", "OpenIddict.Validation.AspNetCore": "6.1.1", "OpenIddict.Validation.SystemNetHttp": "6.1.1", "QuestPDF": "2025.4.2", "Serilog.AspNetCore": "9.0.0", "Serilog.Enrichers.Environment": "3.0.1", "Serilog.Enrichers.Thread": "4.0.0", "Serilog.Sinks.ApplicationInsights": "4.0.0", "Serilog.Sinks.Async": "2.1.0", "Swashbuckle.AspNetCore": "6.7.0", "Swashbuckle.AspNetCore.SwaggerUI": "7.2.0"}, "runtime": {"Application.API.dll": {}}}, "Aspose.Words/25.4.0": {"dependencies": {"Autofac": "6.4.0", "Microsoft.Win32.Registry": "5.0.0", "Newtonsoft.Json": "13.0.3", "SkiaSharp": "3.116.1", "System.Reflection.Emit": "4.7.0", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net9.0/Aspose.Words.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Autofac/6.4.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Autofac.dll": {"assemblyVersion": "6.4.0.0", "fileVersion": "6.4.0.0"}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.3": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.60.3", "Microsoft.Identity.Client.Extensions.Msal": "4.60.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.3.0", "fileVersion": "1.1100.324.25704"}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "BitMiracle.LibTiff.NET/2.4.649": {"runtime": {"lib/netstandard2.0/BitMiracle.LibTiff.NET.dll": {"assemblyVersion": "2.4.649.0", "fileVersion": "2.4.649.0"}}}, "Bold.Licensing/6.2.39": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Bold.Licensing.dll": {"assemblyVersion": "6.2.39.0", "fileVersion": "6.2.39.0"}}}, "BoldReports.Net.Core/6.2.39": {"dependencies": {"Bold.Licensing": "6.2.39", "Microsoft.CodeAnalysis.VisualBasic": "4.8.0", "Microsoft.Data.SqlClient": "5.2.1", "Microsoft.Windows.Compatibility": "8.0.0", "Newtonsoft.Json": "13.0.3", "SSH.NET": "2020.0.2", "SkiaSharp": "3.116.1", "SkiaSharp.NativeAssets.Linux": "2.88.6", "SkiaSharp.Svg": "1.60.0", "Syncfusion.Compression.Net.Core": "26.1.35", "Syncfusion.DocIO.Net.Core": "26.1.35", "Syncfusion.OfficeChart.Net.Core": "26.1.35", "Syncfusion.Pdf.Imaging.Net.Core": "26.1.35", "Syncfusion.Pdf.Net.Core": "26.1.35", "Syncfusion.Presentation.Net.Core": "26.1.35", "Syncfusion.XlsIO.Net.Core": "26.1.35", "System.CodeDom": "8.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Data.Odbc": "8.0.0", "System.Data.OleDb": "8.0.0", "System.Drawing.Common": "8.0.4"}, "runtime": {"lib/net8.0/BoldReports.Web.dll": {"assemblyVersion": "6.2800.39.0", "fileVersion": "6.2800.39.0"}}}, "BouncyCastle.Cryptography/2.5.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.5.1.28965"}}}, "ClosedXML/0.104.1": {"dependencies": {"ClosedXML.Parser": "1.2.0", "DocumentFormat.OpenXml": "3.3.0", "ExcelNumberFormat": "1.1.0", "RBush": "3.2.0", "SixLabors.Fonts": "1.0.0", "System.IO.Packaging": "8.0.1"}, "runtime": {"lib/netstandard2.1/ClosedXML.dll": {"assemblyVersion": "0.104.1.0", "fileVersion": "0.104.1.0"}}}, "ClosedXML.Parser/1.2.0": {"runtime": {"lib/netstandard2.1/ClosedXML.Parser.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DocumentFormat.OpenXml/3.3.0": {"dependencies": {"DocumentFormat.OpenXml.Framework": "3.3.0"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "DocumentFormat.OpenXml.Framework/3.3.0": {"dependencies": {"System.IO.Packaging": "8.0.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "EPPlus/7.5.2": {"dependencies": {"EPPlus.System.Drawing": "7.5.0", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.IO.RecyclableMemoryStream": "3.0.1", "System.ComponentModel.Annotations": "5.0.0", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net8.0/EPPlus.dll": {"assemblyVersion": "7.5.2.0", "fileVersion": "7.5.2.0"}}}, "EPPlus.Interfaces/7.5.0": {"runtime": {"lib/net8.0/EPPlus.Interfaces.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.0"}}}, "EPPlus.System.Drawing/7.5.0": {"dependencies": {"EPPlus.Interfaces": "7.5.0", "System.Drawing.Common": "8.0.4"}, "runtime": {"lib/net8.0/EPPlus.System.Drawing.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.0"}}}, "ExcelDataReader/3.7.0": {"runtime": {"lib/netstandard2.1/ExcelDataReader.dll": {"assemblyVersion": "3.7.0.0", "fileVersion": "3.7.0.0"}}}, "ExcelDataReader.DataSet/3.7.0": {"dependencies": {"ExcelDataReader": "3.7.0"}, "runtime": {"lib/netstandard2.1/ExcelDataReader.DataSet.dll": {"assemblyVersion": "3.7.0.0", "fileVersion": "3.7.0.0"}}}, "ExcelNumberFormat/1.1.0": {"runtime": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "Fido2/3.0.1": {"dependencies": {"Fido2.Models": "3.0.1", "Microsoft.Extensions.Http": "9.0.2", "NSec.Cryptography": "22.4.0", "System.Formats.Cbor": "6.0.0", "System.IdentityModel.Tokens.Jwt": "8.7.0"}, "runtime": {"lib/net6.0/Fido2.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "Fido2.Models/3.0.1": {"runtime": {"lib/net6.0/Fido2.Models.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "FluentValidation/11.9.2": {"runtime": {"lib/net8.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "11.9.2.0"}}}, "FluentValidation.AspNetCore/11.3.0": {"dependencies": {"FluentValidation": "11.9.2", "FluentValidation.DependencyInjectionExtensions": "11.9.2"}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "FluentValidation.DependencyInjectionExtensions/11.9.2": {"dependencies": {"FluentValidation": "11.9.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "1*******", "fileVersion": "11.9.2.0"}}}, "libsodium/********": {"runtimeTargets": {"runtimes/linux-arm/native/libsodium.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libsodium.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libsodium.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libsodium.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libsodium.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libsodium.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libsodium.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.18.0"}, "runtimes/win-x86/native/libsodium.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.18.0"}}}, "log4net/2.0.17": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"lib/netstandard2.0/log4net.dll": {"assemblyVersion": "2.0.17.0", "fileVersion": "2.0.17.0"}}}, "MailKit/4.12.1": {"dependencies": {"MimeKit": "4.12.0", "System.Formats.Asn1": "9.0.0"}, "runtime": {"lib/net8.0/MailKit.dll": {"assemblyVersion": "********", "fileVersion": "4.12.1.0"}}}, "Microsoft.ApplicationInsights/2.20.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "2.20.0.103", "fileVersion": "2.20.0.103"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.7.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "9.0.1.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Authorization/2.3.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.AspNetCore.Authorization.Policy/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.3.0", "Microsoft.AspNetCore.Authorization": "2.3.0"}}, "Microsoft.AspNetCore.Connections.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "System.IO.Pipelines": "9.0.2"}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.2": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.2": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.2"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2"}}, "Microsoft.AspNetCore.Http/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.WebUtilities": "2.3.0", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Net.Http.Headers": "2.3.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http.Connections/1.2.0": {"dependencies": {"Microsoft.AspNetCore.Authorization.Policy": "2.3.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http": "2.3.0", "Microsoft.AspNetCore.Http.Connections.Common": "1.2.0", "Microsoft.AspNetCore.Routing": "2.3.0", "Microsoft.AspNetCore.WebSockets": "2.3.0", "Newtonsoft.Json": "13.0.3", "System.Net.WebSockets.WebSocketProtocol": "5.1.0"}}, "Microsoft.AspNetCore.Http.Connections.Common/1.2.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.3.0", "Newtonsoft.Json": "13.0.3", "System.Buffers": "4.6.0", "System.IO.Pipelines": "9.0.2"}}, "Microsoft.AspNetCore.Http.Extensions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Net.Http.Headers": "2.3.0", "System.Buffers": "4.6.0"}}, "Microsoft.AspNetCore.Http.Features/2.3.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Microsoft.Extensions.Identity.Stores": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}}, "Microsoft.AspNetCore.JsonPatch/9.0.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/9.0.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "9.0.0", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.OpenApi/9.0.0": {"dependencies": {"Microsoft.OpenApi": "1.6.17"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Routing/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.3.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.AspNetCore.Routing.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0"}}, "Microsoft.AspNetCore.SignalR/1.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Connections": "1.2.0", "Microsoft.AspNetCore.SignalR.Core": "1.2.0", "Microsoft.AspNetCore.WebSockets": "2.3.0", "System.IO.Pipelines": "9.0.2"}}, "Microsoft.AspNetCore.SignalR.Common/1.2.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.3.0", "Microsoft.Extensions.Options": "9.0.2", "Newtonsoft.Json": "13.0.3", "System.Buffers": "4.6.0"}}, "Microsoft.AspNetCore.SignalR.Core/1.2.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "2.3.0", "Microsoft.AspNetCore.SignalR.Common": "1.2.0", "Microsoft.AspNetCore.SignalR.Protocols.Json": "1.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "System.IO.Pipelines": "9.0.2", "System.Reflection.Emit": "4.7.0", "System.Threading.Channels": "8.0.0"}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.2.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "1.2.0", "Newtonsoft.Json": "13.0.3", "System.IO.Pipelines": "9.0.2"}}, "Microsoft.AspNetCore.WebSockets/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.Extensions.Options": "9.0.2", "System.Net.WebSockets.WebSocketProtocol": "5.1.0"}}, "Microsoft.AspNetCore.WebUtilities/2.3.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.3.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}}}, "Microsoft.CodeAnalysis.VisualBasic/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.VisualBasic.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/fr/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "fr"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.2.1": {"dependencies": {"Azure.Identity": "1.11.3", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.60.3", "Microsoft.IdentityModel.JsonWebTokens": "8.7.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.7.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Runtime.Caching": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.21.24152.3"}}, "resources": {"lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.21.24152.3"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.21.24152.3"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "5.2.0.0"}}}, "Microsoft.EntityFrameworkCore/9.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.2", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6701"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.2": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6701"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.2": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6701"}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.1", "Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "System.Formats.Asn1": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}}, "Microsoft.Extensions.AmbientMetadata.Application/9.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.ApiDescription.Server/9.0.1": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Caching.Memory/9.0.2": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Compliance.Abstractions/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.Configuration/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2"}}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.2.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Diagnostics/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {}, "Microsoft.Extensions.Hosting.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Http/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Diagnostics": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Http.Diagnostics/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.2.0", "Microsoft.Extensions.Http": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2", "Microsoft.Extensions.Telemetry": "9.2.0", "System.IO.Pipelines": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.Http.Polly/9.0.2": {"dependencies": {"Microsoft.Extensions.Http": "9.0.2", "Polly": "7.2.4", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}}, "Microsoft.Extensions.Http.Resilience/9.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.Http.Diagnostics": "9.2.0", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Resilience": "9.2.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.Identity.Core/9.0.2": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}}, "Microsoft.Extensions.Identity.Stores/9.0.2": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.2", "Microsoft.Extensions.Identity.Core": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}}, "Microsoft.Extensions.Logging/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.ObjectPool/9.0.2": {"runtime": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}}, "Microsoft.Extensions.Options/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Primitives/9.0.2": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Resilience/9.2.0": {"dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.2", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2", "Microsoft.Extensions.Telemetry.Abstractions": "9.2.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.Telemetry/9.2.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.2.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.2.0", "Microsoft.Extensions.Logging.Configuration": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Telemetry.Abstractions": "9.2.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.2.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.2.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Identity.Client/4.60.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.7.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.60.3.0", "fileVersion": "4.60.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.60.3": {"dependencies": {"Microsoft.Identity.Client": "4.60.3", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.60.3.0", "fileVersion": "4.60.3.0"}}}, "Microsoft.IdentityModel.Abstractions/8.7.0": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.7.0.60321"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.7.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.7.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.7.0.60321"}}}, "Microsoft.IdentityModel.Logging/8.7.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.7.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.7.0.60321"}}}, "Microsoft.IdentityModel.Protocols/8.7.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.7.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.7.0.60321"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.7.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.7.0", "System.IdentityModel.Tokens.Jwt": "8.7.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.7.0.60321"}}}, "Microsoft.IdentityModel.Tokens/8.7.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.IdentityModel.Logging": "8.7.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.7.0.60321"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "Microsoft.Net.Http.Headers/2.3.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2", "System.Buffers": "4.6.0"}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.6.17": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.Registry.AccessControl/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Windows.Compatibility/8.0.0": {"dependencies": {"Microsoft.Win32.Registry.AccessControl": "8.0.0", "Microsoft.Win32.SystemEvents": "8.0.0", "System.CodeDom": "8.0.0", "System.ComponentModel.Composition": "8.0.0", "System.ComponentModel.Composition.Registration": "8.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Data.Odbc": "8.0.0", "System.Data.OleDb": "8.0.0", "System.Data.SqlClient": "4.8.5", "System.Diagnostics.EventLog": "9.0.0", "System.Diagnostics.PerformanceCounter": "8.0.0", "System.DirectoryServices": "8.0.0", "System.DirectoryServices.AccountManagement": "8.0.0", "System.DirectoryServices.Protocols": "8.0.0", "System.Drawing.Common": "8.0.4", "System.IO.Packaging": "8.0.1", "System.IO.Ports": "8.0.0", "System.Management": "8.0.0", "System.Reflection.Context": "8.0.0", "System.Runtime.Caching": "8.0.0", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.ProtectedData": "9.0.0", "System.Security.Cryptography.Xml": "8.0.0", "System.Security.Permissions": "8.0.0", "System.ServiceModel.Duplex": "4.10.0", "System.ServiceModel.Http": "4.10.0", "System.ServiceModel.NetTcp": "4.10.0", "System.ServiceModel.Primitives": "4.10.0", "System.ServiceModel.Security": "4.10.0", "System.ServiceModel.Syndication": "8.0.0", "System.ServiceProcess.ServiceController": "8.0.0", "System.Speech": "8.0.0", "System.Text.Encoding.CodePages": "8.0.0", "System.Threading.AccessControl": "8.0.0", "System.Web.Services.Description": "4.10.0"}}, "MimeKit/4.12.0": {"dependencies": {"BouncyCastle.Cryptography": "2.5.1", "System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/MimeKit.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NetEscapades.AspNetCore.SecurityHeaders/1.0.0-preview.3": {"runtime": {"lib/netcoreapp3.1/NetEscapades.AspNetCore.SecurityHeaders.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "NSec.Cryptography/22.4.0": {"dependencies": {"libsodium": "********"}, "runtime": {"lib/net5.0/NSec.Cryptography.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OpenIddict.Abstractions/6.3.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2", "Microsoft.IdentityModel.Tokens": "8.7.0"}, "runtime": {"lib/net9.0/OpenIddict.Abstractions.dll": {"assemblyVersion": "6.3.0.0", "fileVersion": "6.300.25.26476"}}}, "OpenIddict.Core/6.1.1": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "OpenIddict.Abstractions": "6.3.0"}, "runtime": {"lib/net9.0/OpenIddict.Core.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.EntityFrameworkCore/6.1.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "OpenIddict.Core": "6.1.1", "OpenIddict.EntityFrameworkCore.Models": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.EntityFrameworkCore.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.EntityFrameworkCore.Models/6.1.1": {"runtime": {"lib/net9.0/OpenIddict.EntityFrameworkCore.Models.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Server/6.3.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.7.0", "OpenIddict.Abstractions": "6.3.0"}, "runtime": {"lib/net9.0/OpenIddict.Server.dll": {"assemblyVersion": "6.3.0.0", "fileVersion": "6.300.25.26476"}}}, "OpenIddict.Server.AspNetCore/6.3.0": {"dependencies": {"OpenIddict.Server": "6.3.0"}, "runtime": {"lib/net9.0/OpenIddict.Server.AspNetCore.dll": {"assemblyVersion": "6.3.0.0", "fileVersion": "6.300.25.26476"}}}, "OpenIddict.Validation/6.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.7.0", "Microsoft.IdentityModel.Protocols": "8.7.0", "OpenIddict.Abstractions": "6.3.0"}, "runtime": {"lib/net9.0/OpenIddict.Validation.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Validation.AspNetCore/6.1.1": {"dependencies": {"OpenIddict.Validation": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.AspNetCore.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Validation.SystemNetHttp/6.1.1": {"dependencies": {"Microsoft.Extensions.Http.Polly": "9.0.2", "Microsoft.Extensions.Http.Resilience": "9.2.0", "OpenIddict.Validation": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.SystemNetHttp.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.100.125.12678"}}}, "Polly/7.2.4": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.2.4.982"}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.4"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.4.2", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "QuestPDF/2025.4.2": {"runtime": {"lib/net8.0/QuestPDF.dll": {"assemblyVersion": "2025.4.2.0", "fileVersion": "2025.4.2.0"}}, "runtimeTargets": {"runtimes/linux-arm64/native/libQuestPdfSkia.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libcrypto.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libjpeg.so.8": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libqpdf.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libQuestPdfSkia.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libcrypto.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libjpeg.so.8": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libqpdf.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libQuestPdfSkia.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libcrypto.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libjpeg.so.8": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libqpdf.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libQuestPdfSkia.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libcrypto.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libjpeg.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libqpdf.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libQuestPdfSkia.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libcrypto.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libjpeg.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libqpdf.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/QuestPdfSkia.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libgcc_s_seh-1.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libstdc++-6.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libwinpthread-1.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/qpdf.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/QuestPdfSkia.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libgcc_s_dw2-1.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libstdc++-6.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libwinpthread-1.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/qpdf.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "RBush/3.2.0": {"runtime": {"lib/net6.0/RBush.dll": {"assemblyVersion": "*******", "fileVersion": "3.2.0.0"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/8.0.0": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/8.0.0": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/8.0.0": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/8.0.0": {"dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "8.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "8.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "8.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "8.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "8.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/8.0.0": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/8.0.0": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Serilog/4.2.0": {"runtime": {"lib/net9.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/9.0.0": {"dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net9.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Environment/3.0.1": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Thread/4.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Serilog": "4.2.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.0"}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.ApplicationInsights/4.0.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.20.0", "Serilog": "4.2.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.ApplicationInsights.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Async/2.1.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Async.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "SixLabors.Fonts/1.0.0": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkiaSharp/3.116.1": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "3.116.1", "SkiaSharp.NativeAssets.macOS": "3.116.1"}, "runtime": {"lib/net8.0/SkiaSharp.dll": {"assemblyVersion": "3.116.0.0", "fileVersion": "3.116.1.0"}}}, "SkiaSharp.NativeAssets.Linux/2.88.6": {"dependencies": {"SkiaSharp": "3.116.1"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/3.116.1": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/3.116.1": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.Svg/1.60.0": {"dependencies": {"SkiaSharp": "3.116.1"}, "runtime": {"lib/netstandard2.0/SkiaSharp.Extended.Svg.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SSH.NET/2020.0.2": {"dependencies": {"SshNet.Security.Cryptography": "1.3.0"}, "runtime": {"lib/netstandard2.0/Renci.SshNet.dll": {"assemblyVersion": "2020.0.2.0", "fileVersion": "2020.0.2.0"}}}, "SshNet.Security.Cryptography/1.3.0": {"runtime": {"lib/netstandard2.0/SshNet.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/6.7.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "9.0.1", "Swashbuckle.AspNetCore.Swagger": "6.7.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.7.0", "Swashbuckle.AspNetCore.SwaggerUI": "7.2.0"}}, "Swashbuckle.AspNetCore.Swagger/6.7.0": {"dependencies": {"Microsoft.OpenApi": "1.6.17"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "6.7.0.593"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.7.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.7.0"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "6.7.0.593"}}}, "Swashbuckle.AspNetCore.SwaggerUI/7.2.0": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.0.956"}}}, "Syncfusion.Compression.Net.Core/26.1.35": {"runtime": {"lib/net8.0/Syncfusion.Compression.Portable.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.DocIO.Net.Core/26.1.35": {"dependencies": {"Syncfusion.Compression.Net.Core": "26.1.35", "Syncfusion.Licensing": "26.1.35", "Syncfusion.OfficeChart.Net.Core": "26.1.35", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net8.0/Syncfusion.DocIO.Portable.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Licensing/26.1.35": {"runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.OfficeChart.Net.Core/26.1.35": {"dependencies": {"Syncfusion.Compression.Net.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.OfficeChart.Portable.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Pdf.Imaging.Net.Core/26.1.35": {"dependencies": {"BitMiracle.LibTiff.NET": "2.4.649", "SkiaSharp": "3.116.1", "Syncfusion.Compression.Net.Core": "26.1.35", "Syncfusion.Pdf.Net.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Pdf.Imaging.Portable.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Pdf.Net.Core/26.1.35": {"dependencies": {"Syncfusion.Compression.Net.Core": "26.1.35", "Syncfusion.Licensing": "26.1.35", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net8.0/Syncfusion.Pdf.Portable.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Presentation.Net.Core/26.1.35": {"dependencies": {"Syncfusion.Compression.Net.Core": "26.1.35", "Syncfusion.Licensing": "26.1.35", "Syncfusion.OfficeChart.Net.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Presentation.Portable.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.XlsIO.Net.Core/26.1.35": {"dependencies": {"Syncfusion.Compression.Net.Core": "26.1.35", "Syncfusion.Licensing": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.XlsIO.Portable.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.Buffers/4.6.0": {}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.CodeDom/8.0.0": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable/7.0.0": {}, "System.ComponentModel.Annotations/5.0.0": {}, "System.ComponentModel.Composition/8.0.0": {"runtime": {"lib/net8.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.ComponentModel.Composition.Registration/8.0.0": {"dependencies": {"System.ComponentModel.Composition": "8.0.0", "System.Reflection.Context": "8.0.0"}, "runtime": {"lib/net8.0/System.ComponentModel.Composition.Registration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Configuration.ConfigurationManager/9.0.0": {"dependencies": {"System.Diagnostics.EventLog": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Data.Odbc/8.0.0": {"dependencies": {"System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net8.0/System.Data.Odbc.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/freebsd/lib/net8.0/System.Data.Odbc.dll": {"rid": "freebsd", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/illumos/lib/net8.0/System.Data.Odbc.dll": {"rid": "illumos", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/ios/lib/net8.0/System.Data.Odbc.dll": {"rid": "ios", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/linux/lib/net8.0/System.Data.Odbc.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/osx/lib/net8.0/System.Data.Odbc.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/solaris/lib/net8.0/System.Data.Odbc.dll": {"rid": "solaris", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/tvos/lib/net8.0/System.Data.Odbc.dll": {"rid": "tvos", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/win/lib/net8.0/System.Data.Odbc.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Data.OleDb/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0", "System.Diagnostics.PerformanceCounter": "8.0.0"}, "runtime": {"lib/net8.0/System.Data.OleDb.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Data.SqlClient/4.8.5": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.51706"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.22.51706"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.22.51706"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/9.0.0": {}, "System.Diagnostics.PerformanceCounter/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.DirectoryServices/8.0.0": {"runtime": {"lib/net8.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.DirectoryServices.AccountManagement/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0", "System.DirectoryServices": "8.0.0", "System.DirectoryServices.Protocols": "8.0.0"}, "runtime": {"lib/net8.0/System.DirectoryServices.AccountManagement.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.DirectoryServices.AccountManagement.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.DirectoryServices.Protocols/8.0.0": {"runtime": {"lib/net8.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Drawing.Common/8.0.4": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.424.16911"}}}, "System.Formats.Asn1/9.0.0": {}, "System.Formats.Cbor/6.0.0": {"runtime": {"lib/net6.0/System.Formats.Cbor.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/8.7.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.7.0", "Microsoft.IdentityModel.Tokens": "8.7.0"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.7.0.60321"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Packaging/8.0.1": {"runtime": {"lib/net8.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.IO.Pipelines/9.0.2": {}, "System.IO.Ports/8.0.0": {"dependencies": {"runtime.native.System.IO.Ports": "8.0.0"}, "runtime": {"lib/net8.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/win/lib/net8.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/*******": {"runtime": {"lib/net9.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Management/8.0.0": {"dependencies": {"System.CodeDom": "8.0.0"}, "runtime": {"lib/net8.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Net.WebSockets.WebSocketProtocol/5.1.0": {"runtime": {"lib/net6.0/System.Net.WebSockets.WebSocketProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "5.100.24.56208"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Private.ServiceModel/4.10.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.ObjectPool": "9.0.2", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "8.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.22.41602"}}, "resources": {"lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Context/8.0.0": {"runtime": {"lib/net8.0/System.Reflection.Context.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Pkcs/8.0.1": {}, "System.Security.Cryptography.ProtectedData/9.0.0": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Security.Cryptography.Xml/8.0.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "8.0.1"}}, "System.Security.Permissions/8.0.0": {"dependencies": {"System.Windows.Extensions": "8.0.0"}, "runtime": {"lib/net8.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Duplex/4.10.0": {"dependencies": {"System.Private.ServiceModel": "4.10.0", "System.ServiceModel.Primitives": "4.10.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.22.41602"}}}, "System.ServiceModel.Http/4.10.0": {"dependencies": {"System.Private.ServiceModel": "4.10.0", "System.ServiceModel.Primitives": "4.10.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.22.41602"}}}, "System.ServiceModel.NetTcp/4.10.0": {"dependencies": {"System.Private.ServiceModel": "4.10.0", "System.ServiceModel.Primitives": "4.10.0"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.22.41602"}}}, "System.ServiceModel.Primitives/4.10.0": {"dependencies": {"System.Private.ServiceModel": "4.10.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.22.41602"}, "lib/net6.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.1000.22.41602"}}}, "System.ServiceModel.Security/4.10.0": {"dependencies": {"System.Private.ServiceModel": "4.10.0", "System.ServiceModel.Primitives": "4.10.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.22.41602"}}}, "System.ServiceModel.Syndication/8.0.0": {"runtime": {"lib/net8.0/System.ServiceModel.Syndication.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.ServiceProcess.ServiceController/8.0.0": {"dependencies": {"System.Diagnostics.EventLog": "9.0.0"}, "runtime": {"lib/net8.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Speech/8.0.0": {"runtime": {"lib/net8.0/System.Speech.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Speech.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/8.0.0": {}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/9.0.0": {}, "System.Threading.AccessControl/8.0.0": {"runtime": {"lib/net8.0/System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Threading.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Threading.Channels/8.0.0": {}, "System.Threading.RateLimiting/8.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Web.Services.Description/4.10.0": {"runtime": {"lib/netstandard2.0/System.Web.Services.Description.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.22.41602"}}, "resources": {"lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll": {"locale": "fr"}}}, "System.Windows.Extensions/8.0.0": {"runtime": {"lib/net8.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Z.EntityFramework.Extensions.EFCore/*********": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"lib/net8.0/Z.EntityFramework.Extensions.EFCore.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Z.EntityFramework.Plus.EFCore/*********": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Z.EntityFramework.Extensions.EFCore": "*********", "Z.Expressions.Eval": "6.2.10"}, "runtime": {"lib/net8.0/Z.EntityFramework.Plus.EFCore.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Z.Expressions.Eval/6.2.10": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "9.0.2", "System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"lib/net8.0/Z.Expressions.Eval.dll": {"assemblyVersion": "6.2.10.0", "fileVersion": "6.2.10.0"}}}, "Application.Infrastructure/1.0.0": {"dependencies": {"BCrypt.Net-Next": "4.0.3", "DocumentFormat.OpenXml": "3.3.0", "ExcelDataReader": "3.7.0", "ExcelDataReader.DataSet": "3.7.0", "Fido2": "3.0.1", "FluentValidation": "11.9.2", "FluentValidation.AspNetCore": "11.3.0", "FluentValidation.DependencyInjectionExtensions": "11.9.2", "MailKit": "4.12.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.2", "Microsoft.Data.SqlClient": "5.2.1", "Microsoft.EntityFrameworkCore": "9.0.2", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Newtonsoft.Json": "13.0.3", "OpenIddict.EntityFrameworkCore": "6.1.1", "System.Linq": "4.3.0", "System.Linq.Dynamic.Core": "*******", "Z.EntityFramework.Plus.EFCore": "*********", "log4net": "2.0.17"}, "runtime": {"Application.Infrastructure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Application.API/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Aspose.Words/25.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-dxonWRQTiAc7FCrw5rSfnDknOM9CbR8HvNZzLkcmdE3Pm04K5n1BMTClPRmVTUDPHGL7syFUKDOdvn0HV8bTeg==", "path": "aspose.words/25.4.0", "hashPath": "aspose.words.25.4.0.nupkg.sha512"}, "Autofac/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tkFxl6wAPuwVhrlN8wuNADnd+k2tv4ReP7ZZSL0vjfcN0RcfC9v25ogxK6b03HC7D4NwWjSLf1G/zTG8Bw43wQ==", "path": "autofac/6.4.0", "hashPath": "autofac.6.4.0.nupkg.sha512"}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.3": {"type": "package", "serviceable": true, "sha512": "sha512-4EsGMAr+oog5UqHs46qwA7S/lJiwpXjPBY3t9tQBmJ8nsgmT/LLnrc32eiTlfOdfKxUz4fxBD2YjSnVZacu97w==", "path": "azure.identity/1.11.3", "hashPath": "azure.identity.1.11.3.nupkg.sha512"}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "BitMiracle.LibTiff.NET/2.4.649": {"type": "package", "serviceable": true, "sha512": "sha512-XCEdfCphKo0UAvSg1h/OzOFWtjkIGnPMAziYkNqV7NVrqb/tZJTBxJhxIEX7gpvQwg8GIRYt133SUncfkKfjLA==", "path": "bitmiracle.libtiff.net/2.4.649", "hashPath": "bitmiracle.libtiff.net.2.4.649.nupkg.sha512"}, "Bold.Licensing/6.2.39": {"type": "package", "serviceable": true, "sha512": "sha512-pUf+iI1jWGVIC+5umNtRw2NMw4i7qeGlZkxTiyVzvX85djlwl2SBtsHtWpDhfKaQltnLsZmZMJNdDArQs4hpCQ==", "path": "bold.licensing/6.2.39", "hashPath": "bold.licensing.6.2.39.nupkg.sha512"}, "BoldReports.Net.Core/6.2.39": {"type": "package", "serviceable": true, "sha512": "sha512-EJDoaByQH+KEI2uMPcInvGfJBLkGYk3UOPfAF3E5sBeFZM//apNT0MXwitRkwSIzrtMdPdE76yCB2qA2lFuMSQ==", "path": "boldreports.net.core/6.2.39", "hashPath": "boldreports.net.core.6.2.39.nupkg.sha512"}, "BouncyCastle.Cryptography/2.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-zy8TMeTP+1FH2NrLaNZtdRbBdq7u5MI+NFZQOBSM69u5RFkciinwzV2eveY6Kjf5MzgsYvvl6kTStsj3JrXqkg==", "path": "bouncycastle.cryptography/2.5.1", "hashPath": "bouncycastle.cryptography.2.5.1.nupkg.sha512"}, "ClosedXML/0.104.1": {"type": "package", "serviceable": true, "sha512": "sha512-RVm2fUNWJlBJlg07shrfeWzrHPG5ypI/vARqdUOUbUdaog8yBw8l4IbCHf2MXt0AXtzaZqGNqhFaCAHigCBdfw==", "path": "closedxml/0.104.1", "hashPath": "closedxml.0.104.1.nupkg.sha512"}, "ClosedXML.Parser/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-w+/0tsxABS3lkSH8EUlA7IGme+mq5T/Puf3DbOiTckmSuUpAUO2LK29oXYByCcWkBv6wcRHxgWlQb1lxkwI0Tw==", "path": "closedxml.parser/1.2.0", "hashPath": "closedxml.parser.1.2.0.nupkg.sha512"}, "DocumentFormat.OpenXml/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JogRPJNiE6kKvbuCqVRX691pPWeGMqdQgjrUwRYkdpfkMmtElfqAgcRR73geYj7OtBeEpstldZXXzJw27LUI9w==", "path": "documentformat.openxml/3.3.0", "hashPath": "documentformat.openxml.3.3.0.nupkg.sha512"}, "DocumentFormat.OpenXml.Framework/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-R5CLzEoeyr7XDB7g3NTxRobcU19agaxVAhGZm+fZUShJGiU4bw8oUgnA2BNFepigJckfFMayOBMAbV3kDXNInA==", "path": "documentformat.openxml.framework/3.3.0", "hashPath": "documentformat.openxml.framework.3.3.0.nupkg.sha512"}, "EPPlus/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-qHJurPvgWoheHyyam53NV8d2CiOO2q88Rg/Lk0wSYwi/aoGDtzYihTMCHeTwGM9zHZnnI3aVLu482SODN+HB4g==", "path": "epplus/7.5.2", "hashPath": "epplus.7.5.2.nupkg.sha512"}, "EPPlus.Interfaces/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-mGLKgdIKkXRYIu+HIGmZUngVAAlPzIQgI/KqG10m6P5P2112l6p/5dDa35UHu4GV4Qevw0Pq9PxAymrrrl4tzA==", "path": "epplus.interfaces/7.5.0", "hashPath": "epplus.interfaces.7.5.0.nupkg.sha512"}, "EPPlus.System.Drawing/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cgwstM12foFisisURUyxwJOWHMD/rZxPSyBXFsCOFayaKq0oKlOs1mCTueKNNIlpPDG1no9vcaQiJgZXFM4KPA==", "path": "epplus.system.drawing/7.5.0", "hashPath": "epplus.system.drawing.7.5.0.nupkg.sha512"}, "ExcelDataReader/3.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AMv3oDETRHSRyXC17rBtKH45qIfFyo433LMeaMB3u4RNr/c9Luuc0Z+JMP6+3Cx9n4wXqFqcrEIVxrf/GgYnZg==", "path": "exceldatareader/3.7.0", "hashPath": "exceldatareader.3.7.0.nupkg.sha512"}, "ExcelDataReader.DataSet/3.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-zA2/CVzbMspkNg0qf0/Zp+eU6VxYP5PtiJSErLDP46d/Y7F6of/NCcSGeXjs97KDq7UiEf6XJe+89s/92n2GYg==", "path": "exceldatareader.dataset/3.7.0", "hashPath": "exceldatareader.dataset.3.7.0.nupkg.sha512"}, "ExcelNumberFormat/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "path": "excelnumberformat/1.1.0", "hashPath": "excelnumberformat.1.1.0.nupkg.sha512"}, "Fido2/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-S0Bz1vfcKlO4Jase3AWp5XnQ746psf4oGx5kL+D2A10j1SsjoAOAIIpanSwfi0cEepDHgk1bClcOKY5TjOzGdA==", "path": "fido2/3.0.1", "hashPath": "fido2.3.0.1.nupkg.sha512"}, "Fido2.Models/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-mgjcuGETuYSCUEaZG+jQeeuuEMkDLc4GDJHBvKDdOz6oSOWp5adPdWP4btZx7Pi+9fu4szN3JIjJmby67MaILw==", "path": "fido2.models/3.0.1", "hashPath": "fido2.models.3.0.1.nupkg.sha512"}, "FluentValidation/11.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-XeHp9LvFvu1fsQ/NvDCymV02GOCB1nz7ZUhfpI3uMhCcHTkV1K5bMkv+Nc/kuNYyAsX5+5bcmUanIEMd5QN+Eg==", "path": "fluentvalidation/11.9.2", "hashPath": "fluentvalidation.11.9.2.nupkg.sha512"}, "FluentValidation.AspNetCore/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtFVgKnDFySyBlPS8bZbTKEEwJZnn11rXXJ2SQnjDhZ56rQqybBg9Joq4crRLz3y0QR8WoOq4iE4piV81w/Djg==", "path": "fluentvalidation.aspnetcore/11.3.0", "hashPath": "fluentvalidation.aspnetcore.11.3.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-PMAdnR1fX1c8DyUvu5YvRa0V0JHES8vzXnVX0OSS3z9W/SkuHqcGrtiSOdW1QehG3vZhXsKhqf3wgDzW/OYahA==", "path": "fluentvalidation.dependencyinjectionextensions/11.9.2", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.9.2.nupkg.sha512"}, "libsodium/********": {"type": "package", "serviceable": true, "sha512": "sha512-flArHoVdscSzyV8ZdPV+bqqY2TTFlaN+xZf/vIqsmHI51KVcD/mOdUPaK3n/k/wGKz8dppiktXUqSmf3AXFgig==", "path": "libsodium/********", "hashPath": "libsodium.********.nupkg.sha512"}, "log4net/2.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-qnnDf/ubJzwm2i1xH7nRMjEDoD+ctse7nZDqb+p7L1PvZc6ykpMoEesWr1/9hFqlsbII2v9e8yyQHJhoDQh7ZA==", "path": "log4net/2.0.17", "hashPath": "log4net.2.0.17.nupkg.sha512"}, "MailKit/4.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-rIqJm92qtHvk1hDchsJ95Hy7n46A7imE24ol++ikXBsjf3Bi1qDBu4H91FfY6LrYXJaxRlc2gIIpC8AOJrCbqg==", "path": "mailkit/4.12.1", "hashPath": "mailkit.4.12.1.nupkg.sha512"}, "Microsoft.ApplicationInsights/2.20.0": {"type": "package", "serviceable": true, "sha512": "sha512-mb+EC5j06Msn5HhKrhrsMAst6JxvYUnphQMGY2cixCabgGAO3q79Y8o/p1Zce1Azgd1IVkRKAMzAV4vDCbXOqA==", "path": "microsoft.applicationinsights/2.20.0", "hashPath": "microsoft.applicationinsights.2.20.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ve6uvLwKNRkfnO/QeN9M8eUJ49lCnWv/6/9p6iTEuiI6Rtsz+myaBAjdMzLuTViQY032xbTF5AdZF5BJzJJyXQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-R/ZG9llsAOn/E8WOtg+PyLCB599lxzaIGy/NbwV1xG+smkyBF4w5/AJXNDq6tVw7/qbqvd+4xLAdWQiiVj1DXw==", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.1", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2/aBgLqBXva/+w8pzRNY8ET43Gi+dr1gv/7ySfbsh23lTK6IAgID5MGUEa1hreNIF+0XpW4tX7QwVe70+YvaPg==", "path": "microsoft.aspnetcore.authorization/2.3.0", "hashPath": "microsoft.aspnetcore.authorization.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-vn31uQ1dA1MIV2WNNDOOOm88V5KgR9esfi0LyQ6eVaGq2h0Yw+R29f5A6qUNJt+RccS3qkYayylAy9tP1wV+7Q==", "path": "microsoft.aspnetcore.authorization.policy/2.3.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULFSa+/L+WiAHVlIFHyg0OmHChU9Hx+K+xnt0hbIU5XmT1EGy0pNDx23QAzDtAy9jxQrTG6MX0MdvMeU4D4c7w==", "path": "microsoft.aspnetcore.connections.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.connections.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-c628W4yUQyeZAjagLDWINOVAakvELBhS6WmjBYpoFVxrvBquo0JDmDMkQLe0zGffnaK5JoWBBHhT3dTJ6s06fw==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.2", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-F4iMrER+27B2t4IkUhe7oZfFNPwg0ZRPSSCpA4H4FLAsmiUTN1k9sq2G187I3mUlPaysHh83JrPcMtZYtmKBBQ==", "path": "microsoft.aspnetcore.cryptography.keyderivation/9.0.2", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.9.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-4ivq53W2k6Nj4eez9wc81ytfGj6HR1NaZJCpOrvghJo9zHuQF57PLhPoQH5ItyCpHXnrN/y7yJDUm+TGYzrx0w==", "path": "microsoft.aspnetcore.hosting.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-F5iHx7odAbFKBV1DNPDkFFcVmD5Tk7rk+tYm3LMQxHEFFdjlg5QcYb5XhHAefl5YaaPeG6ad+/ck8kSG3/D6kw==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I9azEG2tZ4DDHAFgv+N38e6Yhttvf+QjE2j2UYyCACE7Swm5/0uoihCMWZ87oOZYeqiEFSxbsfpT71OYHe2tpw==", "path": "microsoft.aspnetcore.http/2.3.0", "hashPath": "microsoft.aspnetcore.http.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-39r9PPrjA6s0blyFv5qarckjNkaHRA5B+3b53ybuGGNTXEj1/DStQJ4NWjFL6QTRQpL9zt7nDyKxZdJOlcnq+Q==", "path": "microsoft.aspnetcore.http.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VYMCOLvdT0y3O9lk4jUuIs8+re7u5+i+ka6ZZ6fIzSJ94c/JeMnAOOg39EB2i4crPXvLoiSdzKWlNPJgTbCZ2g==", "path": "microsoft.aspnetcore.http.connections/1.2.0", "hashPath": "microsoft.aspnetcore.http.connections.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-yUA7eg6kv7Wbz5TCW4PqS5/kYE5VxUIEDvoxjw4p1RwS2LGm84F9fBtM0mD6wrRfiv1NUyJ7WBjn3PWd/ccO+w==", "path": "microsoft.aspnetcore.http.connections.common/1.2.0", "hashPath": "microsoft.aspnetcore.http.connections.common.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-EY2u/wFF5jsYwGXXswfQWrSsFPmiXsniAlUWo3rv/MGYf99ZFsENDnZcQP6W3c/+xQmQXq0NauzQ7jyy+o1LDQ==", "path": "microsoft.aspnetcore.http.extensions/2.3.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-f10WUgcsKqrkmnz6gt8HeZ7kyKjYN30PO7cSic1lPtH7paPtnQqXPOveul/SIPI43PhRD4trttg4ywnrEmmJpA==", "path": "microsoft.aspnetcore.http.features/2.3.0", "hashPath": "microsoft.aspnetcore.http.features.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-pWTIxKXMGmrTLPovVJDmkToemQU1/ovgB4LhKa+VfoZJ8nKYwIoBI/mMM5JOwJpLGX73a6kw7W8wIc69gvZwRQ==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/9.0.2", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.9.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/4UONYoAIeexPoAmbzBPkVGA6KAY7t0BM+1sr0fKss2V1ERCdcM+Llub4X5Ma+LJ60oPp6KzM0e3j+Pp/JHCNw==", "path": "microsoft.aspnetcore.jsonpatch/9.0.0", "hashPath": "microsoft.aspnetcore.jsonpatch.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTFDEmZi3GheCSPrBxzyE63+d5unln2vYldo/nOm1xet/4rpEk2oJYcwpclPQ13E+LZBF9XixkgwYTUwqznlWg==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/9.0.0", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FqUK5j1EOPNuFT7IafltZQ3cakqhSwVzH5ZW1MhZDe4pPXs9sJ2M5jom1Omsu+mwF2tNKKlRAzLRHQTZzbd+6Q==", "path": "microsoft.aspnetcore.openapi/9.0.0", "hashPath": "microsoft.aspnetcore.openapi.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-no5/VC0CAQuT4PK4rp2K5fqwuSfzr2mdB6m1XNfWVhHnwzpRQzKAu9flChiT/JTLKwVI0Vq2MSmSW2OFMDCNXg==", "path": "microsoft.aspnetcore.routing/2.3.0", "hashPath": "microsoft.aspnetcore.routing.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZkFpUrSmp6TocxZLBEX3IBv5dPMbQuMs6L/BPl0WRfn32UVOtNYJQ0bLdh3cL9LMV0rmTW/5R0w8CBYxr0AOUw==", "path": "microsoft.aspnetcore.routing.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XoCcsOTdtBiXyOzUtpbCl0IaqMOYjnr+6dbDxvUCFn7NR6bu7CwrlQ3oQzkltTwDZH0b6VEUN9wZPOYvPHi+Lg==", "path": "microsoft.aspnetcore.signalr/1.2.0", "hashPath": "microsoft.aspnetcore.signalr.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FZeXIaoWqe145ZPdfiptwkw/sP1BX1UD0706GNBwwoaFiKsNbLEl/Trhj2+idlp3qbX1BEwkQesKNxkopVY5Xg==", "path": "microsoft.aspnetcore.signalr.common/1.2.0", "hashPath": "microsoft.aspnetcore.signalr.common.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Core/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-eZTuMkSDw1uwjhLhJbMxgW2Cuyxfn0Kfqm8OBmqvuzE9Qc/VVzh8dGrAp2F9Pk7XKTDHmlhc5RTLcPPAZ5PSZw==", "path": "microsoft.aspnetcore.signalr.core/1.2.0", "hashPath": "microsoft.aspnetcore.signalr.core.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hNvZ7kQxp5Udqd/IFWViU35bUJvi4xnNzjkF28HRvrdrS7JNsIASTvMqArP6HLQUc3j6nlUOeShNhVmgI1wzHg==", "path": "microsoft.aspnetcore.signalr.protocols.json/1.2.0", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebSockets/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+T4zpnVPkIjvvkyhTH3WBJlTfqmTBRozvnMudAUDvcb4e+NrWf52q8BXh52rkCrBgX6Cudf6F/UhZwTowyBtKg==", "path": "microsoft.aspnetcore.websockets/2.3.0", "hashPath": "microsoft.aspnetcore.websockets.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-trbXdWzoAEUVd0PE2yTopkz4kjZaAIA7xUWekd5uBw+7xE8Do/YOVTeb9d9koPTlbtZT539aESJjSLSqD8eYrQ==", "path": "microsoft.aspnetcore.webutilities/2.3.0", "hashPath": "microsoft.aspnetcore.webutilities.2.3.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W8<PERSON>QjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.VisualBasic/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-kfHPh/etcWypMDYfHxgfitgJMhi986OFCICb76RPcA1Toordf6bBYEJytWr2L5CNdkXFWuw5qTkrlsktBav4VA==", "path": "microsoft.codeanalysis.visualbasic/4.8.0", "hashPath": "microsoft.codeanalysis.visualbasic.4.8.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-ojg2XWmih4ubPPtrhRqqXk0SM6wC2ZSTkNNEAlYBhMo4IsRHjLazFc0abzcZCNfw1JyWcqY7vGutWTv8ZaFD9g==", "path": "microsoft.data.sqlclient/5.2.1", "hashPath": "microsoft.data.sqlclient.5.2.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON><PERSON>uybgcpW32y985eOYxSoZ9IiL0UTYQlY0y1Pt1iHAnpZj/dQHREpSpry1RNvk8YjAeoAkWFdem5conqB9zQ==", "path": "microsoft.entityframeworkcore/9.0.2", "hashPath": "microsoft.entityframeworkcore.9.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-oVSjNSIYHsk0N66eqAWgDcyo9etEFbUswbz7SmlYR6nGp05byHrJAYM5N8U2aGWJWJI6WvIC2e4TXJgH6GZ6HQ==", "path": "microsoft.entityframeworkcore.abstractions/9.0.2", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-w4jzX7XI+L3erVGzbHXpx64A3QaLXxqG3f1vPpGYYZGpxOIHkh7e4iLLD7cq4Ng1vjkwzWl5ZJp0Kj/nHsgFYg==", "path": "microsoft.entityframeworkcore.analyzers/9.0.2", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-r7O4N5uaM95InVSGUj7SMOQWN0f1PBF2Y30ow7Jg+pGX5GJCRVd/1fq83lQ50YMyq+EzyHac5o4CDQA2RsjKJQ==", "path": "microsoft.entityframeworkcore.relational/9.0.2", "hashPath": "microsoft.entityframeworkcore.relational.9.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Y7/3kgz6C5kRFeELLZ5VeIeBlxB31x/ywscbN4r1JqTXIy8WWGo0CqzuOxBy4UzaTzpifElAZvv4fyD3ZQK5w==", "path": "microsoft.entityframeworkcore.sqlserver/9.0.0", "hashPath": "microsoft.entityframeworkcore.sqlserver.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GMCX3zybUB22aAADjYPXrWhhd1HNMkcY5EcFAJnXy/4k5pPpJ6TS4VRl37xfrtosNyzbpO2SI7pd2Q5PvggSdg==", "path": "microsoft.extensions.ambientmetadata.application/9.2.0", "hashPath": "microsoft.extensions.ambientmetadata.application.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TwgHEHv1/Q4jiaoCnZkqWl/WvpaHFXtKBKlUAv8OQRxG5yRGa+JDMz479KW5OmqMGOPYXHe+3pvHjgIUG2i1UA==", "path": "microsoft.extensions.apidescription.server/9.0.1", "hashPath": "microsoft.extensions.apidescription.server.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-a7QhA25n+BzSM5r5d7JznfyluMBGI7z3qyLlFviZ1Eiqv6DdiK27sLZdP/rpYirBM6UYAKxu5TbmfhIy13GN9A==", "path": "microsoft.extensions.caching.abstractions/9.0.2", "hashPath": "microsoft.extensions.caching.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-AlEfp0DMz8E1h1Exi8LBrUCNmCYcGDfSM4F/uK1D1cYx/R3w0LVvlmjICqxqXTsy7BEZaCf5leRZY2FuPEiFaw==", "path": "microsoft.extensions.caching.memory/9.0.2", "hashPath": "microsoft.extensions.caching.memory.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Te+N4xphDlGIS90lKJMZyezFiMWKLAtYV2/M8gGJG4thH6xyC7LWhMzgz2+tWMehxwZlBUq2D9DvVpjKBZFTPQ==", "path": "microsoft.extensions.compliance.abstractions/9.2.0", "hashPath": "microsoft.extensions.compliance.abstractions.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-EBZW+u96tApIvNtjymXEIS44tH0I/jNwABHo4c33AchWOiDWCq2rL3klpnIo+xGrxoVGJzPDISV6hZ+a9C9SzQ==", "path": "microsoft.extensions.configuration/9.0.2", "hashPath": "microsoft.extensions.configuration.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-I0O/270E/lUNqbBxlRVjxKOMZyYjP88dpEgQTveml+h2lTzAP4vbawLVwjS9SC7lKaU893bwyyNz0IVJYsm9EA==", "path": "microsoft.extensions.configuration.abstractions/9.0.2", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-krJ04xR0aPXrOf5dkNASg6aJjsdzexvsMRL6UNOUjiTzqBvRr95sJ1owoKEm89bSONQCfZNhHrAFV9ahDqIPIw==", "path": "microsoft.extensions.configuration.binder/9.0.2", "hashPath": "microsoft.extensions.configuration.binder.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EJzSNO9oaAXnTdtdNO6npPRsIIeZCBSNmdQ091VDO7fBiOtJAAeEq6dtrVXIi3ZyjC5XRSAtVvF8SzcneRHqKQ==", "path": "microsoft.extensions.configuration.fileextensions/8.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L89DLNuimOghjV3tLx0ArFDwVEJD6+uGB3BMCMX01kaLzXkaXHb2021xOMl2QOxUxbdePKUZsUY7n2UUkycjRg==", "path": "microsoft.extensions.configuration.json/8.0.1", "hashPath": "microsoft.extensions.configuration.json.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZffbJrskOZ40JTzcTyKwFHS5eACSWp2bUQBBApIgGV+es8RaTD4OxUG7XxFr3RIPLXtYQ1jQzF2DjKB5fZn7Qg==", "path": "microsoft.extensions.dependencyinjection/9.0.2", "hashPath": "microsoft.extensions.dependencyinjection.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-MNe7GSTBf3jQx5vYrXF0NZvn6l7hUKF6J54ENfAgCO8y6xjN1XUmKKWG464LP2ye6QqDiA1dkaWEZBYnhoZzjg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-WcwfTpl3IcPcaahTVEaJwMUg1eWog1SkIA6jQZZFqMXiMX9/tVkhNB6yzUQmBdGWdlWDDRKpOmK7T7x1Uu05pQ==", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.2.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-kwFWk6DPaj1Roc0CExRv+TTwjsiERZA730jQIPlwCcS5tMaCAQtaGfwAK0z8CMFpVTiT+MgKXpd/P50qVCuIgg==", "path": "microsoft.extensions.diagnostics/9.0.2", "hashPath": "microsoft.extensions.diagnostics.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-kFwIZEC/37cwKuEm/nXvjF7A/Myz9O7c7P9Csgz6AOiiDE62zdOG5Bu7VkROu1oMYaX0wgijPJ5LqVt6+JKjVg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.2", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-et5JevHsLv1w1O1Zhb6LiUfai/nmDRzIHnbrZJdzLsIbbMCKTZpeHuANYIppAD//n12KvgOne05j4cu0GhG9gw==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.2.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-IcOBmTlr2jySswU+3x8c3ql87FRwTVPQgVKaV5AXzPT5u0VItfNU8SMbESpdSp5STwxT/1R99WYszgHWsVkzhg==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.2", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3+ZUSpOSmie+o8NnLIRqCxSh65XL/ExU7JYnFOg58awDRlY3lVpZ9A369jkoZL1rpsq7LDhEfkn2ghhGaY1y5Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jGFKZiXs2HNseK3NK/rfwHNNovER71jSj4BD1a/649ml9+h6oEtYd0GSALZDNW8jZ2Rh+oAeadOa6sagYW1F2A==", "path": "microsoft.extensions.filesystemglobbing/9.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-PvjZW6CMdZbPbOwKsQXYN5VPtIWZQqdTRuBPZiW3skhU3hymB17XSlLVC4uaBbDZU+/3eHG3p80y+MzZxZqR7Q==", "path": "microsoft.extensions.hosting.abstractions/9.0.2", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-34+kcwxPZr3Owk9eZx268+gqGNB8G/8Y96gZHomxam0IOH08FhPBjPrLWDtKdVn4+sVUUJnJMpECSTJi4XXCcg==", "path": "microsoft.extensions.http/9.0.2", "hashPath": "microsoft.extensions.http.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Eeup1LuD5hVk5SsKAuX1D7I9sF380MjrNG10IaaauRLOmrRg8rq2TA8PYTXVBXf3MLkZ6m2xpBqRbZdxf8ygkg==", "path": "microsoft.extensions.http.diagnostics/9.2.0", "hashPath": "microsoft.extensions.http.diagnostics.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-2y5a9Iijc9iTUN1M7rH2+kUMJPuuxTgfUyL9iAOqe4ueuWtTfG1SVX/oAj35q46OV4kSgCeJC82dLQ96xOo/RQ==", "path": "microsoft.extensions.http.polly/9.0.2", "hashPath": "microsoft.extensions.http.polly.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Km+YyCuk1IaeOsAzPDygtgsUOh3Fi89hpA18si0tFJmpSBf9aKzP9ffV5j7YOoVDvRWirpumXAPQzk1inBsvKw==", "path": "microsoft.extensions.http.resilience/9.2.0", "hashPath": "microsoft.extensions.http.resilience.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-rD3WoEPV4TJ4sb/SLaf7GHwSv8iZhIpviigAxloYivrPTaN8MbOLhSZMn53LtVXFugTQzkBlGSOcB99vJdk5pA==", "path": "microsoft.extensions.identity.core/9.0.2", "hashPath": "microsoft.extensions.identity.core.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-1gY/SvkkvMv/Ea/UUMpTmkMI8ZP+I3dLBiPMSaaSaDK7WclHifheUmmsJqnXrhsswAI0xLLbGwff4xcsXedt4A==", "path": "microsoft.extensions.identity.stores/9.0.2", "hashPath": "microsoft.extensions.identity.stores.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-loV/0UNpt2bD+6kCDzFALVE63CDtqzPeC0LAetkdhiEr/tTNbvOlQ7CBResH7BQBd3cikrwiBfaHdyHMFUlc2g==", "path": "microsoft.extensions.logging/9.0.2", "hashPath": "microsoft.extensions.logging.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dV9s2Lamc8jSaqhl2BQSPn/AryDIH2sSbQUyLitLXV0ROmsb+SROnn2cH939JFbsNrnf3mIM3GNRKT7P0ldwLg==", "path": "microsoft.extensions.logging.abstractions/9.0.2", "hashPath": "microsoft.extensions.logging.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-pnwYZE7U6d3Y6iMVqADOAUUMMBGYAQPsT3fMwVr/V1Wdpe5DuVGFcViZavUthSJ5724NmelIl1cYy+kRfKfRPQ==", "path": "microsoft.extensions.logging.configuration/9.0.2", "hashPath": "microsoft.extensions.logging.configuration.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nWx7uY6lfkmtpyC2dGc0IxtrZZs/LnLCQHw3YYQucbqWj8a27U/dZ+eh72O3ZiolqLzzLkVzoC+w/M8dZwxRTw==", "path": "microsoft.extensions.objectpool/9.0.2", "hashPath": "microsoft.extensions.objectpool.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-zr98z+AN8+isdmDmQRuEJ/DAKZGUTHmdv3t0ZzjHvNqvA44nAgkXE9kYtfoN6581iALChhVaSw2Owt+Z2lVbkQ==", "path": "microsoft.extensions.options/9.0.2", "hashPath": "microsoft.extensions.options.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-OPm1NXdMg4Kb4Kz+YHdbBQfekh7MqQZ7liZ5dYUd+IbJakinv9Fl7Ck6Strbgs0a6E76UGbP/jHR532K/7/feQ==", "path": "microsoft.extensions.options.configurationextensions/9.0.2", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-puBMtKe/wLuYa7H6docBkLlfec+h8L35DXqsDKKJgW0WY5oCwJ3cBJKcDaZchv6knAyqOMfsl6VUbaR++E5LXA==", "path": "microsoft.extensions.primitives/9.0.2", "hashPath": "microsoft.extensions.primitives.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Resilience/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-dyaM+Jeznh/i21bOrrRs3xceFfn0571EOjOq95dRXmL1rHDLC4ExhACJ2xipRBP6g1AgRNqmryi+hMrVWWgmlg==", "path": "microsoft.extensions.resilience/9.2.0", "hashPath": "microsoft.extensions.resilience.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-4+bw7W4RrAMrND9TxonnSmzJOdXiPxljoda8OPJiReIN607mKCc0t0Mf28sHNsTujO1XQw28wsI0poxeeQxohw==", "path": "microsoft.extensions.telemetry/9.2.0", "hashPath": "microsoft.extensions.telemetry.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-kEl+5G3RqS20XaEhHh/nOugcjKEK+rgVtMJra1iuwNzdzQXElelf3vu8TugcT7rIZ/T4T76EKW1OX/fmlxz4hw==", "path": "microsoft.extensions.telemetry.abstractions/9.2.0", "hashPath": "microsoft.extensions.telemetry.abstractions.9.2.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.60.3": {"type": "package", "serviceable": true, "sha512": "sha512-jve1RzmSpBhGlqMzPva6VfRbLMLZZc1Q8WRVZf8+iEruQkBgDTJPq8OeTehcY4GGYG1j6UB1xVofVE+n4BLDdw==", "path": "microsoft.identity.client/4.60.3", "hashPath": "microsoft.identity.client.4.60.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.60.3": {"type": "package", "serviceable": true, "sha512": "sha512-X1Cz14/RbmlLshusE5u2zfG+5ul6ttgou19BZe5Mdw1qm6fgOI9/imBB2TIsx2UD7nkgd2+MCSzhbukZf7udeg==", "path": "microsoft.identity.client.extensions.msal/4.60.3", "hashPath": "microsoft.identity.client.extensions.msal.4.60.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-OQd5aVepYvh5evOmBMeAYjMIpEcTf1ZCBZaU7Nh/RlhhdXefjFDJeP1L2F2zeNT1unFr+wUu/h3Ac2Xb4BXU6w==", "path": "microsoft.identitymodel.abstractions/8.7.0", "hashPath": "microsoft.identitymodel.abstractions.8.7.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-uzsSAWhNhbrkWbQKBTE8QhzviU6sr3bJ1Bkv7gERlhswfSKOp7HsxTRLTPBpx/whQ/GRRHEwMg8leRIPbMrOgw==", "path": "microsoft.identitymodel.jsonwebtokens/8.7.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.7.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bs0TznPAu+nxa9rAVHJ+j3CYECHJkT3tG8AyBfhFYlT5ldsDhoxFT7J+PKxJHLf+ayqWfvDZHHc4639W2FQCxA==", "path": "microsoft.identitymodel.logging/8.7.0", "hashPath": "microsoft.identitymodel.logging.8.7.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-4r4H8LCCoNFlJHRrWCqaNtzJPM4Bfi9ARdl7Gd+OoIZqc1rsp9z60USIf00o5YwAwXwKffPUPrvufkbgR69jYA==", "path": "microsoft.identitymodel.protocols/8.7.0", "hashPath": "microsoft.identitymodel.protocols.8.7.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-nUifCAs2E9cvBiYBC3/L9PoftSxTVpdUdoIu7VV9M9aw7mogsdFRUn5v23c5Jl9u93jdUc0PCagrItLHncG8Qg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.7.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.7.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-5Z6voXjRXAnGklhmZd1mKz89UhcF5ZQQZaZc2iKrOuL4Li1UihG2vlJx8IbiFAOIxy/xdbsAm0A+WZEaH5fxng==", "path": "microsoft.identitymodel.tokens/8.7.0", "hashPath": "microsoft.identitymodel.tokens.8.7.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/M0wVg6tJUOHutWD3BMOUVZAioJVXe0tCpFiovzv0T9T12TBf4MnaHP0efO8TCr1a6O9RZgQeZ9Gdark8L9XdA==", "path": "microsoft.net.http.headers/2.3.0", "hashPath": "microsoft.net.http.headers.2.3.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.17": {"type": "package", "serviceable": true, "sha512": "sha512-Le+kehlmrlQfuDFUt1zZ2dVwrhFQtKREdKBo+rexOwaCoYP0/qpgT9tLxCsZjsgR5Itk1UKPcbgO+FyaNid/bA==", "path": "microsoft.openapi/1.6.17", "hashPath": "microsoft.openapi.1.6.17.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry.AccessControl/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-u8PB9/v02C8mBXzl0vJ7bOyC020zOP+T1mRct+KA46DqZkB40XtsNn9pGD0QowTRsT6R4jPCghn+yAODn2UMMw==", "path": "microsoft.win32.registry.accesscontrol/8.0.0", "hashPath": "microsoft.win32.registry.accesscontrol.8.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "Microsoft.Windows.Compatibility/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4hIR9/0mJe1YWCPJ8ChPf6V+R0SjxF5Uj5RhviRoUk3gri0hQeRrdeGFGjJjAKQRu1OgpF2GnbHIwBTRvZGUuQ==", "path": "microsoft.windows.compatibility/8.0.0", "hashPath": "microsoft.windows.compatibility.8.0.0.nupkg.sha512"}, "MimeKit/4.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-PFUHfs6BZxKYM/QPJksAwXphbJf0SEfdSfsoQ6p6yvFRaJPofFJMBiotWhFRrdSUzfp6C6K49EjBIqIwZ2TJqA==", "path": "mimekit/4.12.0", "hashPath": "mimekit.4.12.0.nupkg.sha512"}, "NetEscapades.AspNetCore.SecurityHeaders/1.0.0-preview.3": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON><PERSON>jkanxopAnq4VZBgMP0OaVCehLNnTy/vud3VjUYpc9IKHOGSLZn4gDOirQ69BC5xUynsVrJ0iJjHjMC4GeLA==", "path": "netescapades.aspnetcore.securityheaders/1.0.0-preview.3", "hashPath": "netescapades.aspnetcore.securityheaders.1.0.0-preview.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "NSec.Cryptography/22.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-lEntcPYd7h3aZ8xxi/y/4TML7o8w0GEGqd+w4L1omqFLbdCBmhxJAeO2YBmv/fXbJKgKCQLm7+TD4bR605PEUQ==", "path": "nsec.cryptography/22.4.0", "hashPath": "nsec.cryptography.22.4.0.nupkg.sha512"}, "OpenIddict.Abstractions/6.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-eyXihbSmFeQ0B7wk1owg+vHqkay16L3K2MVXb8SCW0HMGrSrizrmNFDAak95uj/Tzi2XXgTaQpYrO/leQF1bJQ==", "path": "openiddict.abstractions/6.3.0", "hashPath": "openiddict.abstractions.6.3.0.nupkg.sha512"}, "OpenIddict.Core/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-mE+AnsnROxI9jnIwXBg76/+s3slPayHie9A4PR5I2MOASi/CIqg248iNBRdB1ALHSyiULDO88/c/j4G2YOjFrw==", "path": "openiddict.core/6.1.1", "hashPath": "openiddict.core.6.1.1.nupkg.sha512"}, "OpenIddict.EntityFrameworkCore/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-y+nNH9K4hlsnwh48MWcr9wN7gS+xAOh9c3ZdBnjLa5gtQq8dT0T3kanpDWLMBOyvVi5UbRNgVIxvRpUp3RT2oA==", "path": "openiddict.entityframeworkcore/6.1.1", "hashPath": "openiddict.entityframeworkcore.6.1.1.nupkg.sha512"}, "OpenIddict.EntityFrameworkCore.Models/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-QiwyNqXO0HElp3lCjHquV1bUR8MlYOEoVGd+H0XILBPXdcKBcrHLPiRTGjgDzTQmwWk6XSHmD6e7YnGhhtKpSg==", "path": "openiddict.entityframeworkcore.models/6.1.1", "hashPath": "openiddict.entityframeworkcore.models.6.1.1.nupkg.sha512"}, "OpenIddict.Server/6.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PAhRI+4eVrGuA3HPkqghjsbqzq/p7FdPoo1ggbUvhaP6hjZ5x6lUIyUinQB1TymmzoY93mUEOISQV3D+yzOuZQ==", "path": "openiddict.server/6.3.0", "hashPath": "openiddict.server.6.3.0.nupkg.sha512"}, "OpenIddict.Server.AspNetCore/6.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KY17nH6K64zEwQCPl6gLB3TU85FaPhq+StjBXkNcQBN4UVdOni7NZ1Qpnvq9/WsgGuClTsLNOG3MT6oBygDnFQ==", "path": "openiddict.server.aspnetcore/6.3.0", "hashPath": "openiddict.server.aspnetcore.6.3.0.nupkg.sha512"}, "OpenIddict.Validation/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-EswfSriq61xV4wru48RLCunSOCIULoTUqFcEG6JEstnWQoptcuppUfEWpbnpvBFO3htU9WCC9ybZXEUIvNEkAQ==", "path": "openiddict.validation/6.1.1", "hashPath": "openiddict.validation.6.1.1.nupkg.sha512"}, "OpenIddict.Validation.AspNetCore/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-t6bjzcQUI80ViTlJ8GZgh7NoTuDRZnYOKoR3HoN+z8oqsvdaDPlxtODehGBIBDm+RvfwD2FUQQWLa1+uYNGPCw==", "path": "openiddict.validation.aspnetcore/6.1.1", "hashPath": "openiddict.validation.aspnetcore.6.1.1.nupkg.sha512"}, "OpenIddict.Validation.SystemNetHttp/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ShwUslQA5wFyd28BkH3LBQTU6Veow4Abl7TdhUhW3VdiHqS4aGVJSiUoMZnJljxDx8DA/w5UD8WO2S+6GTfXYw==", "path": "openiddict.validation.systemnethttp/6.1.1", "hashPath": "openiddict.validation.systemnethttp.6.1.1.nupkg.sha512"}, "Polly/7.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-bw00Ck5sh6ekduDE3mnCo1ohzuad946uslCDEENu3091+6UKnBuKLo4e+yaNcCzXxOZCXWY2gV4a35+K1d4LDA==", "path": "polly/7.2.4", "hashPath": "polly.7.2.4.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "Polly.Extensions/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "path": "polly.extensions/8.4.2", "hashPath": "polly.extensions.8.4.2.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}, "QuestPDF/2025.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-5x6P87aXvzrBbGn3LIw8I96FuMhEpL59heYyxQ4S/2vHxjVzS9EKwxuGSoa57gYH6TtScY/4427yQOQxP2ghUg==", "path": "questpdf/2025.4.2", "hashPath": "questpdf.2025.4.2.nupkg.sha512"}, "RBush/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ijGh9N0zZ7JfXk3oQkWCwK8SwSSByexbyh/MjbCjNxOft9eG5ZqKC1vdgiYq78h4IZRFmN4s3JZ/b10Jipud5w==", "path": "rbush/3.2.0", "hashPath": "rbush.3.2.0.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gK720fg6HemDg8sXcfy+xCMZ9+hF78Gc7BmREbmkS4noqlu1BAr9qZtuWGhLzFjBfgecmdtl4+SYVwJ1VneZBQ==", "path": "runtime.linux-arm.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-KYG6/3ojhEWbb3FwQAKgGWPHrY+HKUXXdVjJlrtyCLn3EMcNTaNcPadb2c0ndQzixZSmAxZKopXJr0nLwhOrpQ==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wnw5vhA4mgGbIFoo6l9Fk3iEcwRSq49a1aKwJgXUCUtEQLCSUDjTGSxqy/oMUuOyyn7uLHsH8KgZzQ1y3lReiQ==", "path": "runtime.linux-x64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ee7Sz5llLpTgyKIWzKI/GeuRSbFkOABgJRY00SqTY0OkTYtkB+9l5rFZfE7fxPA3c22RfytCBYkUdAkcmwMjQg==", "path": "runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rbUBLAaFW9oVkbsb0+XSrAo2QdhBeAyzLl5KQ6Oci9L/u626uXGKInsVJG6B9Z5EO8bmplC8tsMiaHK8wOBZ+w==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IcfB4jKtM9pkzP9OpYelEcUX1MiDt0IJPBh3XYYdEISFF+6Mc+T8WWi0dr9wVh1gtcdVjubVEIBgB8BHESlGfQ==", "path": "runtime.osx-x64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Serilog/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gmoWVOvKgbME8TYR+gwMf7osROiWAURterc6Rt2dQyX7wtjZYpqFiA/pY6ztjGQKKV62GGCyOcmtP1UKMHgSmA==", "path": "serilog/4.2.0", "hashPath": "serilog.4.2.0.nupkg.sha512"}, "Serilog.AspNetCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "path": "serilog.aspnetcore/9.0.0", "hashPath": "serilog.aspnetcore.9.0.0.nupkg.sha512"}, "Serilog.Enrichers.Environment/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9BqCE4C9FF+/rJb/CsQwe7oVf44xqkOvMwX//CUxvUR25lFL4tSS6iuxE5eW07quby1BAyAEP+vM6TWsnT3iqw==", "path": "serilog.enrichers.environment/3.0.1", "hashPath": "serilog.enrichers.environment.3.0.1.nupkg.sha512"}, "Serilog.Enrichers.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C7BK25a1rhUyr+Tp+1BYcVlBJq7M2VCHlIgnwoIUVJcicM9jYcvQK18+OeHiXw7uLPSjqWxJIp1EfaZ/RGmEwA==", "path": "serilog.enrichers.thread/4.0.0", "hashPath": "serilog.enrichers.thread.4.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "path": "serilog.extensions.hosting/9.0.0", "hashPath": "serilog.extensions.hosting.9.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "path": "serilog.extensions.logging/9.0.0", "hashPath": "serilog.extensions.logging.9.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "path": "serilog.formatting.compact/3.0.0", "hashPath": "serilog.formatting.compact.3.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "path": "serilog.settings.configuration/9.0.0", "hashPath": "serilog.settings.configuration.9.0.0.nupkg.sha512"}, "Serilog.Sinks.ApplicationInsights/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AlYq1JFqh+RFKwLKZ3X224Zbe1gnkMbqSELp2FApLN0iMyRPdwwxMJBCCrk49C8qOefBd4zN+J/1Tq3i75DunA==", "path": "serilog.sinks.applicationinsights/4.0.0", "hashPath": "serilog.sinks.applicationinsights.4.0.0.nupkg.sha512"}, "Serilog.Sinks.Async/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-SnmRknWsSMgyo9wDXeZZCqSp48kkQYy44taSM6vcpxfiRICzSf09oLKEmVr0RCwQnfd8mJQ2WNN6nvhqf0RowQ==", "path": "serilog.sinks.async/2.1.0", "hashPath": "serilog.sinks.async.2.1.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "path": "serilog.sinks.debug/3.0.0", "hashPath": "serilog.sinks.debug.3.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "SixLabors.Fonts/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "path": "sixlabors.fonts/1.0.0", "hashPath": "sixlabors.fonts.1.0.0.nupkg.sha512"}, "SkiaSharp/3.116.1": {"type": "package", "serviceable": true, "sha512": "sha512-DNDwbRjP+aMo27dV2h/uHCVTcWubWWxHnPLiePNyl24f4Pv43mQ8AQQeseOrKR+J3AOCEs6t0sUjo0aa3j3RWQ==", "path": "skiasharp/3.116.1", "hashPath": "skiasharp.3.116.1.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-iQcOUE0tPZvBUxOdZaP3LIdAC21H8BEMhDvpCQ/mUUvbKGLd5rF7veJVSZBNu20SuCC0oZpEdGxB+mLVOK8uzw==", "path": "skiasharp.nativeassets.linux/2.88.6", "hashPath": "skiasharp.nativeassets.linux.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/3.116.1": {"type": "package", "serviceable": true, "sha512": "sha512-3KPvpKysDmEMt0NnAZPX5U6KFk0LmG/72/IjAIJemIksIZ0Tjs9pGpr3L+zboVCv1MLVoJLKl3nJDXUG6Jda6A==", "path": "skiasharp.nativeassets.macos/3.116.1", "hashPath": "skiasharp.nativeassets.macos.3.116.1.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/3.116.1": {"type": "package", "serviceable": true, "sha512": "sha512-dRQ75MCI8oz6zAs2Y1w6pq6ARs4MhdNG+gf3doOxOxdnueDXffQLGQIxON54GDoxc0WjKOoHMKBR4DhaduwwQw==", "path": "skiasharp.nativeassets.win32/3.116.1", "hashPath": "skiasharp.nativeassets.win32.3.116.1.nupkg.sha512"}, "SkiaSharp.Svg/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-rTneyM6mt0w2xCCampFlkO3mGFIwGEJo7L2X3JOdyeMCZDQkyqv7m9kZfA89kLSErXErFnY72TZsryzqTIiT7g==", "path": "skiasharp.svg/1.60.0", "hashPath": "skiasharp.svg.1.60.0.nupkg.sha512"}, "SSH.NET/2020.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-G0dNlTBAM00KZXv1wWVwgg26d9/METcM6qWBpNQwllzQmmbu+Zu+FS1L1X4fFgGdPu3e8k9mmTBu6SwtQ0614g==", "path": "ssh.net/2020.0.2", "hashPath": "ssh.net.2020.0.2.nupkg.sha512"}, "SshNet.Security.Cryptography/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5pBIXRjcSO/amY8WztpmNOhaaCNHY/B6CcYDI7FSTgqSyo/ZUojlLiKcsl+YGbxQuLX439qIkMfP0PHqxqJi/Q==", "path": "sshnet.security.cryptography/1.3.0", "hashPath": "sshnet.security.cryptography.1.3.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-nrkZMoflCvTKj2yckJ1k/tQu53IRGHYkKp7ESiQETkAaUXzn05CYlCl/DY9kBBbFqefHWIwC/9VmYisD44jqlA==", "path": "swashbuckle.aspnetcore/6.7.0", "hashPath": "swashbuckle.aspnetcore.6.7.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-qI1ntGtNnt1Nksi/oDNkmCULVrImHyLWodJhQzghGj9W6uKYMqVl8Y7M2oU8VbHTQZFImD2kYR9ay8LZkJwaqA==", "path": "swashbuckle.aspnetcore.swagger/6.7.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.7.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2eRUBYnMBWzyl0esJfcB4YHuG/boD6nmuDcKdRa+nYPU/57+kbZ3Ot4TGpRDTJcZ0BrWoQiYe5mWRy6whvTkQ==", "path": "swashbuckle.aspnetcore.swaggergen/6.7.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.7.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hgrXeKzyp5OGN8qVvL7A+vhmU7mDJTfGpiMBRL66IcfLOyna8UTLtn3cC3CghamXpRDufcc9ciklTszUGEQK0w==", "path": "swashbuckle.aspnetcore.swaggerui/7.2.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.7.2.0.nupkg.sha512"}, "Syncfusion.Compression.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-ULVmApIZUDthh5jbAjdDPO7OvHfiC5es+OsLL1QQ+Fk1WfDQQxwOgo7pOFe5SQVz+9/AdVAuMIwZSNa1bD9Ksg==", "path": "syncfusion.compression.net.core/26.1.35", "hashPath": "syncfusion.compression.net.core.26.1.35.nupkg.sha512"}, "Syncfusion.DocIO.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-SOtVARxTyLu1oUjmJS6i+QpV5LNkZ899tjhNOpHsMboTafYi3+7RCdQyA0IDpavXoRU8G3EyS+RcxTjD56FsiQ==", "path": "syncfusion.docio.net.core/26.1.35", "hashPath": "syncfusion.docio.net.core.26.1.35.nupkg.sha512"}, "Syncfusion.Licensing/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-DPMS7ODbzaS+S2//uOMW8XL/08AEIMz/TEb113gaXdwga328ozz+4wiQGZBJzjLGglyO1vV+GTUigquDGHWcFg==", "path": "syncfusion.licensing/26.1.35", "hashPath": "syncfusion.licensing.26.1.35.nupkg.sha512"}, "Syncfusion.OfficeChart.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-M8rLhvfpvG4t+v1kGWv+TtPkmGWzRdJHvXFqSCOss5zlPP+Mo2jMRX7yGFtsfJMCgOjHLdr1BQP8UI94htoKGg==", "path": "syncfusion.officechart.net.core/26.1.35", "hashPath": "syncfusion.officechart.net.core.26.1.35.nupkg.sha512"}, "Syncfusion.Pdf.Imaging.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-M4Ltbw/p/77/FjRJQ1O1VZvX4kp8QSDYIqDz9jhy/P4eRIl+Di1Kh1L558vjLRJbIShIKZAvjFUzs6MLkaJtAQ==", "path": "syncfusion.pdf.imaging.net.core/26.1.35", "hashPath": "syncfusion.pdf.imaging.net.core.26.1.35.nupkg.sha512"}, "Syncfusion.Pdf.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-s8gT52vdI/yrcNkW2ucqoUVHUTwTkr/PvxAxWNKzx33I2FbZoJ9FIBS2z1wLMY5otRfEWu6O52zFtIQwB+U6Eg==", "path": "syncfusion.pdf.net.core/26.1.35", "hashPath": "syncfusion.pdf.net.core.26.1.35.nupkg.sha512"}, "Syncfusion.Presentation.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-BXmz+LHcYWj0V4ozMRRdzvpPei0p4IHuyKFmLaKIXguQW9fV6s+K9Pq9cDsfuQodCGCTxHpV0zfKqbk4b7D3xA==", "path": "syncfusion.presentation.net.core/26.1.35", "hashPath": "syncfusion.presentation.net.core.26.1.35.nupkg.sha512"}, "Syncfusion.XlsIO.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-oeQF2qcNjUnC0aJYHQF73tPW8Iuk/FcHrPTaWCyQjNnvBrWRTh+n6dGxM0dXaa1bp3ZPBanzg9/qMreZbbLFZA==", "path": "syncfusion.xlsio.net.core/26.1.35", "hashPath": "syncfusion.xlsio.net.core.26.1.35.nupkg.sha512"}, "System.Buffers/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-lN6tZi7Q46zFzAbRYXTIvfXcyvQQgxnY7Xm6C6xQ9784dEL1amjM6S6Iw4ZpsvesAKnRVsM4scrDQaDqSClkjA==", "path": "system.buffers/4.6.0", "hashPath": "system.buffers.4.6.0.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "path": "system.codedom/8.0.0", "hashPath": "system.codedom.8.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.ComponentModel.Composition/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bGhUX5BTivJ9Wax0qnJy7uGq7dn/TQkEpJ2Fpu1etg8dbPwyDkUzNPc1d3I2/jUr9y4wDI3a1dkSmi8X21Pzbw==", "path": "system.componentmodel.composition/8.0.0", "hashPath": "system.componentmodel.composition.8.0.0.nupkg.sha512"}, "System.ComponentModel.Composition.Registration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BVMXYqX7Z0Zdq3tc94UKJL/cOWq4LF3ufexfdPuUDrDl4ekbbfwPVzsusVbx+aq6Yx60CJnmJLyHtM3V2Q7BBQ==", "path": "system.componentmodel.composition.registration/8.0.0", "hashPath": "system.componentmodel.composition.registration.8.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PdkuMrwDhXoKFo/JxISIi9E8L+QGn9Iquj2OKDWHB6Y/HnUOuBouF7uS3R4Hw3FoNmwwMo6hWgazQdyHIIs27A==", "path": "system.configuration.configurationmanager/9.0.0", "hashPath": "system.configuration.configurationmanager.9.0.0.nupkg.sha512"}, "System.Data.Odbc/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c+GfnZt2/HyU+voKw2fctLZClcNjPZPWS+mnIhGvDknRMqL/fwWlREWPgA4csbp9ZkQIgB4qkufgdh/oh5Ubow==", "path": "system.data.odbc/8.0.0", "hashPath": "system.data.odbc.8.0.0.nupkg.sha512"}, "System.Data.OleDb/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FpUTcQ0E8mFvcYp8UZA3NX8wgmhmsCue56g1zfkr1xdOnT5FrYYmC5DWQ9xCw8o8zuxVBKLZvliqEGgmeoalaQ==", "path": "system.data.oledb/8.0.0", "hashPath": "system.data.oledb.8.0.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.5": {"type": "package", "serviceable": true, "sha512": "sha512-fRqxut4lrndPHrXD+ht1XRmCL3obuKldm4XjCRYS9p5f7FSR7shBxAwTkDrpFMsHC9BhNgjjmUtiIjvehn5zkg==", "path": "system.data.sqlclient/4.8.5", "hashPath": "system.data.sqlclient.4.8.5.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qd01+AqPhbAG14KtdtIqFk+cxHQFZ/oqRSCoxU1F+Q6Kv0cl726sl7RzU9yLFGd4BUOKdN4XojXF0pQf/R6YeA==", "path": "system.diagnostics.eventlog/9.0.0", "hashPath": "system.diagnostics.eventlog.9.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lX6DXxtJqVGWw7N/QmVoiCyVQ+Q/Xp+jVXPr3gLK1jJExSn1qmAjJQeb8gnOYeeBTG3E3PmG1nu92eYj/TEjpg==", "path": "system.diagnostics.performancecounter/8.0.0", "hashPath": "system.diagnostics.performancecounter.8.0.0.nupkg.sha512"}, "System.DirectoryServices/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7nit//efUTy1OsAKco2f02PMrwsR2S234N0dVVp84udC77YcvpOQDz5znAWMtgMWBzY1aRJvUW61jo/7vQRfXg==", "path": "system.directoryservices/8.0.0", "hashPath": "system.directoryservices.8.0.0.nupkg.sha512"}, "System.DirectoryServices.AccountManagement/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dCT8BYeeisx0IzAf6x+FSVWK3gz2fKI9pgLV16c7dY/lckw4aodNrgXqsFqyqJN5Kfxc3oklG+SCMYkRfg1V7A==", "path": "system.directoryservices.accountmanagement/8.0.0", "hashPath": "system.directoryservices.accountmanagement.8.0.0.nupkg.sha512"}, "System.DirectoryServices.Protocols/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-puwJxURHDrYLGTQdsHyeMS72ClTqYa4lDYz6LHSbkZEk5hq8H8JfsO4MyYhB5BMMxg93jsQzLUwrnCumj11UIg==", "path": "system.directoryservices.protocols/8.0.0", "hashPath": "system.directoryservices.protocols.8.0.0.nupkg.sha512"}, "System.Drawing.Common/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-3G4xpa8mUYGzEF0HlswlBArAFywHJIzsZoB5hU4yMlnYHaabj/lg019BwbyyYBxj0aoM7Cz+jdlgUemeno9LOQ==", "path": "system.drawing.common/8.0.4", "hashPath": "system.drawing.common.8.0.4.nupkg.sha512"}, "System.Formats.Asn1/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VRDjgfqV0hCma5HBQa46nZTRuqfYMWZClwxUtvLJVTCeDp9Esdvr91AfEWP98IMO8ooSv1yXb6/oCc6jApoXvQ==", "path": "system.formats.asn1/9.0.0", "hashPath": "system.formats.asn1.9.0.0.nupkg.sha512"}, "System.Formats.Cbor/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mGaLOoiw7KurJagOOcIsWUoCT5ACIiGxKlCcbYQASefBGXjnCcKTq5Hdjb94eEAKg38zXKlHw4c6EjzgBl9dIw==", "path": "system.formats.cbor/6.0.0", "hashPath": "system.formats.cbor.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-8dKL3A9pVqYCJIXHd4H2epQqLxSvKeNxGonR0e5g89yMchyvsM/NLuB06otx29BicUd6+LUJZgNZmvYjjPsPGg==", "path": "system.identitymodel.tokens.jwt/8.7.0", "hashPath": "system.identitymodel.tokens.jwt.8.7.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Packaging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "path": "system.io.packaging/8.0.1", "hashPath": "system.io.packaging.8.0.1.nupkg.sha512"}, "System.IO.Pipelines/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-UIBaK7c/A3FyQxmX/747xw4rCUkm1BhNiVU617U5jweNJssNjLJkPUGhBsrlDG0BpKWCYKsncD+Kqpy4KmvZZQ==", "path": "system.io.pipelines/9.0.2", "hashPath": "system.io.pipelines.9.0.2.nupkg.sha512"}, "System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MaiPbx2/QXZc62gm/DrajRrGPG1lU4m08GWMoWiymPYM+ba4kfACp2PbiYpqJ4QiFGhHD00zX3RoVDTucjWe9g==", "path": "system.io.ports/8.0.0", "hashPath": "system.io.ports.8.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/*******": {"type": "package", "serviceable": true, "sha512": "sha512-rtfRierS5ZjG4xBfDKerRPImwDbav7q2hgf88jUZKfIjQb16PIQqzFCpPVWMb+7fS2ECXnPSmmBbssPz7WUg6g==", "path": "system.linq.dynamic.core/*******", "hashPath": "system.linq.dynamic.core.*******.nupkg.sha512"}, "System.Management/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "path": "system.management/8.0.0", "hashPath": "system.management.8.0.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.WebSockets.WebSocketProtocol/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-cVTT/Zw4JuUeX8H0tdWii0OMHsA5MY2PaFYOq/Hstw0jk479jZ+f8baCicWFNzJlCPWAe0uoNCELoB5eNmaMqA==", "path": "system.net.websockets.websocketprotocol/5.1.0", "hashPath": "system.net.websockets.websocketprotocol.5.1.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Private.ServiceModel/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-dB4hD50X7FaCCPoMJ+TShvSVXEHWBD/GKEd494N4a3V+avJmNFmKK7bM40J1zsj+QWt66DG2YkwWlRf/OHx8zw==", "path": "system.private.servicemodel/4.10.0", "hashPath": "system.private.servicemodel.4.10.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Context/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k76ubeIBOeIVg7vkQ4I+LoB8sY1EzFIc3oHEtoiNLhXleb7TBLXUQu0CFZ4sPlXJzWNabRf+gn1T7lyhOBxIMA==", "path": "system.reflection.context/8.0.0", "hashPath": "system.reflection.context.8.0.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "path": "system.runtime.caching/8.0.0", "hashPath": "system.runtime.caching.8.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>J<PERSON>+x/F6fmRQ7N6K8paasTw9PDZp4t7G76UjGNlSDgoHPF0h08vTzLYbLZpOLEJSg35d5wy2jCXGo84EN05DpQ==", "path": "system.security.cryptography.protecteddata/9.0.0", "hashPath": "system.security.cryptography.protecteddata.9.0.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HQSFbakswZ1OXFz2Bt3AJlC6ENDqWeVpgqhf213xqQUMDifzydOHIKVb1RV4prayobvR3ETIScMaQdDF2hwGZA==", "path": "system.security.cryptography.xml/8.0.0", "hashPath": "system.security.cryptography.xml.8.0.0.nupkg.sha512"}, "System.Security.Permissions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/BBylw7XevuAsHXoX9dDUUfmBIcUf7Lkz8K3ZXIKz3YRKpw8YftpSir4n4e/jDTKFoaK37AsC3xnk+GNFI1Ow==", "path": "system.security.permissions/8.0.0", "hashPath": "system.security.permissions.8.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-4TiHY9zNCyU5++0hzgQQY8Lg2iUxBndRbo/xVWxljqekBiPSK037QASLD4ZZCKc/JcA4cpHUFDXZjzrdVVn6aw==", "path": "system.servicemodel.duplex/4.10.0", "hashPath": "system.servicemodel.duplex.4.10.0.nupkg.sha512"}, "System.ServiceModel.Http/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-/PbmNSEwTQ7Vizor3F/Zp8bzR6L9YZNGIwGr1Tyc//ZZuAYDhiwiMbNpX3EnPZM63qD2bJmR/FWH9S5Ffp8K6g==", "path": "system.servicemodel.http/4.10.0", "hashPath": "system.servicemodel.http.4.10.0.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-tG69H0sRdzEuOcdGzsZwbmPk54Akb3t1Db4SSXN6hSTOc2ZBFu1jLt5wJA6ATbIjJ5WqXA8beRNLhO77lBNIdA==", "path": "system.servicemodel.nettcp/4.10.0", "hashPath": "system.servicemodel.nettcp.4.10.0.nupkg.sha512"}, "System.ServiceModel.Primitives/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-BtrvvpgU2HolcC0tUf1g+n4Fk5kLhfbIBgRibcGe7TDHXcy6zTfkyXxR88rl2tO4KEPLkJXxWf/HW/LJmsI0Ew==", "path": "system.servicemodel.primitives/4.10.0", "hashPath": "system.servicemodel.primitives.4.10.0.nupkg.sha512"}, "System.ServiceModel.Security/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-/COEfB7QqKW37DOfmzJG6rd0apH0uMMCYNDUir9ZVDQR/ulQHx12T/5jMTo25YgUUk++i0SfGDbzutMH3w/nQg==", "path": "system.servicemodel.security/4.10.0", "hashPath": "system.servicemodel.security.4.10.0.nupkg.sha512"}, "System.ServiceModel.Syndication/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CJxIUwpBkMCPmIx46tFVOt0zpRrYurUHLW6tJBcmyj+MyWpKc6MMcS69B7IdlV/bgtgys073wMIHZX9QOQ1OFA==", "path": "system.servicemodel.syndication/8.0.0", "hashPath": "system.servicemodel.syndication.8.0.0.nupkg.sha512"}, "System.ServiceProcess.ServiceController/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtYVG3bpw2n/NvNnP2g/JLri0D4UtfusTvLeH6cZPNAEjJXJVGspS3wLgVvjNbm+wjaYkFgsXejMTocV1T5DIQ==", "path": "system.serviceprocess.servicecontroller/8.0.0", "hashPath": "system.serviceprocess.servicecontroller.8.0.0.nupkg.sha512"}, "System.Speech/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CNuiA6vb95Oe5PRjClZEBiaju31vwB8OIeCgeSBXyZL6+MS4RVVB2X/C11z0xCkooHE3Vy91nM2z76emIzR+sg==", "path": "system.speech/8.0.0", "hashPath": "system.speech.8.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "System.Threading.AccessControl/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cIed5+HuYz+eV9yu9TH95zPkqmm1J9Qps9wxjB335sU8tsqc2kGdlTEH9FZzZeCS8a7mNSEsN8ZkyhQp1gfdEw==", "path": "system.threading.accesscontrol/8.0.0", "hashPath": "system.threading.accesscontrol.8.0.0.nupkg.sha512"}, "System.Threading.Channels/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CMaFr7v+57RW7uZfZkPExsPB6ljwzhjACWW1gfU35Y56rk72B/Wu+sTqxVmGSk4SFUlPc3cjeKND0zktziyjBA==", "path": "system.threading.channels/8.0.0", "hashPath": "system.threading.channels.8.0.0.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Web.Services.Description/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-Dwr64geRujAwnI+wPMJP1rf4pFaYRITrAS7EIGd0GVMwQ8OayM6ypwmnAPzQG4YTyN84w6KD5Rv8LJywYK+vUA==", "path": "system.web.services.description/4.10.0", "hashPath": "system.web.services.description.4.10.0.nupkg.sha512"}, "System.Windows.Extensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Obg3a90MkOw9mYKxrardLpY2u0axDMrSmy4JCdq2cYbelM2cUwmUir5Bomvd1yxmPL9h5LVHU1tuKBZpUjfASg==", "path": "system.windows.extensions/8.0.0", "hashPath": "system.windows.extensions.8.0.0.nupkg.sha512"}, "Z.EntityFramework.Extensions.EFCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-D1+zqtocyQb7bCuHV/Nfkvu6dCB30Zg3WyGznZTsw7QxK2Qqi5805yysKqrtjUL+BxIxBoQYglPnzubQuwuErg==", "path": "z.entityframework.extensions.efcore/*********", "hashPath": "z.entityframework.extensions.efcore.*********.nupkg.sha512"}, "Z.EntityFramework.Plus.EFCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-yKbHDlH2kLbFZ7Hcb+OJSdRGrhWk7R99itDpTHrkwT/2i6ApgjgBRJSnv8MWuyxJk4M9cqqPDzcA12gsZPvsKA==", "path": "z.entityframework.plus.efcore/*********", "hashPath": "z.entityframework.plus.efcore.*********.nupkg.sha512"}, "Z.Expressions.Eval/6.2.10": {"type": "package", "serviceable": true, "sha512": "sha512-JngT7Ze4Pn/p9ci/0d40CJy/HN3BKegPt1FvmMjgc4tmWZR9uCLLYf9E6SY+wEFQO0EIaauVkHkcMkleZtCNDg==", "path": "z.expressions.eval/6.2.10", "hashPath": "z.expressions.eval.6.2.10.nupkg.sha512"}, "Application.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}