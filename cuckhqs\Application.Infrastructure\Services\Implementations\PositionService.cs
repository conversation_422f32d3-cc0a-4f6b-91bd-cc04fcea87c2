﻿using System.Xml.Linq;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category.Employee;
using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using Application.Infrastructure.Models.Request.Category.Position;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Repositories.Abstractions.IEmployee;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;

namespace Application.Infrastructure.Services.Implementations;


public class PositionService : IPositionService
{
    private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);
    private readonly IUnitOfWork _unitOfWork;

    public PositionService(
          IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<List<PositionResponse>> GetAllPositionAsync()
    {
        try
        {
            var result = await _unitOfWork.Position.GetAllPositionAsync(1);

            var mappedResult = result.Select(entity => new PositionResponse
            {
                Id = entity.Id,
                ParentId = entity.ParentId,
                Active = entity.Active.ToString(),
                FullPositionName = entity.FullPositionName,
                ParentCode = entity.ParentCode,
                PositionCode = entity.PositionCode,
                PositionName = entity.PositionName,
                ShortPositionName = entity.ShortPositionName,
                SortOrder = entity.SortOrder,
                Classify = entity.Classify,
                IsRoot = entity.IsRoot,
                Description = entity.Description,
            }).ToList();

            return mappedResult;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw new Exception(e.Message);
        }
    }


    public async Task<List<PositionResponse>> GetAllPositionBuildTreeAsync()
    {
        try
        {
            var result = await GetAllPositionAsync();

            var tree = BuildPositionTree(result);

            return tree;

            //return result;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw new Exception(e.Message);
        }
    }


    public List<PositionResponse> BuildPositionTree(List<PositionResponse> flatList)
    {
        var lookup = flatList.ToDictionary(x => x.Id, x => x);

        List<PositionResponse> roots = new();

        foreach (var item in flatList)
        {
            if (item.ParentId.HasValue && lookup.ContainsKey(item.ParentId.Value))
            {
                var parent = lookup[item.ParentId.Value];
                parent.Children.Add(item);
            }
            else
            {
                // Gốc (không có parentId)
                roots.Add(item);
            }
        }

        return roots;
    }

    public async Task<BaseSearchResponse<PositionResponse>> SearchPositionAsync(SearchPositionRequest request)
    {
        try
        {
            int? parent_id;
            var org = await _unitOfWork.Position.GetAllPositionAsync(0);
            var parentIds = org?.Select(x => x.ParentId).Where(id => id != null).Distinct().ToList() ?? new List<int?>();
            if (request.ParentId == null || request.ParentId == 0)
            {
                 parent_id = org?.Where(e => e.IsRoot == true)
                          .Select(e => e.Id)
                          .FirstOrDefault() ?? 0;
                //parent_id = org?.Where(e => e.ParentId == org_id_root)
                //          .Select(e => e.Id)
                //          .FirstOrDefault() ?? 0;
                }
            else parent_id = request.ParentId;
            IQueryable<PositionResponse> query = _unitOfWork.Position
                                                           .AsQueryable()
                                                           .AsNoTracking()
                                                           .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                                                  x.FullPositionName.Contains(request.keyword) ||
                                                                 x.PositionName.Contains(request.keyword) ||
                                                                 x.PositionCode.Contains(request.keyword)) &&
                                                               x.ParentId == parent_id)
                                                           .Select(s => new PositionResponse()
                                                           {
                                                               Id = s.Id,
                                                               ParentId = s.ParentId,
                                                               IsRoot = s.IsRoot,
                                                               FullPositionName = s.FullPositionName,
                                                               PositionCode = s.PositionCode,
                                                               PositionName = s.PositionName,
                                                               ShortPositionName  = s.ShortPositionName,
                                                               ParentCode = s.ParentCode,
                                                               Classify = s.Classify,
                                                               Description = s.Description,
                                                               Active = s.Active == true ? "Có" : "Không",
                                                               SortOrder = s.SortOrder,
                                                               CreatedDate = s.CreatedDate,
                                                               ModifiedDate = s.ModifiedDate,
                                                               IPAddress = s.IPAddress,
                                                               ModifiedBy = s.ModifiedBy,
                                                               CreatedBy = s.CreatedBy,
                                                               Expandable = parentIds.Contains(s.Id)
                                                           });
            return await BaseSearchResponse<PositionResponse>.GetResponse(query, request);
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại CreateSoftwaresAsync: " + ex.ToString());
            throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
        }
    }

    public async Task<List<PositionResponse>> GetPositionById(int Id)
    {
        try
        {
            var org = await _unitOfWork.Position.GetAllPositionAsync(0);
            var parentIds = org?.Select(x => x.ParentId).Where(id => id != null).Distinct().ToList() ?? new List<int?>();

            var Emm = await _unitOfWork.Position
           .AsQueryable()
           .AsNoTracking()
           .Where(s => s.Id == Id)
           .ToListAsync();

            var Org_id = Emm.Select(s => new PositionResponse
            {
                Id = s.Id,
                ParentId = s.ParentId,
                IsRoot = s.IsRoot,
                FullPositionName = s.FullPositionName,
                PositionCode = s.PositionCode,
                PositionName = s.PositionName,
                ShortPositionName = s.ShortPositionName,
                ParentCode = s.ParentCode,
                Classify = s.Classify,
                Description = s.Description,
                Active = s.Active.ToString(),
                SortOrder = s.SortOrder,
                CreatedDate = s.CreatedDate,
                ModifiedDate = s.ModifiedDate,
                IPAddress = s.IPAddress,
                ModifiedBy = s.ModifiedBy,
                CreatedBy = s.CreatedBy,
                Expandable = parentIds.Contains(s.Id)
            }).ToList();

            return Org_id;
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại CreateSoftwaresAsync: " + ex.ToString());
            throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
        }
    }

    public async Task<PositionResponse> CreatePositionAsync(CreatePositionRequest request)
    {
        try
        {
            var entity = CreatePositionRequest.Create(request);

            await _unitOfWork.Position.AddAsync(entity);
            await _unitOfWork.CommitChangesAsync();
            return PositionResponse.Create(entity);
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
            throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
        }
    }

    public async Task<bool> UpdatePositionAsync(UpdatePositionRequest request)
    {
        try
        {
            var findRecord = await _unitOfWork.Position.AsQueryable()
                                                    .AsNoTracking()
                                                    .FirstOrDefaultAsync(x => x.Id == request.Id);

            if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

            var updateRecord = UpdatePositionRequest.Create(request);

            await _unitOfWork.Position.UpdateAsync(request.Id, updateRecord);
            await _unitOfWork.CommitChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
            throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
        }
    }

    public async Task<string> DeletePositionAsync(DeletePositionRequest request)
    {
        try
        {
            var position = await _unitOfWork.Position.GetAllPositionAsync(0);
            var hasParentReference = position.Any(o => o.ParentId.HasValue && request.Ids.Contains(o.ParentId.Value));
            if (hasParentReference)
            {
                return "Xóa không thành công: Một hoặc nhiều chức vụ có chức vụ con";
            }
            var record = await _unitOfWork.Position.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

            _unitOfWork.Position.RemoveRange(record);
            await _unitOfWork.CommitChangesAsync();
            return "Xóa thành công";
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
            throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
        }
    }
}
