{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "App_Data/times.nzcq01ozbz.ttf", "AssetFile": "App_Data/times.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1206884"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Q6iQeVrRM9c1aNFpo1kk9AQWvoppUKGgMmMFLsjpW4o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nzcq01ozbz"}, {"Name": "integrity", "Value": "sha256-Q6iQeVrRM9c1aNFpo1kk9AQWvoppUKGgMmMFLsjpW4o="}, {"Name": "label", "Value": "App_Data/times.ttf"}]}, {"Route": "App_Data/times.ttf", "AssetFile": "App_Data/times.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1206884"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Q6iQeVrRM9c1aNFpo1kk9AQWvoppUKGgMmMFLsjpW4o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Q6iQeVrRM9c1aNFpo1kk9AQWvoppUKGgMmMFLsjpW4o="}]}, {"Route": "Resources/Mau_bao_cao_tuan.56cxbsj16x.rdlc", "AssetFile": "Resources/Mau_bao_cao_tuan.rdlc", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "57304"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3QbCbtLMWn41naSC3535X99tPkj2jnea5fToLoI9t3c=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56cxbsj16x"}, {"Name": "integrity", "Value": "sha256-3QbCbtLMWn41naSC3535X99tPkj2jnea5fToLoI9t3c="}, {"Name": "label", "Value": "Resources/Mau_bao_cao_tuan.rdlc"}]}, {"Route": "Resources/Mau_bao_cao_tuan.rdlc", "AssetFile": "Resources/Mau_bao_cao_tuan.rdlc", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "57304"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3QbCbtLMWn41naSC3535X99tPkj2jnea5fToLoI9t3c=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3QbCbtLMWn41naSC3535X99tPkj2jnea5fToLoI9t3c="}]}, {"Route": "Templates/WorkingScheduleTemplate.cwd10xbcw3.docx", "AssetFile": "Templates/WorkingScheduleTemplate.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15445"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"ZC9DxML8wJOrB9h31RKfEjNikKL0Z628zaYt/BAT4R4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwd10xbcw3"}, {"Name": "integrity", "Value": "sha256-ZC9DxML8wJOrB9h31RKfEjNikKL0Z628zaYt/BAT4R4="}, {"Name": "label", "Value": "Templates/WorkingScheduleTemplate.docx"}]}, {"Route": "Templates/WorkingScheduleTemplate.docx", "AssetFile": "Templates/WorkingScheduleTemplate.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15445"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"ZC9DxML8wJOrB9h31RKfEjNikKL0Z628zaYt/BAT4R4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZC9DxML8wJOrB9h31RKfEjNikKL0Z628zaYt/BAT4R4="}]}]}