﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.Province;
using Application.Infrastructure.Models.Request.Category.Rank;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class ProvinceService : IProvinceService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public ProvinceService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<ProvinceResponse>> SearchProvinceAsync(SearchProvinceRequest request)
        {
            try
            {
                IQueryable<ProvinceResponse> query = _unitOfWork.Province.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.ProvinceName.Contains(request.keyword) ||
                                x.ProvinceCode.Contains(request.keyword)))
                    .Select(s => new ProvinceResponse()
                    {
                        Id = s.Id,
                        ProvinceCode = s.ProvinceCode,
                        ProvinceName = s.ProvinceName,
                        Description = s.Description,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                    });

                return await BaseSearchResponse<ProvinceResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<ProvinceResponse> CreateProvinceAsync(CreateProvinceRequest request)
        {
            try
            {
                var entity = CreateProvinceRequest.Create(request);

                await _unitOfWork.Province.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return ProvinceResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<string> DeleteProvinceAsync(DeleteProvinceRequest request)
        {
            try
            {
                var district = await _unitOfWork.Province.GetAllDistrictAsync();
                var hasParentReference = district.Any(o => o.ProvinceId.HasValue && request.Ids.Contains(o.ProvinceId.Value));
                if (hasParentReference)
                {
                    return "Xóa không thành công: Một hoặc nhiều tỉnh vẫn còn đơn vị cấp huyện";
                }
                var record = await _unitOfWork.Province.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.Province.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return "Xóa thành công";
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateProvinceAsync(UpdateProvinceRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.Province.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateProvinceRequest.Create(request);

                await _unitOfWork.Province.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
