﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.RewardType;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class RewardTypeService : IRewardTypeService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public RewardTypeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<RewardTypeResponse>> SearchRewardTypeAsync(SearchRewardTypeRequest request)
        {
            try
            {
                IQueryable<RewardTypeResponse> query = _unitOfWork.RewardType.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.RewardTypeCode.Contains(request.keyword) ||
                                x.RewardTypeName.Contains(request.keyword)
                                ))
                    .Select(s => new RewardTypeResponse()
                    {
                        Id = s.Id,
                        RewardTypeCode = s.RewardTypeCode,
                        RewardTypeName = s.RewardTypeName,
                        Class = s.Class,
                        Year = s.Year,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                    });

                return await BaseSearchResponse<RewardTypeResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<RewardTypeResponse> CreateRewardTypeAsync(CreateRewardTypeRequest request)
        {
            try
            {
                var entity = CreateRewardTypeRequest.Create(request);

                await _unitOfWork.RewardType.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return RewardTypeResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteRewardTypeAsync(DeleteRewardTypeRequest request)
        {
            try
            {
                var record = await _unitOfWork.RewardType.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.RewardType.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateRewardTypeAsync(UpdateRewardTypeRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.RewardType.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateRewardTypeRequest.Create(request);

                await _unitOfWork.RewardType.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
