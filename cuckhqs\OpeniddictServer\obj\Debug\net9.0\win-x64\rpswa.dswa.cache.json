{"GlobalPropertiesHash": "MQq35xBMKhyGzDNyYnYMgmWFwKRUW50Is+qkIWVVCl4=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["PaUWe0HOIz1ee6oL+LKurzpUqvYOgWnyi3c9k/rdi3c=", "FZcmlxYnBfdf5jd1BxKG/YMx79fRnlNwnJPG3r3iE/g=", "ebutLD9RIxg2/WO97SrzeCYkHwpUpnogF6oSYA0ALUM=", "y2c1R9Pu4VlqcQCmiWP2vUgZsf7tbWaT3M1wzH/gqbU=", "BPSvvtiCGQE7eLUJFrOdjQA/dyk6bium4bQ7VcUC7LU=", "B+US77sjtgfhy/QiGg3ZjPljUkCTkJTIfMgHPNU+4XE=", "N0VS+YtZHgyzxMNOH4OQDkyeoqOe9Ah2XsB0q7vTTVc=", "uhzK2HlRiRKuSbloku5oSwsJ2EQlkthgKjD1nfuTgTs=", "H9sT/UkrpNvT7PHkaDiUckBgKWq5ecuDvBmvFndEQLI=", "HHoyY3GrddV4AxgOpGV2AOBI2B66kXEFQjGcJMH72bQ=", "JN4d0wL0pG7ZuAOeXiThV2s7Orb1pScdd/mrnKnUub4=", "35FlvQ3Pb7UOY85HEOB3rxpSuKHUGVCJQrvukjSxifg=", "+zJyIHigYK3JJBtP4zYVp3ySIJbSUOAJNJS31auc9wU=", "BDwxZromMGAUKfhDgqb9RKJVQgcmNMnceXsfT4lc0qk=", "23apJM4J9H4OHkbXDQi/Fs6V/VeCguJV/aMdQbWKKTs=", "WVa5Lth2u/pU9pDI7qtUBhRw6RIb6rflM3PwHnTTTTs=", "E2mQBMETinTWkZdJNLKNXGeop79sdhZARGHt2mUoGiA=", "upivNyX4OFqqpXkS3K67w4MTWeuWMLkpU5AcitH2drc=", "tAsYwp6I4hADcBk5gG/Zh0AbPw7LQKYp6jiDNqgUg7Q=", "HT0iIHCSxAqZjtUfaCku90aPiNlEWGiBWp/4zkpoo2o=", "A9Ra+aYcjV6lFKKEs2ZDJ3+nDws+WRoboRJAh2LjTCc=", "beGqhWpi7X5qRGx3DqNOelQ2YOek28tt3lP3S77kMZU=", "y4SmcVkWD0hEOnd6vj0xMaShEVFBL0KowEDQy3aufIU=", "4gJ1XJugHfiinJpPX0d/4hfqxXwXjAOT/f98neFdwak=", "vUz26mdsEFQ+0xiQOkCTP3a8CQfj+OpiReZPU3brFbk=", "2Tvz6BUiO+L0fWvvzZif4XzZb2RznGDTuLtTatbbBak=", "9qrVahdcUNeIfCWknW9P0XrSJqudHWElzSEUmC7YOo8=", "WrXy4pMMfGYBe8vl8wWGE3EsAXOhz0v2lNlBuFQCgYw=", "mWus7nNpKFVeOAXPqM5aMS7ALYB/sZVn2FWeobiuw48=", "vZYeNqc+waNDehsjfSnon7YDAYcFod7tG31/gPvkVzM=", "exi4yzu5KHHilP8/9hsLsBeeKqMtW8cMmwlZG8zJeXg=", "D0xGIYmf0SAfI/BuSA7IDOjK1YMbzOxJkvBcNR/XpuI=", "ugAumnGHqOovAVoJY5eGJcaOhc67QWMccvFaPBVNXrk=", "sTl67m0Sc8cQDoXLU5Gvx1/zanRYLfrGNpAbBQUrZYM=", "l44Xq/ACN0XsXa+DNmDnw+JL/X3Sivdpkl8yZq76JGc=", "h55rxlThkXwA8DaFZpwM2syo99xScLADgQu9njVxid8=", "2elBLI56ydH45i6e8D8qeqY3QDnJDIHXKOt939n198Q=", "ltlJWVn3QXIZ0b6HhfPG3FdVdbsymELCEW0kWNgB//I=", "dUtJI5odF0K8EX+1b2VjMBQ2966YkTRHMws9In8gPqs=", "EFEATNbzeLLS7VvarHtBgF9dV5DkZx7MO6wA/JMaRNg=", "+c4dR6F3QxRZ9W94rhqkL785Sk1vGcJ4RbdPTJIXO5w=", "xQulQJz5HblXvPgDXKrnJx8g+kpqlsbriQh866tQFgw=", "IPgOYCOZtcQclu0tYHC12l83klTyuDzmdo29AR0xDVI=", "mWkAMk2TbjHIDtoUQa9jFedPMXcy7VmWkjQCUwkolKk=", "5zLtkPsCqCf4BVpLBJff5he+fmD3shDAyxCjC4og58M=", "xoC8Vkx+coyN8zYPP014osdLcNEktVTfURSuwIZSHbw=", "UGvpqsmfnV31/BtUJx89rHAm4/MA6GDCiRppJor+djM=", "+CoSxHumYMtFOeG5eFLgffR5QiroFjWjlLjC9XiK8Bg=", "4r3PQKjdsAHizN1Azm6uMe7p52wS/ORUrWsFSbS7EiA=", "niyqxwCTSOhWEw9CKmFzwADrpB23a7jdtKcYVCmKFnQ=", "0wsQWETON4PoB1/mwPiRYw3dfH137D7CTjK+l31kRG0=", "5al3fyZR+a4dcZU3n5De3tytUvDvAXthVkSI++wE47c=", "zMmXmvczynwbYWguwfqOnp/yz0vVAuaHdUJXCydCcW0=", "JhJpNVmLZmbzKn/aq+DJl9a50zXsAtnp1MLSj19G/dQ=", "jmgB3tUwcXa29vIT1s1N0V20UyN3zV5qkehA8goWtKQ=", "eHcuDnydGTir25vH9IL+VnM2e37OYcrKH9Q+AyaSu1M=", "Hdsjou77a//ZAjkPnV/fz4mW/EGm3PVYOgV3XhE0Jq8=", "nBQRf/kt5LX0PJVfVXiJALxClhsQQTXVpoFMO92ltbE=", "rEr9/i64k86Nl5/osljn6gNaTQ/JUpdod3Mtbz6rd3A=", "eeyrKX4kJ+7XoITEIoV1nt4Rf+TPZQVMRTuVMZTohvA=", "hmtXd9TSf+gt3d5CX4fqqRvI404muBtJfTXuB0suPqc=", "7yQq3TwoRSf6efxQtaoekgK6eYloSh3YsHWTnxzIxz0=", "xW4ZPEhRPAo7+GRyUwnoxK0WXwDT0bZZDKR3ac8huss=", "UtkRqJLdd5m7FP1KMygmG3FfhWNZh0WSM47iQ2+NNUg=", "kcPWADYxx0NvlX96UoUH0UUUB5TKL7lVePgcK9jgIIE=", "aE8aH66DZQzMyv6DofWLcfi0SRs+1pxHMA2xDDDvjow=", "UOj93+nKYnG54YuAfEua7IagTLHNI+5W9EH5rqhsD10=", "d9zhRN8Fk4W8awKCkcl3g8yZy5tfUCxIOi3BEHjw/jE=", "2H4iSj7NzlwmnA/REBHx/WG7+ibD+EFtI4M99xW/NN8=", "KZr7sqvSVvLqlje7C2nUoYuM6Fkc2Wt4KQ+Kz6041lI=", "yPjtcsFS4zklZAl5b22vlK8nCnTVebuJuIezQ+i7KDM=", "k95MCT+uVnr36Gx3nyEmRZmqQ7/byk/akcBE2PGdae8=", "5fF6VdPXPMs7GmNCgNBvykFU4Nkmcy0A8IlPYVZQ+mw=", "uC3EDcxRVISonwzrbJ+Y3EgCd+RydbxrQ/K3ZguzA8I=", "vLYDFwYHtI9Qzebo3uvRbaajoDsD0vpm0y93YSbsDbg=", "gzDBv9A3L1EarYYOPJtGdNUZ6E//ksQvSjBKMOV2a2Q=", "g/u9WhxyhaVpoSB4NLyiS6beo+FVgVwhU3WSu7Hz0NA=", "58pzZl/0eA00MRxFlQOUdxFj0NBXln+Jrqsntj6X9Rk=", "fdAlcRzyg2iLWRWTQjMnYNKG3maMLosOVFVBk9TZvq8=", "e/t+F8ClyQs6mQawv3bF+nnO3u1spRp0G6MMEyD/voQ=", "6dF2clGZjt7uw6BkWMkLCjjZYGR5XVdVC5xRYu6Czn4=", "PtrRCMdcxABzq5WSIz5dMnXI8OLBK0hEbxl8CnLNX9U=", "/NDEEekc3CBufdYxKsMtq8ZtNlb+xlv8JYOIBXRyEuc=", "k1v5KPkmChX5qqE6kEpeR1lJbHkce4TGitxxV+rHYAs=", "5EUCAM/4Q2Hp9Kw5aggVptKh7On3EUwdlwDMEg5LkGM=", "LgQkrtbvwhEEIV3Spr6MX2PoaHWSXXFg+R3GTxOBFjA=", "W/7mp1EjBqmq9QbUjJaeqNn6nu6zP/TTWwoWtCHuZZI=", "w8vEPcDJBS4lhoCTvWYhWGrzgSfSZN/I/LpoMXbm5P4=", "vyajgXFYa3eJ5oS0a6TwDilrXNqBubOxB/UhUm4qHTI=", "5/9WKAIdHHARKNGStjNVjCnqKFyqUjNM6wZFzR5ksfE=", "JWFeinaqEkC01ipUWrDyshUKnCXvVeEFURnnQZ5tUNE=", "rNCefg6IrLuCkBu2Jmw95crXxKdLjsfk3C9iCvtSABk=", "ZJ/JH42fsiIxNPY3OpdUvbhELLE2//EcwNCoAmJer1c=", "d21b/7AaJnZZ9E1FzqQq+Q7NkN925B+7krvqtvwlRdw=", "qT8NqD2c151GodgBkuefGPRyFZtnZi5aF2kZAM7o44k=", "eOixaBk3cmURwUVCcst+gJdRAVVobH9j3BiDeqXsEj0=", "ilFZesuEFUjZRm894aG4sTfsOXLmzYt5oluD3QQYV5o=", "7/jEnheEN3gRy8DZtExsRfRuk01ijyfqP0pU1OsscSs="], "CachedAssets": {"PaUWe0HOIz1ee6oL+LKurzpUqvYOgWnyi3c9k/rdi3c=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\css\\authorize.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "css/authorize#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "frtuvha3jy", "Integrity": "K7Whqts8bLwlZTCacDR7UTFm0wBx7dK+ppTAsA7UEhY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\authorize.css", "FileLength": 2972, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "FZcmlxYnBfdf5jd1BxKG/YMx79fRnlNwnJPG3r3iE/g=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\css\\login.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "css/login#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wg7ipxjode", "Integrity": "sPw5gnXlai+Fd69FgARljhP7HjYALuHdxxKu5THyxNc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\login.css", "FileLength": 4053, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "ebutLD9RIxg2/WO97SrzeCYkHwpUpnogF6oSYA0ALUM=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\css\\register.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "css/register#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9if3gs9u7l", "Integrity": "QYGkoPResHA1PZUXCw63m56ZH5TY5QA2YREy0S/tSYg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\register.css", "FileLength": 4073, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "y2c1R9Pu4VlqcQCmiWP2vUgZsf7tbWaT3M1wzH/gqbU=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\css\\site.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "BPSvvtiCGQE7eLUJFrOdjQA/dyk6bium4bQ7VcUC7LU=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\favicon.ico", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "B+US77sjtgfhy/QiGg3ZjPljUkCTkJTIfMgHPNU+4XE=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\images\\securitykey.min.svg", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "images/securitykey.min#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qfovega6u0", "Integrity": "drsxci8C58ICuCZIqtryHWeox10ErRHt7qh8EIlsjGU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\securitykey.min.svg", "FileLength": 1827, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "N0VS+YtZHgyzxMNOH4OQDkyeoqOe9Ah2XsB0q7vTTVc=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\images\\securitykey.svg", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "images/securitykey#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tpzvxqh2v0", "Integrity": "kb/0pd6YO0MX6hkiQDZ3XD00cTZSyp5gMmLefJ7n6ng=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\securitykey.svg", "FileLength": 6285, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "uhzK2HlRiRKuSbloku5oSwsJ2EQlkthgKjD1nfuTgTs=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\js\\helpers.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "js/helpers#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f46jbbevlw", "Integrity": "vzx+8BscYmYfJUBU5VZ/Vi/d+WE0aHLm1gMCthcrn4A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\helpers.js", "FileLength": 2722, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "H9sT/UkrpNvT7PHkaDiUckBgKWq5ecuDvBmvFndEQLI=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\js\\instant.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "js/instant#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ucvtj4ulhp", "Integrity": "/uZqfuizGEQRKs2j4Iu3wVQsoMHcaEsd2YHDZ+dI4fc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\instant.js", "FileLength": 3282, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "HHoyY3GrddV4AxgOpGV2AOBI2B66kXEFQjGcJMH72bQ=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\js\\mfa.login.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "js/mfa.login#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rpnp1fccqm", "Integrity": "sb7S23qUy9W18NMyQO+1UhaJy1U/xGByztNAhhgFRcU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\mfa.login.js", "FileLength": 5885, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "JN4d0wL0pG7ZuAOeXiThV2s7Orb1pScdd/mrnKnUub4=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\js\\mfa.register.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "js/mfa.register#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3v0arpjzns", "Integrity": "Ie5SqkNZe+ecE7dx5XcwGKPePrNO0LWGIWjghg0/BOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\mfa.register.js", "FileLength": 6437, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "35FlvQ3Pb7UOY85HEOB3rxpSuKHUGVCJQrvukjSxifg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\js\\site.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "+zJyIHigYK3JJBtP4zYVp3ySIJbSUOAJNJS31auc9wU=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t1cqhe9u97", "Integrity": "f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 74413, "LastWriteTime": "2025-07-21T14:54:49.6870019+00:00"}, "BDwxZromMGAUKfhDgqb9RKJVQgcmNMnceXsfT4lc0qk=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-21T14:54:49.6870019+00:00"}, "23apJM4J9H4OHkbXDQi/Fs6V/VeCguJV/aMdQbWKKTs=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sejl45xvog", "Integrity": "hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51800, "LastWriteTime": "2025-07-21T14:54:49.6870019+00:00"}, "WVa5Lth2u/pU9pDI7qtUBhRw6RIb6rflM3PwHnTTTTs=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-21T14:54:49.6870019+00:00"}, "E2mQBMETinTWkZdJNLKNXGeop79sdhZARGHt2mUoGiA=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xvp3kq03qx", "Integrity": "M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 74486, "LastWriteTime": "2025-07-21T14:54:49.6880245+00:00"}, "upivNyX4OFqqpXkS3K67w4MTWeuWMLkpU5AcitH2drc=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-21T14:54:49.6880245+00:00"}, "tAsYwp6I4hADcBk5gG/Zh0AbPw7LQKYp6jiDNqgUg7Q=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "22vffe00uq", "Integrity": "+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51875, "LastWriteTime": "2025-07-21T14:54:49.6880245+00:00"}, "HT0iIHCSxAqZjtUfaCku90aPiNlEWGiBWp/4zkpoo2o=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-21T14:54:49.6880245+00:00"}, "A9Ra+aYcjV6lFKKEs2ZDJ3+nDws+WRoboRJAh2LjTCc=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qesaa3a1fm", "Integrity": "rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12661, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "beGqhWpi7X5qRGx3DqNOelQ2YOek28tt3lP3S77kMZU=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "y4SmcVkWD0hEOnd6vj0xMaShEVFBL0KowEDQy3aufIU=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tmc1g35s3z", "Integrity": "y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10131, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "4gJ1XJugHfiinJpPX0d/4hfqxXwXjAOT/f98neFdwak=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "vUz26mdsEFQ+0xiQOkCTP3a8CQfj+OpiReZPU3brFbk=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rxsg74s51o", "Integrity": "gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12651, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "2Tvz6BUiO+L0fWvvzZif4XzZb2RznGDTuLtTatbbBak=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "9qrVahdcUNeIfCWknW9P0XrSJqudHWElzSEUmC7YOo8=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q9ht133ko3", "Integrity": "UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10203, "LastWriteTime": "2025-07-21T14:54:49.6901144+00:00"}, "WrXy4pMMfGYBe8vl8wWGE3EsAXOhz0v2lNlBuFQCgYw=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-21T14:54:49.6901144+00:00"}, "mWus7nNpKFVeOAXPqM5aMS7ALYB/sZVn2FWeobiuw48=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gye83jo8yx", "Integrity": "uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 113224, "LastWriteTime": "2025-07-21T14:54:49.6901144+00:00"}, "vZYeNqc+waNDehsjfSnon7YDAYcFod7tG31/gPvkVzM=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-21T14:54:49.6910005+00:00"}, "exi4yzu5KHHilP8/9hsLsBeeKqMtW8cMmwlZG8zJeXg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wl58j5mj3v", "Integrity": "ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85357, "LastWriteTime": "2025-07-21T14:54:49.6910005+00:00"}, "D0xGIYmf0SAfI/BuSA7IDOjK1YMbzOxJkvBcNR/XpuI=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-21T14:54:49.6910005+00:00"}, "ugAumnGHqOovAVoJY5eGJcaOhc67QWMccvFaPBVNXrk=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d4r6k3f320", "Integrity": "MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 113083, "LastWriteTime": "2025-07-21T14:54:49.6910005+00:00"}, "sTl67m0Sc8cQDoXLU5Gvx1/zanRYLfrGNpAbBQUrZYM=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-21T14:54:49.6921166+00:00"}, "l44Xq/ACN0XsXa+DNmDnw+JL/X3Sivdpkl8yZq76JGc=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "keugtjm085", "Integrity": "7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85286, "LastWriteTime": "2025-07-21T14:54:49.6921166+00:00"}, "h55rxlThkXwA8DaFZpwM2syo99xScLADgQu9njVxid8=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-21T14:54:49.6921166+00:00"}, "2elBLI56ydH45i6e8D8qeqY3QDnJDIHXKOt939n198Q=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zub09dkrxp", "Integrity": "CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 293102, "LastWriteTime": "2025-07-21T14:54:49.693122+00:00"}, "ltlJWVn3QXIZ0b6HhfPG3FdVdbsymELCEW0kWNgB//I=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-21T14:54:49.6941198+00:00"}, "dUtJI5odF0K8EX+1b2VjMBQ2966YkTRHMws9In8gPqs=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "43atpzeawx", "Integrity": "sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232808, "LastWriteTime": "2025-07-21T14:54:49.6941198+00:00"}, "EFEATNbzeLLS7VvarHtBgF9dV5DkZx7MO6wA/JMaRNg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-21T14:54:49.6951093+00:00"}, "+c4dR6F3QxRZ9W94rhqkL785Sk1vGcJ4RbdPTJIXO5w=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ynyaa8k90p", "Integrity": "/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 292288, "LastWriteTime": "2025-07-21T14:54:49.6960456+00:00"}, "xQulQJz5HblXvPgDXKrnJx8g+kpqlsbriQh866tQFgw=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-21T14:54:49.6970632+00:00"}, "IPgOYCOZtcQclu0tYHC12l83klTyuDzmdo29AR0xDVI=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c63t5i9ira", "Integrity": "rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232916, "LastWriteTime": "2025-07-21T14:54:49.6970632+00:00"}, "mWkAMk2TbjHIDtoUQa9jFedPMXcy7VmWkjQCUwkolKk=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-21T14:54:49.6981291+00:00"}, "5zLtkPsCqCf4BVpLBJff5he+fmD3shDAyxCjC4og58M=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4094rpi4f9", "Integrity": "bNojyBU9M3Cv/K4YqdKkq4xeaAkchkkS7HXP7554z9Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 214133, "LastWriteTime": "2025-07-21T14:54:49.6990015+00:00"}, "xoC8Vkx+coyN8zYPP014osdLcNEktVTfURSuwIZSHbw=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-21T14:54:49.7001045+00:00"}, "UGvpqsmfnV31/BtUJx89rHAm4/MA6GDCiRppJor+djM=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hd3gran6i8", "Integrity": "6zfk2L8R3wCgRbZzpkEi7UYC2bc6fYGIgFfNeqyOWnQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80727, "LastWriteTime": "2025-07-21T14:54:49.7001045+00:00"}, "+CoSxHumYMtFOeG5eFLgffR5QiroFjWjlLjC9XiK8Bg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-21T14:54:49.7001045+00:00"}, "4r3PQKjdsAHizN1Azm6uMe7p52wS/ORUrWsFSbS7EiA=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ltid2c489k", "Integrity": "idJzCWndWIhl4ZZvXbuLRTe5wquRfykXbEiDpO7ZsFk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 140276, "LastWriteTime": "2025-07-21T14:54:49.7011174+00:00"}, "niyqxwCTSOhWEw9CKmFzwADrpB23a7jdtKcYVCmKFnQ=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-21T14:54:49.7021047+00:00"}, "0wsQWETON4PoB1/mwPiRYw3dfH137D7CTjK+l31kRG0=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8vyfqsqgz1", "Integrity": "REjSeieVKd00nAKwd6dv7MMhuVKvKctPmLI4iDRs/cc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73941, "LastWriteTime": "2025-07-21T14:54:49.7021047+00:00"}, "5al3fyZR+a4dcZU3n5De3tytUvDvAXthVkSI++wE47c=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-21T14:54:49.7030272+00:00"}, "zMmXmvczynwbYWguwfqOnp/yz0vVAuaHdUJXCydCcW0=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u9q1upor1n", "Integrity": "vqK8KwEVuWidx0Ddm5m5LyBriPDPl9TcjrKZQhkFSVA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 149895, "LastWriteTime": "2025-07-21T14:54:49.7030272+00:00"}, "JhJpNVmLZmbzKn/aq+DJl9a50zXsAtnp1MLSj19G/dQ=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-21T14:54:49.7041153+00:00"}, "jmgB3tUwcXa29vIT1s1N0V20UyN3zV5qkehA8goWtKQ=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4d85u1mtcx", "Integrity": "aCTIqw9op0XQGYnNe1649V7fnihACD48OP3M8BP2xVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60641, "LastWriteTime": "2025-07-21T14:54:49.7041153+00:00"}, "eHcuDnydGTir25vH9IL+VnM2e37OYcrKH9Q+AyaSu1M=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-21T14:54:49.7041153+00:00"}, "Hdsjou77a//ZAjkPnV/fz4mW/EGm3PVYOgV3XhE0Jq8=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "nBQRf/kt5LX0PJVfVXiJALxClhsQQTXVpoFMO92ltbE=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "03hjc8e09r", "Integrity": "xJbBMLGhYbXlPrrrddrSVmduyF6KEtbxEsYxw7hYZV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19820, "LastWriteTime": "2025-07-21T14:54:49.7051054+00:00"}, "rEr9/i64k86Nl5/osljn6gNaTQ/JUpdod3Mtbz6rd3A=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "48y08845bh", "Integrity": "RFWFWIIPsjB4DucR4jqwxTWw13ZmtI+s6tVR2LJmZXk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5831, "LastWriteTime": "2025-07-21T14:54:49.7051054+00:00"}, "eeyrKX4kJ+7XoITEIoV1nt4Rf+TPZQVMRTuVMZTohvA=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-21T14:54:49.7051054+00:00"}, "hmtXd9TSf+gt3d5CX4fqqRvI404muBtJfTXuB0suPqc=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-21T14:54:49.7061182+00:00"}, "7yQq3TwoRSf6efxQtaoekgK6eYloSh3YsHWTnxzIxz0=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-21T14:54:49.7061182+00:00"}, "xW4ZPEhRPAo7+GRyUwnoxK0WXwDT0bZZDKR3ac8huss=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "worgj0nhwm", "Integrity": "0GxoUp3otVLneA5AAK6d6uUs64GFYYMOJ8VrHPs4xKA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 54238, "LastWriteTime": "2025-07-21T14:54:49.7061182+00:00"}, "UtkRqJLdd5m7FP1KMygmG3FfhWNZh0WSM47iQ2+NNUg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gk0pw8i4co", "Integrity": "4NCwFlQ/wYiXS/TiueaKHFZND6AibX0E5NEBx9i3ar0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25311, "LastWriteTime": "2025-07-21T14:54:49.7061182+00:00"}, "kcPWADYxx0NvlX96UoUH0UUUB5TKL7lVePgcK9jgIIE=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-21T14:54:49.7051054+00:00"}, "aE8aH66DZQzMyv6DofWLcfi0SRs+1pxHMA2xDDDvjow=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d6nrnj6jjx", "Integrity": "6440qEDaqjKqrIVfk4x21neDBVsbef6XUR5dUCKBv/E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 296030, "LastWriteTime": "2025-07-21T14:54:49.7081156+00:00"}, "UOj93+nKYnG54YuAfEua7IagTLHNI+5W9EH5rqhsD10=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m9qm4tazc0", "Integrity": "eqaw4I9IoPldjffqieTL/h7z0ejA9zc/fyXt+05KMl4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87535, "LastWriteTime": "2025-07-21T14:54:49.7081156+00:00"}, "d9zhRN8Fk4W8awKCkcl3g8yZy5tfUCxIOi3BEHjw/jE=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-21T14:54:49.7081156+00:00"}, "2H4iSj7NzlwmnA/REBHx/WG7+ibD+EFtI4M99xW/NN8=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jjsuc0puko", "Integrity": "AXgugf7kYNDl5ASR/8ZY13Id5HcDtTTmscOm4zwYn4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 240632, "LastWriteTime": "2025-07-21T14:54:49.7101108+00:00"}, "KZr7sqvSVvLqlje7C2nUoYuM6Fkc2Wt4KQ+Kz6041lI=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9vr69e8ynj", "Integrity": "a4xPRM7I0Iu95emrjGFRPWtNfn31f7PRjqVT5MIQMuc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70266, "LastWriteTime": "2025-07-21T14:54:49.7101108+00:00"}, "yPjtcsFS4zklZAl5b22vlK8nCnTVebuJuIezQ+i7KDM=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-21T14:54:49.7101108+00:00"}, "k95MCT+uVnr36Gx3nyEmRZmqQ7/byk/akcBE2PGdae8=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-21T14:54:49.7061182+00:00"}}, "CachedCopyCandidates": {}}