{"GlobalPropertiesHash": "MQq35xBMKhyGzDNyYnYMgmWFwKRUW50Is+qkIWVVCl4=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["9XiNZraPsBq/KYcLh3EaXuUDH8AeBDS6OYpMoM8Z/eA=", "LkEHxm4U9UXUJCq6zwCQe3ezt5ViM9/k6aAZqR0g5dc=", "2yssk1DNkYkX2DpDhWEH8awBkyQpe4PS3I/lZFSsIYY=", "vdJ2wbXYVlFQo9HixftD+UIRIzlKBDpGCg1FicEMa94=", "REjGvCpTjWkk3x8BDLemMxWYydFXSnOVGs26/yBJtiM=", "siHkRFMKH2vcQ622uEz8itW/T1QvdwmwHK/+JoVr00c=", "JHBZjiMMwrWpPwNUuTF6MlUFPt43BuxfCCQdAr/D2wY=", "AXhC4fD3ExYknF9gMAb4EKOi9RK+3jliLUGVpfNjr5M=", "mlgnqp/xxHelzPB95gUsEKENkvOQCNj/koRJ0tGppIs=", "p82U6yEWQ9mtnvdNlfCP2X46tcRQuanEL6MrENNooME=", "+5X0NWESMqUEhZtMBv7kS8TBJCzUpPQgKnbm32Qqok8=", "o7z2ogEcppvd5CM3s0RETo/XlUaM0Epw8HlsixjH2ZM=", "PmDlRYaczsJ2Nr5gZohrzll/kT6r1kGW16rhmP10UTc=", "N8PM2uhxybWeuo/tb2+EUCT5jzBU5fOQpORvDWfB0Yc=", "zwf6w2lxrv423lnwpqHHgJjeTpAX186tYMvN81PJJVg=", "f0ZHg3pCYIjoMGpkIYPOwHRo7LYnoKr0gAQ6doznePk=", "L6T8f116A2thyTlX2CL6RNipK0z1rEj3NgEg66TYNlY=", "F4g45mCUmKolkmrQuoNDctvpJguRhx8hjb47B9HNgHk=", "5i2rfiEEQR+LEhzzjZc/QhBZ577kXSlbJjty2cu04bk=", "rVV6H4KapImpo8ZIWaEcWxvT+ufsC2fEOuomZHYU50E=", "7Gc5Dj1y0K6JTMjpSme3od4gRGvvc0k1OS0CMum8FCc=", "8A4M+WcS4NzZMqQ4tjd/wjZvQwcrf31JOaKiZMuH3Hg=", "u9bkZB7WU+fP72isIvT8ikmeD91qUYJpruWgukVHMVE=", "GqJYrPw/Y8yCComlOoTBCVxG5TenHExXYHo3RTT6TNk=", "//ec3jQ7bcIDjZ++DhUPKMHVgBeV6BmxfUFUnrDe540=", "gcK4GEnvEnDONbWMDC1DN9OHFuJdEjLZR3NP6t1OwUg=", "9WIU7NdiKROAIxJKCGNNn5ZhiBqRwhpX6ap/VwP+Lrw=", "L2nRwYQyYTJNgab5y4ccR5ZDf+azm5xtMKnKGj5KvmI=", "pZtXDf5zqIITD1sFFcj+oFUrbDpnNheNUCk8DSXPsp0=", "FXVwbNsphKFRyxaIRh7cZNQ/JEW3khZnvUnX2liE590=", "4zODHKlc6j3ybwl1LANvG7+tPwbEpiBmH06Rj65o8pI=", "x3n/tzUpv6JJ3UhAiJrRpkKZSAJ8gJoC4KIwxENdTVI=", "PPBDMgYxC03YzVSawSzu2MlXZF6B0ZKtDwgbRm9SZ4U=", "ll0SJgigq3IeWP7NLwA4a7cFpYe/Vbn+lDUtncyUWJ0=", "WW7kgIR0jzAtu0ua+F2aJva36dERgGKCPSyXs6n8YWg=", "gtMuUxoJNBwR5SLd8lBW2R/2BL3UN8ZV8un1QziT+iY=", "v45/uUWZFcRj8HZaaw9Hoq+3DrIUI07JQUIlYlOEOyE=", "LxXdFRDkQWYyD0RZz20+aFpqY39tkd0lyR9LCn92yQc=", "/4C8Dk3eY1jvgkMU//ILdD+T4cw5zliJxGKQd/oNmqg=", "6wzeBxpXh+1X3mxD5tP7h4X4Pw/OH4YylzN9zBOeQX0=", "M1FDg7hPsenFBOG891+3o9tlDRGFqnYf+Q8VzUwUEek=", "Dd6PpHHQ2XzQhgOAGYPXHOEx0MglHAOOLsudYP/1aIs=", "Xx5y2CD6t3ilqidRteLynGklV61d6PFnNtaDIRcI9YE=", "dqHnpakSCPqMAozXnj3OKieZfbGINRX9qAbh5svk0cI=", "TBrrEtZbBfgQHkpiNOkdridWYuXfz492T4vGs4Bcs60=", "gfAMAScuqDhXvMXnq+HKjD9HGz3pHhHUiwV/XoMwAu8=", "qcXjKzxvwn/oMej38yn6QLc07OYe13KItfcj7EIi7r4=", "rYFcSDT2jVPx5HwNXGFwxkQCSKqW1zjjI57Fcix3dqk=", "icOfE99Vo+N6XDp1chKXWpVFLIbc5WmK7YipsGkZseQ=", "WjTcAXl4dSsjsiFe/SbR3jYnipyWh9aMHtlr2a5W48k=", "k65SNDHESI1LHzQlMWgFIx59nICnbamBHRd1x9LL/1g=", "tWa+B3rsE+0o+UlZISWgQI/8OkVeMChmRw2eeT/BE/M=", "OEiwQhqHxtfgcttagwnpnI/kKLkTGwbWbeJPjomw5bg=", "s6xFf9tJy0Dh9uXmrAMPrlkN+i+wp/7t1sNSAce/OBE=", "eXaYGlUY2VmIRO9n63/zPUWU/UcU+y9+Bn3rahZ0A0o=", "nU2D9BiyNDvatLsZBNmOf8rzHNATHDs6Rwz0EMe7wGM=", "oqrXGfnSWA9D1TrSk966bWrZNUYXLJMQSEp9yC1v0F8=", "zg2x2z4W1mSxeEygnb4eJHpi2J4LB861mkOrS76SU1Q=", "A10KwZuKrE3KFK2BsGlGj49AzNMc0nJXGuEAIQvPu+0=", "8nNVPH1h3bD0M9BDkWODV7hU44LU5+tTQntvvO/YTYw=", "4FAfnRNTb5OoPiM7tg07YYzd+51BOY7xKHOM7lXpCjs=", "nppLvaklHbQoSkfl1TSExE5EMFaH28k2t58G6EG3Xv0=", "tuQC+Ls7jmCw3MnbqT+PAhmYx8dNJSeoSYRgW8OEMng=", "ytRduP60G574rA2mP4MC/0n6jx7upAmjq/hK7VNb+sM=", "9MZh93S5UByoPORIKqMG8m+POXtadei3fWtNHW8BVP4=", "pHsuGEl57puP6WnflfJM2Key5xe/9k3AuK1NOdC1bDQ=", "H2oRNoiz/l64itGhDpBSW8GH8911XlZRRMtR7APJRvE=", "L09Tgj07Fu8zHZlSrj2DZx7qY5rgdC5QosFJaBqnMTU=", "QKCcNYYOXNWLg3F14DBVgpNmtcoMTzMgdNKozkjLjmM=", "bRRX2LZEsH1I4Qq5qw5KjilD9nlrzglo4BTwdS3ioPQ=", "+KLjMn5aDSMiKDyqNJjQ3zUeg+o5H7M+TqeOKa06DEg=", "usoe+rnKx9ApgpIMqPcGLBv+/KST66dTKITwHGgTnf4=", "5fF6VdPXPMs7GmNCgNBvykFU4Nkmcy0A8IlPYVZQ+mw=", "uC3EDcxRVISonwzrbJ+Y3EgCd+RydbxrQ/K3ZguzA8I=", "vLYDFwYHtI9Qzebo3uvRbaajoDsD0vpm0y93YSbsDbg=", "TqQfVqBvU4vcZRCiKqsFjacfOs8f0iO16MXzMcTOitA=", "BufCAcn2MEsCR/XXyWt0uHsxOwm4tn97Ji2K5KNK6AY=", "4RgAnUYrQhxQjLqcx2B6qeLwUgs/vwJ+1/LdKpaoGcg=", "LPtE7zgrL2Mh7VD45LVRf8v8CwOAFIZgz4j1nWyPvhU=", "H2agBHremtz0slHJ1PefiGfc8FQdr1010/XN5171q3k=", "M3AV0HHM7cGUsGZWcaZ6VhqibE4CxDRElH91znOGdg4=", "FU+51tygCgh3UgQX+Dq3lcNxJQaEOhRbErLJy9t2L9Q=", "cagKRven5iseZRtLeD3fId6nmLN5YJ+CpVkWziWNuGY=", "Ygid0fUOxHGHR/Hc5AkHm+iP72Y7fJZEThwD5O+8ljw=", "od5RiKtJcoJF02gkOddCgEx3JB/tqJWb6mHp+veafu4=", "1nG2z2GdeY4J3FYhHzASfc3NmUDDWLRzgr0mJGJNMt8=", "F+/AtIRHtvTRW08Tsa2roPTgilESpPIANE2i5bmm5kE=", "TeHl2UoQbqw8a708uJBVSKsdgzhPQIvKlE0enCKTg3Q=", "9XRqKwha0hcnZeevqs7U1WOK7bAX0bQQ/8Im+gd/AXs=", "AEM+NOyV2uPGAW+SAV6Odu7TA3RyJJN04Nr8YjP3gX8=", "3zGByEQ/QL5hj71tXHeXWjT84YBcoaejduZaS+KBrcc=", "zGHOQlSbDqhkUcN9wIo7LI+IzqW+OosQTlfAebOhCfo=", "kx+aujhsk4V82KyGaZ7esO3CMfAM/080ec559veaDqw=", "U2Rl1PGF+iaPogwHYKGKy/zwSXvUMkbwQRz08EgTn8E=", "UUc4Yi8UFEVg/y53Qw/RiPClgaJlZBIdrmxUxF8B8J8=", "S/uOpn6yHNWRhvZvB8G3//yWyF0cGRMoZMZzfUadABk=", "pSnkxdA91EorZ+4ETr3x4d9t5In0hIb7s+wiL93lx8M=", "690fAg/TOFUD689KY64IKadVi0dcXuhm8kbbz7pCJhw="], "CachedAssets": {"usoe+rnKx9ApgpIMqPcGLBv+/KST66dTKITwHGgTnf4=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-21T14:54:49.7061182+00:00"}, "+KLjMn5aDSMiKDyqNJjQ3zUeg+o5H7M+TqeOKa06DEg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-21T14:54:49.7101108+00:00"}, "bRRX2LZEsH1I4Qq5qw5KjilD9nlrzglo4BTwdS3ioPQ=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9vr69e8ynj", "Integrity": "a4xPRM7I0Iu95emrjGFRPWtNfn31f7PRjqVT5MIQMuc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70266, "LastWriteTime": "2025-07-21T14:54:49.7101108+00:00"}, "QKCcNYYOXNWLg3F14DBVgpNmtcoMTzMgdNKozkjLjmM=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jjsuc0puko", "Integrity": "AXgugf7kYNDl5ASR/8ZY13Id5HcDtTTmscOm4zwYn4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 240632, "LastWriteTime": "2025-07-21T14:54:49.7101108+00:00"}, "L09Tgj07Fu8zHZlSrj2DZx7qY5rgdC5QosFJaBqnMTU=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-21T14:54:49.7081156+00:00"}, "H2oRNoiz/l64itGhDpBSW8GH8911XlZRRMtR7APJRvE=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m9qm4tazc0", "Integrity": "eqaw4I9IoPldjffqieTL/h7z0ejA9zc/fyXt+05KMl4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87535, "LastWriteTime": "2025-07-21T14:54:49.7081156+00:00"}, "pHsuGEl57puP6WnflfJM2Key5xe/9k3AuK1NOdC1bDQ=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d6nrnj6jjx", "Integrity": "6440qEDaqjKqrIVfk4x21neDBVsbef6XUR5dUCKBv/E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 296030, "LastWriteTime": "2025-07-21T14:54:49.7081156+00:00"}, "9MZh93S5UByoPORIKqMG8m+POXtadei3fWtNHW8BVP4=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-21T14:54:49.7051054+00:00"}, "ytRduP60G574rA2mP4MC/0n6jx7upAmjq/hK7VNb+sM=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gk0pw8i4co", "Integrity": "4NCwFlQ/wYiXS/TiueaKHFZND6AibX0E5NEBx9i3ar0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25311, "LastWriteTime": "2025-07-21T14:54:49.7061182+00:00"}, "tuQC+Ls7jmCw3MnbqT+PAhmYx8dNJSeoSYRgW8OEMng=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "worgj0nhwm", "Integrity": "0GxoUp3otVLneA5AAK6d6uUs64GFYYMOJ8VrHPs4xKA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 54238, "LastWriteTime": "2025-07-21T14:54:49.7061182+00:00"}, "nppLvaklHbQoSkfl1TSExE5EMFaH28k2t58G6EG3Xv0=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-21T14:54:49.7061182+00:00"}, "4FAfnRNTb5OoPiM7tg07YYzd+51BOY7xKHOM7lXpCjs=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-21T14:54:49.7061182+00:00"}, "8nNVPH1h3bD0M9BDkWODV7hU44LU5+tTQntvvO/YTYw=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-21T14:54:49.7051054+00:00"}, "A10KwZuKrE3KFK2BsGlGj49AzNMc0nJXGuEAIQvPu+0=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "48y08845bh", "Integrity": "RFWFWIIPsjB4DucR4jqwxTWw13ZmtI+s6tVR2LJmZXk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5831, "LastWriteTime": "2025-07-21T14:54:49.7051054+00:00"}, "zg2x2z4W1mSxeEygnb4eJHpi2J4LB861mkOrS76SU1Q=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "03hjc8e09r", "Integrity": "xJbBMLGhYbXlPrrrddrSVmduyF6KEtbxEsYxw7hYZV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19820, "LastWriteTime": "2025-07-21T14:54:49.7051054+00:00"}, "oqrXGfnSWA9D1TrSk966bWrZNUYXLJMQSEp9yC1v0F8=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "nU2D9BiyNDvatLsZBNmOf8rzHNATHDs6Rwz0EMe7wGM=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-21T14:54:49.7041153+00:00"}, "eXaYGlUY2VmIRO9n63/zPUWU/UcU+y9+Bn3rahZ0A0o=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4d85u1mtcx", "Integrity": "aCTIqw9op0XQGYnNe1649V7fnihACD48OP3M8BP2xVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60641, "LastWriteTime": "2025-07-21T14:54:49.7041153+00:00"}, "s6xFf9tJy0Dh9uXmrAMPrlkN+i+wp/7t1sNSAce/OBE=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-21T14:54:49.7041153+00:00"}, "OEiwQhqHxtfgcttagwnpnI/kKLkTGwbWbeJPjomw5bg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u9q1upor1n", "Integrity": "vqK8KwEVuWidx0Ddm5m5LyBriPDPl9TcjrKZQhkFSVA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 149895, "LastWriteTime": "2025-07-21T14:54:49.7030272+00:00"}, "tWa+B3rsE+0o+UlZISWgQI/8OkVeMChmRw2eeT/BE/M=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-21T14:54:49.7030272+00:00"}, "k65SNDHESI1LHzQlMWgFIx59nICnbamBHRd1x9LL/1g=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8vyfqsqgz1", "Integrity": "REjSeieVKd00nAKwd6dv7MMhuVKvKctPmLI4iDRs/cc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73941, "LastWriteTime": "2025-07-21T14:54:49.7021047+00:00"}, "WjTcAXl4dSsjsiFe/SbR3jYnipyWh9aMHtlr2a5W48k=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-21T14:54:49.7021047+00:00"}, "icOfE99Vo+N6XDp1chKXWpVFLIbc5WmK7YipsGkZseQ=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ltid2c489k", "Integrity": "idJzCWndWIhl4ZZvXbuLRTe5wquRfykXbEiDpO7ZsFk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 140276, "LastWriteTime": "2025-07-21T14:54:49.7011174+00:00"}, "rYFcSDT2jVPx5HwNXGFwxkQCSKqW1zjjI57Fcix3dqk=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-21T14:54:49.7001045+00:00"}, "qcXjKzxvwn/oMej38yn6QLc07OYe13KItfcj7EIi7r4=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hd3gran6i8", "Integrity": "6zfk2L8R3wCgRbZzpkEi7UYC2bc6fYGIgFfNeqyOWnQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80727, "LastWriteTime": "2025-07-21T14:54:49.7001045+00:00"}, "gfAMAScuqDhXvMXnq+HKjD9HGz3pHhHUiwV/XoMwAu8=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-21T14:54:49.7001045+00:00"}, "TBrrEtZbBfgQHkpiNOkdridWYuXfz492T4vGs4Bcs60=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4094rpi4f9", "Integrity": "bNojyBU9M3Cv/K4YqdKkq4xeaAkchkkS7HXP7554z9Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 214133, "LastWriteTime": "2025-07-21T14:54:49.6990015+00:00"}, "dqHnpakSCPqMAozXnj3OKieZfbGINRX9qAbh5svk0cI=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-21T14:54:49.6981291+00:00"}, "Xx5y2CD6t3ilqidRteLynGklV61d6PFnNtaDIRcI9YE=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c63t5i9ira", "Integrity": "rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232916, "LastWriteTime": "2025-07-21T14:54:49.6970632+00:00"}, "Dd6PpHHQ2XzQhgOAGYPXHOEx0MglHAOOLsudYP/1aIs=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-21T14:54:49.6970632+00:00"}, "M1FDg7hPsenFBOG891+3o9tlDRGFqnYf+Q8VzUwUEek=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ynyaa8k90p", "Integrity": "/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 292288, "LastWriteTime": "2025-07-21T14:54:49.6960456+00:00"}, "6wzeBxpXh+1X3mxD5tP7h4X4Pw/OH4YylzN9zBOeQX0=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-21T14:54:49.6951093+00:00"}, "/4C8Dk3eY1jvgkMU//ILdD+T4cw5zliJxGKQd/oNmqg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "43atpzeawx", "Integrity": "sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232808, "LastWriteTime": "2025-07-21T14:54:49.6941198+00:00"}, "LxXdFRDkQWYyD0RZz20+aFpqY39tkd0lyR9LCn92yQc=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-21T14:54:49.6941198+00:00"}, "v45/uUWZFcRj8HZaaw9Hoq+3DrIUI07JQUIlYlOEOyE=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zub09dkrxp", "Integrity": "CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 293102, "LastWriteTime": "2025-07-21T14:54:49.693122+00:00"}, "gtMuUxoJNBwR5SLd8lBW2R/2BL3UN8ZV8un1QziT+iY=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-21T14:54:49.6921166+00:00"}, "WW7kgIR0jzAtu0ua+F2aJva36dERgGKCPSyXs6n8YWg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "keugtjm085", "Integrity": "7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85286, "LastWriteTime": "2025-07-21T14:54:49.6921166+00:00"}, "ll0SJgigq3IeWP7NLwA4a7cFpYe/Vbn+lDUtncyUWJ0=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-21T14:54:49.6921166+00:00"}, "PPBDMgYxC03YzVSawSzu2MlXZF6B0ZKtDwgbRm9SZ4U=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d4r6k3f320", "Integrity": "MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 113083, "LastWriteTime": "2025-07-21T14:54:49.6910005+00:00"}, "x3n/tzUpv6JJ3UhAiJrRpkKZSAJ8gJoC4KIwxENdTVI=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-21T14:54:49.6910005+00:00"}, "4zODHKlc6j3ybwl1LANvG7+tPwbEpiBmH06Rj65o8pI=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wl58j5mj3v", "Integrity": "ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85357, "LastWriteTime": "2025-07-21T14:54:49.6910005+00:00"}, "FXVwbNsphKFRyxaIRh7cZNQ/JEW3khZnvUnX2liE590=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-21T14:54:49.6910005+00:00"}, "pZtXDf5zqIITD1sFFcj+oFUrbDpnNheNUCk8DSXPsp0=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gye83jo8yx", "Integrity": "uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 113224, "LastWriteTime": "2025-07-21T14:54:49.6901144+00:00"}, "L2nRwYQyYTJNgab5y4ccR5ZDf+azm5xtMKnKGj5KvmI=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-21T14:54:49.6901144+00:00"}, "9WIU7NdiKROAIxJKCGNNn5ZhiBqRwhpX6ap/VwP+Lrw=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q9ht133ko3", "Integrity": "UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10203, "LastWriteTime": "2025-07-21T14:54:49.6901144+00:00"}, "gcK4GEnvEnDONbWMDC1DN9OHFuJdEjLZR3NP6t1OwUg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "//ec3jQ7bcIDjZ++DhUPKMHVgBeV6BmxfUFUnrDe540=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rxsg74s51o", "Integrity": "gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12651, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "GqJYrPw/Y8yCComlOoTBCVxG5TenHExXYHo3RTT6TNk=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "u9bkZB7WU+fP72isIvT8ikmeD91qUYJpruWgukVHMVE=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tmc1g35s3z", "Integrity": "y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10131, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "8A4M+WcS4NzZMqQ4tjd/wjZvQwcrf31JOaKiZMuH3Hg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "7Gc5Dj1y0K6JTMjpSme3od4gRGvvc0k1OS0CMum8FCc=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qesaa3a1fm", "Integrity": "rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12661, "LastWriteTime": "2025-07-21T14:54:49.6891187+00:00"}, "rVV6H4KapImpo8ZIWaEcWxvT+ufsC2fEOuomZHYU50E=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-21T14:54:49.6880245+00:00"}, "5i2rfiEEQR+LEhzzjZc/QhBZ577kXSlbJjty2cu04bk=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "22vffe00uq", "Integrity": "+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51875, "LastWriteTime": "2025-07-21T14:54:49.6880245+00:00"}, "F4g45mCUmKolkmrQuoNDctvpJguRhx8hjb47B9HNgHk=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-21T14:54:49.6880245+00:00"}, "L6T8f116A2thyTlX2CL6RNipK0z1rEj3NgEg66TYNlY=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xvp3kq03qx", "Integrity": "M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 74486, "LastWriteTime": "2025-07-21T14:54:49.6880245+00:00"}, "f0ZHg3pCYIjoMGpkIYPOwHRo7LYnoKr0gAQ6doznePk=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-21T14:54:49.6870019+00:00"}, "zwf6w2lxrv423lnwpqHHgJjeTpAX186tYMvN81PJJVg=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sejl45xvog", "Integrity": "hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51800, "LastWriteTime": "2025-07-21T14:54:49.6870019+00:00"}, "N8PM2uhxybWeuo/tb2+EUCT5jzBU5fOQpORvDWfB0Yc=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-21T14:54:49.6870019+00:00"}, "PmDlRYaczsJ2Nr5gZohrzll/kT6r1kGW16rhmP10UTc=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t1cqhe9u97", "Integrity": "f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 74413, "LastWriteTime": "2025-07-21T14:54:49.6870019+00:00"}, "o7z2ogEcppvd5CM3s0RETo/XlUaM0Epw8HlsixjH2ZM=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\js\\site.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "+5X0NWESMqUEhZtMBv7kS8TBJCzUpPQgKnbm32Qqok8=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\js\\mfa.register.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "js/mfa.register#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3v0arpjzns", "Integrity": "Ie5SqkNZe+ecE7dx5XcwGKPePrNO0LWGIWjghg0/BOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\mfa.register.js", "FileLength": 6437, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "p82U6yEWQ9mtnvdNlfCP2X46tcRQuanEL6MrENNooME=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\js\\mfa.login.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "js/mfa.login#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rpnp1fccqm", "Integrity": "sb7S23qUy9W18NMyQO+1UhaJy1U/xGByztNAhhgFRcU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\mfa.login.js", "FileLength": 5885, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "mlgnqp/xxHelzPB95gUsEKENkvOQCNj/koRJ0tGppIs=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\js\\instant.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "js/instant#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ucvtj4ulhp", "Integrity": "/uZqfuizGEQRKs2j4Iu3wVQsoMHcaEsd2YHDZ+dI4fc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\instant.js", "FileLength": 3282, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "AXhC4fD3ExYknF9gMAb4EKOi9RK+3jliLUGVpfNjr5M=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\js\\helpers.js", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "js/helpers#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f46jbbevlw", "Integrity": "vzx+8BscYmYfJUBU5VZ/Vi/d+WE0aHLm1gMCthcrn4A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\helpers.js", "FileLength": 2722, "LastWriteTime": "2025-07-21T14:54:49.6861177+00:00"}, "JHBZjiMMwrWpPwNUuTF6MlUFPt43BuxfCCQdAr/D2wY=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\images\\securitykey.svg", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "images/securitykey#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tpzvxqh2v0", "Integrity": "kb/0pd6YO0MX6hkiQDZ3XD00cTZSyp5gMmLefJ7n6ng=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\securitykey.svg", "FileLength": 6285, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "siHkRFMKH2vcQ622uEz8itW/T1QvdwmwHK/+JoVr00c=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\images\\securitykey.min.svg", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "images/securitykey.min#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qfovega6u0", "Integrity": "drsxci8C58ICuCZIqtryHWeox10ErRHt7qh8EIlsjGU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\securitykey.min.svg", "FileLength": 1827, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "REjGvCpTjWkk3x8BDLemMxWYydFXSnOVGs26/yBJtiM=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\favicon.ico", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "vdJ2wbXYVlFQo9HixftD+UIRIzlKBDpGCg1FicEMa94=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\css\\site.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "2yssk1DNkYkX2DpDhWEH8awBkyQpe4PS3I/lZFSsIYY=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\css\\register.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "css/register#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9if3gs9u7l", "Integrity": "QYGkoPResHA1PZUXCw63m56ZH5TY5QA2YREy0S/tSYg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\register.css", "FileLength": 4073, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "LkEHxm4U9UXUJCq6zwCQe3ezt5ViM9/k6aAZqR0g5dc=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\css\\login.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "css/login#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wg7ipxjode", "Integrity": "sPw5gnXlai+Fd69FgARljhP7HjYALuHdxxKu5THyxNc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\login.css", "FileLength": 4053, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}, "9XiNZraPsBq/KYcLh3EaXuUDH8AeBDS6OYpMoM8Z/eA=": {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\css\\authorize.css", "SourceId": "OpeniddictServer", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\OpeniddictServer\\wwwroot\\", "BasePath": "_content/OpeniddictServer", "RelativePath": "css/authorize#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "frtuvha3jy", "Integrity": "K7Whqts8bLwlZTCacDR7UTFm0wBx7dK+ppTAsA7UEhY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\authorize.css", "FileLength": 2972, "LastWriteTime": "2025-07-21T14:54:49.6850003+00:00"}}, "CachedCopyCandidates": {}}