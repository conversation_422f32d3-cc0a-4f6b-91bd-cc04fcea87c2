{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:6583", "sslPort": 0}}, "profiles": {"Application": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "http://localhost:5095", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Local": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Local"}, "applicationUrl": "http://localhost:5095"}, "Development": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://*************:5095"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}