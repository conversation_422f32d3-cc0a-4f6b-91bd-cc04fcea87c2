{"version": "0.2.0", "configurations": [{"name": "Launch API", "type": "coreclr", "request": "launch", "preLaunchTask": "build-api", "program": "${workspaceFolder}/cuckhqs/Application.API/bin/Debug/net9.0/Application.API.dll", "args": [], "cwd": "${workspaceFolder}/cuckhqs/Application.API", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}, "requireExactSource": false}, {"name": "Launch OpenIddict Server", "type": "coreclr", "request": "launch", "preLaunchTask": "build-openiddict", "program": "${workspaceFolder}/cuckhqs/OpeniddictServer/bin/Debug/net9.0/win-x64/OpeniddictServer.dll", "args": [], "cwd": "${workspaceFolder}/cuckhqs/OpeniddictServer", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}, "requireExactSource": false}, {"name": "Launch API (No Build)", "type": "dotnet", "request": "launch", "projectPath": "${workspaceFolder}/cuckhqs/Application.API/Application.API.csproj"}, {"name": "Launch OpenIddict Server (No Build)", "type": "dotnet", "request": "launch", "projectPath": "${workspaceFolder}/cuckhqs/OpeniddictServer/OpeniddictServer.csproj"}], "compounds": [{"name": "Launch All .NET Services", "configurations": ["Launch API (No Build)", "Launch OpenIddict Server (No Build)"], "stopAll": true}, {"name": "Launch All .NET Services (With Build)", "configurations": ["Launch API", "Launch OpenIddict Server"], "stopAll": true}]}