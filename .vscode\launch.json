{"version": "0.2.0", "configurations": [{"name": "Launch API", "type": "coreclr", "request": "launch", "preLaunchTask": "build-api", "program": "${workspaceFolder}/cuckhqs/Application.API/bin/Debug/net9.0/Application.API.dll", "args": [], "cwd": "${workspaceFolder}/cuckhqs/Application.API", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Launch OpenIddict Server", "type": "coreclr", "request": "launch", "preLaunchTask": "build-openiddict", "program": "${workspaceFolder}/cuckhqs/OpeniddictServer/bin/Debug/net9.0/OpeniddictServer.dll", "args": [], "cwd": "${workspaceFolder}/cuckhqs/OpeniddictServer", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}], "compounds": [{"name": "Launch All .NET Services", "configurations": ["Launch API", "Launch OpenIddict Server"], "stopAll": true}]}