﻿using OpenIddict.Abstractions;
using System.Globalization;
using Microsoft.Extensions.Options;
using static OpenIddict.Abstractions.OpenIddictConstants;
using OpeniddictServer.ViewModels.Authorization;
using Application.Infrastructure.Configurations;

namespace OpeniddictServer
{
    public class Worker : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly OAuthClientSettings _clientSettings;

        public Worker(IServiceProvider serviceProvider, IOptions<OAuthClientSettings> options)
        {
            _serviceProvider = serviceProvider;
            _clientSettings = options.Value;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            await context.Database.EnsureCreatedAsync(cancellationToken);
            await RegisterApplicationsAsync(scope.ServiceProvider, _clientSettings);
            await RegisterScopesAsync(scope.ServiceProvider);
        }

        static async Task RegisterApplicationsAsync(IServiceProvider provider, OAuthClientSettings _clientSettings)
        {
            var manager = provider.GetRequiredService<IOpenIddictApplicationManager>();

            // Angular UI Client
            if (await manager.FindByClientIdAsync("angularclient") is null)
            {
                var clientApp = new OpenIddictApplicationDescriptor
                {
                    ClientId = "angularclient",
                    ConsentType = ConsentTypes.Implicit,
                    DisplayName = "angular client",
                    DisplayNames =
                    {
                        [CultureInfo.GetCultureInfo("fr-FR")] = "Application cliente MVC"
                    },
                    Permissions =
                    {
                        Permissions.Endpoints.Authorization,
                        Permissions.Endpoints.EndSession,
                        Permissions.Endpoints.Token,
                        Permissions.Endpoints.Revocation,
                        Permissions.GrantTypes.AuthorizationCode,
                        Permissions.GrantTypes.RefreshToken,
                        Permissions.ResponseTypes.Code,
                        Permissions.Scopes.Email,
                        Permissions.Scopes.Profile,
                        Permissions.Scopes.Roles,
                        Permissions.Prefixes.Scope + "dataEventRecords"
                    },
                    Requirements =
                    {
                        Requirements.Features.ProofKeyForCodeExchange
                    }
                };
                AddRedirectUris(clientApp, _clientSettings.AngularClientRedirectUris, _clientSettings.AngularClientPostLogoutRedirectUris);
                await manager.CreateAsync(clientApp);
            }

            // Blazor Hosted
            if (await manager.FindByClientIdAsync("blazorcodeflowpkceclient") is null)
            {
                var clientApp = new OpenIddictApplicationDescriptor
                {
                    ClientId = "blazorcodeflowpkceclient",
                    ConsentType = ConsentTypes.Explicit,
                    DisplayName = "Blazor code PKCE",
                    DisplayNames =
                    {
                        [CultureInfo.GetCultureInfo("fr-FR")] = "Application cliente MVC"
                    },
                    ClientSecret = "codeflow_pkce_client_secret",
                    Permissions =
                    {
                        Permissions.Endpoints.Authorization,
                        Permissions.Endpoints.EndSession,
                        Permissions.Endpoints.Token,
                        Permissions.Endpoints.Revocation,
                        Permissions.GrantTypes.AuthorizationCode,
                        Permissions.GrantTypes.RefreshToken,
                        Permissions.ResponseTypes.Code,
                        Permissions.Scopes.Email,
                        Permissions.Scopes.Profile,
                        Permissions.Scopes.Roles,
                        Permissions.Prefixes.Scope + "dataEventRecords"
                    },
                    Requirements =
                    {
                        Requirements.Features.ProofKeyForCodeExchange
                    }
                };
                AddRedirectUris(clientApp, _clientSettings.BlazorClientRedirectUris, _clientSettings.BlazorClientPostLogoutRedirectUris);
                await manager.CreateAsync(clientApp);
            }

            // Web Hosted
            if (await manager.FindByClientIdAsync("webpubliclient") is null)
            {
                var clientApp = new OpenIddictApplicationDescriptor
                {
                    ClientId = "webpubliclient",
                    ConsentType = ConsentTypes.Explicit,
                    DisplayName = "Web code PKCE",
                    DisplayNames =
                    {
                        [CultureInfo.GetCultureInfo("fr-FR")] = "Application cliente MVC"
                    },
                    ClientSecret = "codeflow_pkce_client_secret123",
                    Permissions =
                    {
                        Permissions.Endpoints.Authorization,
                        Permissions.Endpoints.EndSession,
                        Permissions.Endpoints.Token,
                        Permissions.Endpoints.Revocation,
                        Permissions.GrantTypes.AuthorizationCode,
                        Permissions.GrantTypes.RefreshToken,
                        Permissions.ResponseTypes.Code,
                        Permissions.Scopes.Email,
                        Permissions.Scopes.Profile,
                        Permissions.Scopes.Roles,
                        Permissions.Prefixes.Scope + "dataEventRecords"
                    },
                    Requirements =
                    {
                        Requirements.Features.ProofKeyForCodeExchange
                    }
                };
                AddRedirectUris(clientApp, _clientSettings.WebClientRedirectUris, _clientSettings.WebClientPostLogoutRedirectUris);
                await manager.CreateAsync(clientApp);
            }

            // API application CC
            if (await manager.FindByClientIdAsync("CC") == null)
            {
                await manager.CreateAsync(new OpenIddictApplicationDescriptor
                {
                    ClientId = "CC",
                    ClientSecret = "cc_secret",
                    DisplayName = "CC for protected API",
                    Permissions =
                    {
                        Permissions.Endpoints.Authorization,
                        Permissions.Endpoints.Token,
                        Permissions.GrantTypes.ClientCredentials,
                        Permissions.Prefixes.Scope + "dataEventRecords"
                    }
                });
            }

            // API
            if (await manager.FindByClientIdAsync("rs_dataEventRecordsApi") == null)
            {
                var descriptor = new OpenIddictApplicationDescriptor
                {
                    ClientId = "rs_dataEventRecordsApi",
                    ClientSecret = "dataEventRecordsSecret",
                    DisplayName = "Application API",
                    ConsentType = ConsentTypes.Explicit,
                    Permissions =
                    {
                        Permissions.Endpoints.Authorization,
                        Permissions.GrantTypes.AuthorizationCode,
                        Permissions.GrantTypes.RefreshToken,
                        Permissions.Endpoints.Token,
                        Permissions.GrantTypes.ClientCredentials,
                        Permissions.ResponseTypes.Code,
                        Permissions.Prefixes.Scope + "dataEventRecords",
                        Permissions.Endpoints.Introspection
                    }
                };
                await manager.CreateAsync(descriptor);
            }
            // API
            if (await manager.FindByClientIdAsync("swaggerClient") == null)
            {
                var descriptor = new OpenIddictApplicationDescriptor
                {
                    ClientId = "swaggerClient",
                    ClientSecret = "swaggerClient@123",
                    DisplayName = "SwaggerClient API",
                    ConsentType = ConsentTypes.Implicit,
                    Permissions =
                    {
                        Permissions.GrantTypes.Password,
                        Permissions.Endpoints.Authorization,
                        Permissions.GrantTypes.AuthorizationCode,
                        Permissions.GrantTypes.RefreshToken,
                        Permissions.Endpoints.Token,
                        Permissions.GrantTypes.ClientCredentials,
                        Permissions.Scopes.Email,
                        Permissions.Scopes.Profile,
                        Permissions.Scopes.Roles,
                        Permissions.ResponseTypes.Code,
                        Permissions.Prefixes.Scope + "swaggerClient",
                        Permissions.Endpoints.Introspection
                    }
                };
                await manager.CreateAsync(descriptor);
            }
            if (await manager.FindByClientIdAsync("mobileapp") == null)
            {
                await manager.CreateAsync(new OpenIddictApplicationDescriptor
                {
                    ClientId = "mobileapp",
                    DisplayName = "React Native App",
                    RedirectUris =
                {
                    new Uri("myapp://oauthredirect")
                },
                    Permissions =
                {
                    Permissions.Endpoints.Authorization,
                    Permissions.Endpoints.Token,
                    Permissions.GrantTypes.AuthorizationCode,
                    Permissions.GrantTypes.RefreshToken,
                    Permissions.Scopes.Email,
                    Permissions.Scopes.Profile,
                    Permissions.Scopes.Roles,
                    Permissions.ResponseTypes.Code
                }
                });
            }
        }

        static async Task RegisterScopesAsync(IServiceProvider provider)
        {
            var manager = provider.GetRequiredService<IOpenIddictScopeManager>();

            if (await manager.FindByNameAsync("dataEventRecords") is null)
            {
                await manager.CreateAsync(new OpenIddictScopeDescriptor
                {
                    DisplayName = "Application API access",
                    DisplayNames =
                    {
                        [CultureInfo.GetCultureInfo("fr-FR")] = "Application API access"
                    },
                    Name = "dataEventRecords",
                    Resources =
                    {
                        "rs_dataEventRecordsApi"
                    }
                });
            }
        }
        private static OpenIddictApplicationDescriptor AddRedirectUris(
    OpenIddictApplicationDescriptor descriptor,
    IEnumerable<Uri> redirectUris,
    IEnumerable<Uri> postLogoutRedirectUris)
        {
            foreach (var uri in redirectUris)
            {
                descriptor.RedirectUris.Add(uri);
            }

            foreach (var uri in postLogoutRedirectUris)
            {
                descriptor.PostLogoutRedirectUris.Add(uri);
            }

            return descriptor;
        }
        public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;

    }
}