﻿"restore":{"projectUniqueName":"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Test\\Application.Test.csproj","projectName":"Application.Test","projectPath":"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Test\\Application.Test.csproj","outputPath":"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Test\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net9.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"MSTest.Analyzers":{"target":"Package","version":"[3.6.4, )","versionOverride":"[3.6.4, )"},"MSTest.TestAdapter":{"target":"Package","version":"[3.6.4, )","versionOverride":"[3.6.4, )"},"MSTest.TestFramework":{"target":"Package","version":"[3.6.4, )","versionOverride":"[3.6.4, )"},"Microsoft.NET.Test.Sdk":{"target":"Package","version":"[17.11.1, )","versionOverride":"[17.11.1, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}