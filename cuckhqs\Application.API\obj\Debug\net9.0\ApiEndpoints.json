[{"ContainingType": "Application.API.Controllers.AcademicRankController", "Method": "CreateAcademicRankAsync", "RelativePath": "api/AcademicRank/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.AcademicRank.CreateAcademicRankRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.AcademicRankController", "Method": "DeleteAcademicRankAsync", "RelativePath": "api/AcademicRank/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.AcademicRank.DeleteAcademicRankRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.AcademicRankController", "Method": "SearchAcademicRankAsync", "RelativePath": "api/AcademicRank/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.AcademicRank.SearchAcademicRankRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.AcademicRankController", "Method": "UpdateAcademicRankAsync", "RelativePath": "api/AcademicRank/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.AcademicRank.UpdateAcademicRankRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.AdvertisementController", "Method": "CreateAdvertisementAsync", "RelativePath": "api/Advertisement/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Advertisement.CreateAdvertisementRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.AdvertisementController", "Method": "DeleteAdvertisementAsync", "RelativePath": "api/Advertisement/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Advertisement.DeleteAdvertisementRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.AdvertisementController", "Method": "SearchAdvertisementAsync", "RelativePath": "api/Advertisement/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Advertisement.SearchAdvertisementRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.AdvertisementController", "Method": "UpdateAdvertisementAsync", "RelativePath": "api/Advertisement/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Advertisement.UpdateAdvertisementRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ql_tb_vk_vt.API.Controllers.BaoCao.BcMauBaoCaoTuanController", "Method": "ExportToWord", "RelativePath": "api/BcMauBaoCaoTuan/ExportToWord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "jsonResult", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ql_tb_vk_vt.API.Controllers.BaoCao.BcMauBaoCaoTuanController", "Method": "GetResource", "RelativePath": "api/BcMauBaoCaoTuan/GetResource", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": false}, {"Name": "resourcetype", "Type": "System.String", "IsRequired": false}, {"Name": "isPrint", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ql_tb_vk_vt.API.Controllers.BaoCao.BcMauBaoCaoTuanController", "Method": "PostFormReportAction", "RelativePath": "api/BcMauBaoCaoTuan/PostFormReportAction", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ql_tb_vk_vt.API.Controllers.BaoCao.BcMauBaoCaoTuanController", "Method": "PostReportAction", "RelativePath": "api/BcMauBaoCaoTuan/PostReportAction", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "jsonResult", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.CountryController", "Method": "CreateCountryAsync", "RelativePath": "api/Country/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Country.CreateCountryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.CountryController", "Method": "DeleteCountryAsync", "RelativePath": "api/Country/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Country.DeleteCountryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.CountryController", "Method": "SearchCountryAsync", "RelativePath": "api/Country/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Country.SearchCountryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.CountryController", "Method": "UpdateCountryAsync", "RelativePath": "api/Country/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Country.UpdateCountryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DecisionLevelController", "Method": "CreateDecisionLevelAsync", "RelativePath": "api/DecisionLevel/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.DecisionLevel.CreateDecisionLevelRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DecisionLevelController", "Method": "DeleteDecisionLevelAsync", "RelativePath": "api/DecisionLevel/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.DecisionLevel.DeleteDecisionLevelRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DecisionLevelController", "Method": "SearchDecisionLevelAsync", "RelativePath": "api/DecisionLevel/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.DecisionLevel.SearchDecisionLevelRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DecisionLevelController", "Method": "UpdateDecisionLevelAsync", "RelativePath": "api/DecisionLevel/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.DecisionLevel.UpdateDecisionLevelRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DegreeController", "Method": "CreateDegreeAsync", "RelativePath": "api/Degree/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Degree.CreateDegreeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DegreeController", "Method": "DeleteDegreeAsync", "RelativePath": "api/Degree/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Degree.DeleteDegreeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DegreeController", "Method": "SearchDegreeAsync", "RelativePath": "api/Degree/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Degree.SearchDegreeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DegreeController", "Method": "UpdateDegreeAsync", "RelativePath": "api/Degree/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Degree.UpdateDegreeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DepartmentController", "Method": "UpdateStatusByIdsAsync", "RelativePath": "api/Department/confirm-status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.UpdateStatusByIdsIntRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DepartmentController", "Method": "CreateStaffPositonAsync", "RelativePath": "api/Department/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.CreateDepartmentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DepartmentController", "Method": "DeleteStaffPositonAsync", "RelativePath": "api/Department/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "Application.Infrastructure.Models.Request.DeleteDepartmentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DepartmentController", "Method": "SearchDepartmentAsync", "RelativePath": "api/Department/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.SearchDepartmentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DepartmentController", "Method": "UpdateStaffPositonAsync", "RelativePath": "api/Department/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.UpdateDepartmentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DisciplineTypeController", "Method": "CreateDisciplineTypeAsync", "RelativePath": "api/DisciplineType/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.DisciplineType.CreateDisciplineTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DisciplineTypeController", "Method": "DeleteDisciplineTypeAsync", "RelativePath": "api/DisciplineType/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.DisciplineType.DeleteDisciplineTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DisciplineTypeController", "Method": "SearchDisciplineTypeAsync", "RelativePath": "api/DisciplineType/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.DisciplineType.SearchDisciplineTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DisciplineTypeController", "Method": "UpdateDisciplineTypeAsync", "RelativePath": "api/DisciplineType/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.DisciplineType.UpdateDisciplineTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DistrictController", "Method": "CreateDistrictAsync", "RelativePath": "api/District/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.District.CreateDistrictRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DistrictController", "Method": "DeleteDistrictAsync", "RelativePath": "api/District/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.District.DeleteDistrictRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DistrictController", "Method": "SearchDistrictAsync", "RelativePath": "api/District/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.District.SearchDistrictRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.DistrictController", "Method": "UpdateDistrictAsync", "RelativePath": "api/District/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.District.UpdateDistrictRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EducationLevelController", "Method": "CreateEducationLevelAsync", "RelativePath": "api/EducationLevel/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.EducationLevel.CreateEducationLevelRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EducationLevelController", "Method": "DeleteEducationLevelAsync", "RelativePath": "api/EducationLevel/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.EducationLevel.DeleteEducationLevelRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EducationLevelController", "Method": "SearchEducationLevelAsync", "RelativePath": "api/EducationLevel/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.EducationLevel.SearchEducationLevelRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EducationLevelController", "Method": "UpdateEducationLevelAsync", "RelativePath": "api/EducationLevel/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.EducationLevel.UpdateEducationLevelRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EmployeeController", "Method": "CreateEmployeeAsync", "RelativePath": "api/Employee/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Employee.CreateEmployeeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EmployeeController", "Method": "DeleteEmployeeAsync", "RelativePath": "api/Employee/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Employee.DeleteEmployeeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EmployeeController", "Method": "GetAlDepartmentHeadAsync", "RelativePath": "api/Employee/department-head", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EmployeeController", "Method": "GetAllEmployeesAsync", "RelativePath": "api/Employee/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EmployeeController", "Method": "GetEmployeeById", "RelativePath": "api/Employee/GetEmployeeById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EmployeeController", "Method": "SearchEmployeeAsync", "RelativePath": "api/Employee/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Employee.SearchEmployeeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.EmployeeController", "Method": "UpdateEmployeeAsync", "RelativePath": "api/Employee/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Employee.UpdateEmployeeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.FileController", "Method": "DownloadFile", "RelativePath": "api/File/download-by-filename/{filename}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "filename", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.FileController", "Method": "UploadFile", "RelativePath": "api/File/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalController", "Method": "CreateJournalAsync", "RelativePath": "api/Journal/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Journal.CreateJournalRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalController", "Method": "DeleteJournalAsync", "RelativePath": "api/Journal/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Journal.DeleteJournalRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalController", "Method": "SearchJournalAsync", "RelativePath": "api/Journal/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Journal.SearchJournalRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalController", "Method": "UpdateJournalAsync", "RelativePath": "api/Journal/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Journal.UpdateJournalRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalGroupController", "Method": "CreateJournalTypeAsync", "RelativePath": "api/JournalGroup/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.JournalGroup.CreateJournalGroupRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalGroupController", "Method": "DeleteJournalTypeAsync", "RelativePath": "api/JournalGroup/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.JournalGroup.DeleteJournalGroupRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalGroupController", "Method": "SearchJournalTypeAsync", "RelativePath": "api/JournalGroup/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.JournalGroup.SearchJournalGroupRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalGroupController", "Method": "UpdateJournalTypeAsync", "RelativePath": "api/JournalGroup/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.JournalGroup.UpdateJournalGroupRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalTypeController", "Method": "CreateJournalTypeAsync", "RelativePath": "api/JournalType/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.JournalType.CreateJournalTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalTypeController", "Method": "DeleteJournalTypeAsync", "RelativePath": "api/JournalType/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.JournalType.DeleteJournalTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalTypeController", "Method": "SearchJournalTypeAsync", "RelativePath": "api/JournalType/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.JournalType.SearchJournalTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.JournalTypeController", "Method": "UpdateJournalTypeAsync", "RelativePath": "api/JournalType/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.JournalType.UpdateJournalTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OnDutyCommandController", "Method": "CreateOnDutyCommandAsync", "RelativePath": "api/OnDutyCommand/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.OnDutyCommand.CreateOnDutyCommandRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OnDutyCommandController", "Method": "SearchOnDutyCommandAsync", "RelativePath": "api/OnDutyCommand/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.OnDutyCommand.SearchOnDutyCommandRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OnDutyCommandController", "Method": "UpdateOnDutyCommandAsync", "RelativePath": "api/OnDutyCommand/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.OnDutyCommand.UpdateOnDutyCommandRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OrganizationUnitController", "Method": "CreateOrganizationUnitAsync", "RelativePath": "api/OrganizationUnit/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.OrganizationUnit.CreateOrganizationUnitRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OrganizationUnitController", "Method": "DeleteOrganizationUnitAsync", "RelativePath": "api/OrganizationUnit/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.OrganizationUnit.DeleteOrganizationUnitRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OrganizationUnitController", "Method": "GetAllOrganizationUnitAsync", "RelativePath": "api/OrganizationUnit/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OrganizationUnitController", "Method": "OrganizationUnitCascadeEmployee", "RelativePath": "api/OrganizationUnit/get-people-Receiced", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OrganizationUnitController", "Method": "GetOrganizationUnitTreeAsync", "RelativePath": "api/OrganizationUnit/get-tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OrganizationUnitController", "Method": "GetOrganizationUnitById", "RelativePath": "api/OrganizationUnit/GetOrganizationUnitById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OrganizationUnitController", "Method": "SearchOrganizationUnitAsync", "RelativePath": "api/OrganizationUnit/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.OrganizationUnit.SearchOrganizationUnitRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.OrganizationUnitController", "Method": "UpdateWorkingResultAsync", "RelativePath": "api/OrganizationUnit/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.OrganizationUnit.UpdateOrganizationUnitRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.PermissionController", "Method": "Update", "RelativePath": "api/Permission", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Application.Infrastructure.Models.PermissionViewResponse", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.PermissionController", "Method": "SearchByRoleId", "RelativePath": "api/Permission/{roleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.PositionController", "Method": "CreatePositionAsync", "RelativePath": "api/Position/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.OrganizationUnit.CreatePositionRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.PositionController", "Method": "DeletePositionAsync", "RelativePath": "api/Position/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Position.DeletePositionRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.PositionController", "Method": "GetAllPositionAsync", "RelativePath": "api/Position/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.PositionController", "Method": "GetPositionBuildTreeAsync", "RelativePath": "api/Position/get-tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.PositionController", "Method": "GetPositionById", "RelativePath": "api/Position/GetPositionById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.PositionController", "Method": "SearchPositionAsync", "RelativePath": "api/Position/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Position.SearchPositionRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.PositionController", "Method": "UpdatePositionAsync", "RelativePath": "api/Position/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Position.UpdatePositionRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.ProvinceController", "Method": "CreateProvinceAsync", "RelativePath": "api/Province/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Province.CreateProvinceRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.ProvinceController", "Method": "DeleteProvinceAsync", "RelativePath": "api/Province/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Province.DeleteProvinceRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.ProvinceController", "Method": "SearchProvinceAsync", "RelativePath": "api/Province/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Province.SearchProvinceRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.ProvinceController", "Method": "UpdateProvinceAsync", "RelativePath": "api/Province/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Province.UpdateProvinceRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RankController", "Method": "CreateRankAsync", "RelativePath": "api/Rank/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Rank.CreateRankRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RankController", "Method": "DeleteRankAsync", "RelativePath": "api/Rank/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Rank.DeleteRankRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RankController", "Method": "SearchRankAsync", "RelativePath": "api/Rank/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Rank.SearchRankRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RankController", "Method": "UpdateRankAsync", "RelativePath": "api/Rank/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Rank.UpdateRankRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RewardTypeController", "Method": "CreateRewardTypeAsync", "RelativePath": "api/RewardType/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.RewardType.CreateRewardTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RewardTypeController", "Method": "DeleteRewardTypeAsync", "RelativePath": "api/RewardType/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.RewardType.DeleteRewardTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RewardTypeController", "Method": "SearchRewardTypeAsync", "RelativePath": "api/RewardType/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.RewardType.SearchRewardTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RewardTypeController", "Method": "UpdateRewardTypeAsync", "RelativePath": "api/RewardType/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.RewardType.UpdateRewardTypeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RolesController", "Method": "Get", "RelativePath": "api/Roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RolesController", "Method": "AddRole", "RelativePath": "api/Roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.RolesController", "Method": "UpdateRole", "RelativePath": "api/Roles/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "roleRequest", "Type": "Microsoft.AspNetCore.Identity.IdentityRole", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.ScheduleController", "Method": "CreateScheduleAsync", "RelativePath": "api/Schedule/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Schedule.CreateScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.ScheduleController", "Method": "DeleteScheduleAsync", "RelativePath": "api/Schedule/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Schedule.DeleteScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.ScheduleController", "Method": "SearchScheduleAsync", "RelativePath": "api/Schedule/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Schedule.SearchScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.ScheduleController", "Method": "UpdateScheduleAsync", "RelativePath": "api/Schedule/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Schedule.UpdateScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.SoSController", "Method": "CreateSoSAsync", "RelativePath": "api/SoS/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.SoS.CreateSoSRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.SoSController", "Method": "DeleteSoSAsync", "RelativePath": "api/SoS/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.SoS.DeleteSoSRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.SoSController", "Method": "GetAllSoSAsync", "RelativePath": "api/SoS/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.SoSController", "Method": "GetSoSBuildTreeAsync", "RelativePath": "api/SoS/get-tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.SoSController", "Method": "GetSoSById", "RelativePath": "api/SoS/GetPositionById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.SoSController", "Method": "SearchSoSAsync", "RelativePath": "api/SoS/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.SoS.SearchSoSRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.SoSController", "Method": "UpdateSoSAsync", "RelativePath": "api/SoS/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.SoS.UpdateSoSRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.TestController", "Method": "TestSendMail", "RelativePath": "api/Test/SendTestMail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "text", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.TestController", "Method": "TestNotification", "RelativePath": "api/Test/TestNotification", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "text", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.UserRolesController", "Method": "Update", "RelativePath": "api/UserRoles", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "UserRoles", "Type": "System.Collections.Generic.IList`1[[Application.Infrastructure.Models.Response.Identity.UserRolesResponse, Application.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.UserRolesController", "Method": "GetUserRoleById", "RelativePath": "api/UserRoles/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.UsersController", "Method": "GetUserAsync", "RelativePath": "api/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.UsersController", "Method": "UpdateUserAsync", "RelativePath": "api/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "model", "Type": "Application.Infrastructure.Models.Request.UpdateUserModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.UsersController", "Method": "CreateUserAsync", "RelativePath": "api/Users/<USER>", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Application.Infrastructure.Models.Request.CreateUserModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.UsersController", "Method": "SearchUsersAsync", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WardController", "Method": "CreateWardAsync", "RelativePath": "api/Ward/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Ward.CreateWardRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WardController", "Method": "DeleteWardAsync", "RelativePath": "api/Ward/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Ward.DeleteWardRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WardController", "Method": "SearchWardAsync", "RelativePath": "api/Ward/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Ward.SearchWardRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WardController", "Method": "UpdateWardAsync", "RelativePath": "api/Ward/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Category.Ward.UpdateWardRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingResultController", "Method": "CreateWorkingResultAsync", "RelativePath": "api/WorkingResult/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "System.Collections.Generic.List`1[[Application.Infrastructure.Models.Request.Advertisement.CreateWorkingResultRequest, Application.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingResultController", "Method": "DeleteWorkingResultAsync", "RelativePath": "api/WorkingResult/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingResult.DeleteWorkingResultRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingResultController", "Method": "SearchWorkingResultAsync", "RelativePath": "api/WorkingResult/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingResult.SearchWorkingResultRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingResultController", "Method": "UpdateWorkingResultAsync", "RelativePath": "api/WorkingResult/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.Advertisement.UpdateWorkingResultRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "CheckDuplicateSchedule", "RelativePath": "api/WorkingSchedule/check-duplicate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingSchedule.CreateWorkingScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "CreateWorkingSchedule", "RelativePath": "api/WorkingSchedule/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingSchedule.CreateWorkingScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "CreateWorkingScheduleAnnouncedAsync", "RelativePath": "api/WorkingSchedule/create-announced", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingSchedule.WorkingScheduleIssueAnnouncedRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "DeleteWorkingScheduleAsync", "RelativePath": "api/WorkingSchedule/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingResult.DeleteWorkingScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "DownloadWordSchedule", "RelativePath": "api/WorkingSchedule/download-word", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingSchedule.SearchWorkingScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "ExportPdf", "RelativePath": "api/WorkingSchedule/export-pdf", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingSchedule.SearchWorkingScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "GetPlaceAsync", "RelativePath": "api/WorkingSchedule/get-place", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "GetWorkingScheduleById", "RelativePath": "api/WorkingSchedule/GetWorkingScheduleById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "SearchProcurementSourcesAsync", "RelativePath": "api/WorkingSchedule/Search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingSchedule.SearchWorkingScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "SearchWorkingScheduleAnnouncedAsync", "RelativePath": "api/WorkingSchedule/search-announced", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingResult.SearchWorkingScheduleAnnouncedRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleController", "Method": "UpdateAsync", "RelativePath": "api/WorkingSchedule/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingSchedule.UpdateWorkingScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleResultController", "Method": "CreateWorkingScheduleResult", "RelativePath": "api/WorkingScheduleResult/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingScheduleResult.CreateWorkingScheduleResultRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingScheduleResultController", "Method": "GetWorkingScheduleResultByWorkingScheduleId", "RelativePath": "api/WorkingScheduleResult/GetWorkingScheduleResultByWorkingScheduleId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "WorkingScheduleId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingResultSyntheticController", "Method": "CreateWorkingResultSyntheticAsync", "RelativePath": "api/wrs/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingResultSynthetic.CreateWorkingResultSyntheticRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingResultSyntheticController", "Method": "DeleteWorkingResultSyntheticAsync", "RelativePath": "api/wrs/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingResultSynthetic.DeleteWorkingResultSyntheticRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingResultSyntheticController", "Method": "SearchWorkingResultSyntheticAsync", "RelativePath": "api/wrs/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingResultSynthetic.SearchWorkingResultSyntheticRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Application.API.Controllers.WorkingResultSyntheticController", "Method": "UpdateWorkingResultSyntheticAsync", "RelativePath": "api/wrs/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.Infrastructure.Models.Request.WorkingResultSynthetic.UpdateWorkingResultSyntheticRequest", "IsRequired": true}], "ReturnTypes": []}]