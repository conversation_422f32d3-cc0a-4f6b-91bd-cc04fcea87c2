﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category.Rank;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;

namespace Application.Infrastructure.Services.Implementations
{
    public class RankService : IRankService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public RankService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<RankResponse>> SearchRankAsync(SearchRankRequest request)
        {
            try
            {
                IQueryable<RankResponse> query = _unitOfWork.Rank.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.RankName.Contains(request.keyword) ||
                                x.RankCode.Contains(request.keyword)))
                    .Select(s => new RankResponse()
                    {
                        Id = s.Id,
                        RankCode = s.RankCode,
                        RankName = s.RankName,
                        Description = s.Description,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                    });

                return await BaseSearchResponse<RankResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<RankResponse> CreateRankAsync(CreateRankRequest request)
        {
            try
            {
                var entity = CreateRankRequest.Create(request);

                await _unitOfWork.Rank.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return RankResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteRankAsync(DeleteRankRequest request)
        {
            try
            {
                var record = await _unitOfWork.Rank.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.Rank.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateRankAsync(UpdateRankRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.Rank.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateRankRequest.Create(request);

                await _unitOfWork.Rank.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
