﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.BaoCao;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Services.Abstractions;
using ExcelDataReader.Log;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class ReportProductService : IReportProductService
    {
        private readonly IConfiguration _configuration;
        private readonly IUserContext _userContext;
        public ReportProductService(IConfiguration configuration, IUserContext userContext)
        {
            _configuration = configuration;
            _userContext = userContext;
        }
        public async Task<List<BCMauBaoCaoTuanResponse>> Bc2MauBaoCaoTuan(MauBaoCaoTuanRequest request)
        {
            try
            {
                var paramList = new List<SqlParameter>
                {
                    new SqlParameter("@tuan", request.tuan),
                    new SqlParameter("@nam", request.nam),
                    new SqlParameter("@don_vi", request.don_vi)
                };
                var lis = await DatabaseSql.ExecuteProcToList<BCMauBaoCaoTuanResponse>("bc_mau_bao_cao_tuan", paramList, _configuration);
                return lis.ToList(); // Hoặc chỉ return lis nếu không cần ToList()
            }
            catch (Exception ex)
            {
                //log.Error("Lỗi tại Bc2MauBaoCaoTuan: " + ex.ToString());
                throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
            }
        }
    }
}
