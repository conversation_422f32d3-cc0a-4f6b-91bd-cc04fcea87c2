{"openapi": "3.0.1", "info": {"title": "ApiPlayground", "version": "v1"}, "paths": {"/api/AcademicRank/Search": {"post": {"tags": ["AcademicRank"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchAcademicRankRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchAcademicRankRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchAcademicRankRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/AcademicRank/Create": {"post": {"tags": ["AcademicRank"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAcademicRankRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAcademicRankRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAcademicRankRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/AcademicRank/Update": {"put": {"tags": ["AcademicRank"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAcademicRankRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateAcademicRankRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateAcademicRankRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/AcademicRank/Delete": {"delete": {"tags": ["AcademicRank"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteAcademicRankRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteAcademicRankRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteAcademicRankRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advertisement/Search": {"post": {"tags": ["Advertisement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchAdvertisementRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchAdvertisementRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchAdvertisementRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advertisement/Create": {"post": {"tags": ["Advertisement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdvertisementRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAdvertisementRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAdvertisementRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advertisement/Update": {"put": {"tags": ["Advertisement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAdvertisementRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateAdvertisementRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateAdvertisementRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advertisement/Delete": {"delete": {"tags": ["Advertisement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteAdvertisementRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteAdvertisementRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteAdvertisementRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/BcMauBaoCaoTuan/PostReportAction": {"post": {"tags": ["BcMauBaoCaoTuan"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/BcMauBaoCaoTuan/GetResource": {"get": {"tags": ["BcMauBaoCaoTuan"], "parameters": [{"name": "key", "in": "query", "schema": {"type": "string"}}, {"name": "resourcetype", "in": "query", "schema": {"type": "string"}}, {"name": "isPrint", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/BcMauBaoCaoTuan/PostFormReportAction": {"post": {"tags": ["BcMauBaoCaoTuan"], "responses": {"200": {"description": "OK"}}}}, "/api/BcMauBaoCaoTuan/ExportToWord": {"post": {"tags": ["BcMauBaoCaoTuan"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Country/Search": {"post": {"tags": ["Country"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchCountryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchCountryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchCountryRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Country/Create": {"post": {"tags": ["Country"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCountryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCountryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCountryRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Country/Update": {"put": {"tags": ["Country"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCountryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCountryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCountryRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Country/Delete": {"delete": {"tags": ["Country"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteCountryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteCountryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteCountryRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/DecisionLevel/Search": {"post": {"tags": ["DecisionLevel"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchDecisionLevelRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchDecisionLevelRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchDecisionLevelRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/DecisionLevel/Create": {"post": {"tags": ["DecisionLevel"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDecisionLevelRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDecisionLevelRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDecisionLevelRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/DecisionLevel/Update": {"put": {"tags": ["DecisionLevel"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDecisionLevelRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDecisionLevelRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDecisionLevelRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/DecisionLevel/Delete": {"delete": {"tags": ["DecisionLevel"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDecisionLevelRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteDecisionLevelRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteDecisionLevelRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Degree/Search": {"post": {"tags": ["Degree"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchDegreeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchDegreeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchDegreeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Degree/Create": {"post": {"tags": ["Degree"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDegreeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDegreeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDegreeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Degree/Update": {"put": {"tags": ["Degree"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDegreeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDegreeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDegreeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Degree/Delete": {"delete": {"tags": ["Degree"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDegreeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteDegreeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteDegreeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Department/search": {"post": {"tags": ["Department"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchDepartmentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchDepartmentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchDepartmentRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Department/Create": {"post": {"tags": ["Department"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Department/Update": {"put": {"tags": ["Department"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Department/Delete": {"delete": {"tags": ["Department"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDepartmentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteDepartmentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteDepartmentRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Department/confirm-status": {"post": {"tags": ["Department"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusByIdsIntRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusByIdsIntRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStatusByIdsIntRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/DisciplineType/Search": {"post": {"tags": ["DisciplineType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchDisciplineTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchDisciplineTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchDisciplineTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/DisciplineType/Create": {"post": {"tags": ["DisciplineType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDisciplineTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDisciplineTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDisciplineTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/DisciplineType/Update": {"put": {"tags": ["DisciplineType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDisciplineTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDisciplineTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDisciplineTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/DisciplineType/Delete": {"delete": {"tags": ["DisciplineType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDisciplineTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteDisciplineTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteDisciplineTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/District/Search": {"post": {"tags": ["District"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchDistrictRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchDistrictRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchDistrictRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/District/Create": {"post": {"tags": ["District"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDistrictRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDistrictRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDistrictRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/District/Update": {"put": {"tags": ["District"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDistrictRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDistrictRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDistrictRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/District/Delete": {"delete": {"tags": ["District"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDistrictRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteDistrictRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteDistrictRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/EducationLevel/Search": {"post": {"tags": ["EducationLevel"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchEducationLevelRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchEducationLevelRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchEducationLevelRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/EducationLevel/Create": {"post": {"tags": ["EducationLevel"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEducationLevelRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateEducationLevelRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateEducationLevelRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/EducationLevel/Update": {"put": {"tags": ["EducationLevel"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEducationLevelRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateEducationLevelRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateEducationLevelRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/EducationLevel/Delete": {"delete": {"tags": ["EducationLevel"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteEducationLevelRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteEducationLevelRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteEducationLevelRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Employee/department-head": {"get": {"tags": ["Employee"], "responses": {"200": {"description": "OK"}}}}, "/api/Employee/get-all": {"get": {"tags": ["Employee"], "responses": {"200": {"description": "OK"}}}}, "/api/Employee/Search": {"post": {"tags": ["Employee"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchEmployeeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchEmployeeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchEmployeeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Employee/GetEmployeeById": {"get": {"tags": ["Employee"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Employee/Create": {"post": {"tags": ["Employee"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEmployeeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateEmployeeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateEmployeeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Employee/Update": {"put": {"tags": ["Employee"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEmployeeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateEmployeeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateEmployeeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Employee/Delete": {"delete": {"tags": ["Employee"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteEmployeeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteEmployeeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteEmployeeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/File/upload": {"post": {"tags": ["File"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/File/download-by-filename/{filename}": {"get": {"tags": ["File"], "parameters": [{"name": "filename", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Journal/Search": {"post": {"tags": ["Journal"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchJournalRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchJournalRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchJournalRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Journal/Create": {"post": {"tags": ["Journal"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJournalRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateJournalRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateJournalRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Journal/Update": {"put": {"tags": ["Journal"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateJournalRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateJournalRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateJournalRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Journal/Delete": {"delete": {"tags": ["Journal"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteJournalRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteJournalRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteJournalRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/JournalGroup/Search": {"post": {"tags": ["JournalGroup"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchJournalGroupRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchJournalGroupRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchJournalGroupRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/JournalGroup/Create": {"post": {"tags": ["JournalGroup"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJournalGroupRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateJournalGroupRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateJournalGroupRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/JournalGroup/Update": {"put": {"tags": ["JournalGroup"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateJournalGroupRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateJournalGroupRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateJournalGroupRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/JournalGroup/Delete": {"delete": {"tags": ["JournalGroup"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteJournalGroupRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteJournalGroupRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteJournalGroupRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/JournalType/Search": {"post": {"tags": ["JournalType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchJournalTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchJournalTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchJournalTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/JournalType/Create": {"post": {"tags": ["JournalType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJournalTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateJournalTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateJournalTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/JournalType/Update": {"put": {"tags": ["JournalType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateJournalTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateJournalTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateJournalTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/JournalType/Delete": {"delete": {"tags": ["JournalType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteJournalTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteJournalTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteJournalTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/OnDutyCommand/Search": {"post": {"tags": ["OnDutyCommand"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchOnDutyCommandRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchOnDutyCommandRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchOnDutyCommandRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/OnDutyCommand/Create": {"post": {"tags": ["OnDutyCommand"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOnDutyCommandRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOnDutyCommandRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOnDutyCommandRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/OnDutyCommand/Update": {"put": {"tags": ["OnDutyCommand"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOnDutyCommandRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOnDutyCommandRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOnDutyCommandRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/OrganizationUnit/get-all": {"get": {"tags": ["OrganizationUnit"], "responses": {"200": {"description": "OK"}}}}, "/api/OrganizationUnit/get-tree": {"get": {"tags": ["OrganizationUnit"], "responses": {"200": {"description": "OK"}}}}, "/api/OrganizationUnit/get-people-Receiced": {"get": {"tags": ["OrganizationUnit"], "responses": {"200": {"description": "OK"}}}}, "/api/OrganizationUnit/Search": {"post": {"tags": ["OrganizationUnit"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchOrganizationUnitRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchOrganizationUnitRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchOrganizationUnitRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/OrganizationUnit/GetOrganizationUnitById": {"get": {"tags": ["OrganizationUnit"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/OrganizationUnit/Create": {"post": {"tags": ["OrganizationUnit"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrganizationUnitRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOrganizationUnitRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOrganizationUnitRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/OrganizationUnit/Update": {"put": {"tags": ["OrganizationUnit"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrganizationUnitRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOrganizationUnitRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOrganizationUnitRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/OrganizationUnit/Delete": {"delete": {"tags": ["OrganizationUnit"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteOrganizationUnitRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteOrganizationUnitRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteOrganizationUnitRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Permission/{roleId}": {"get": {"tags": ["Permission"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Permission": {"put": {"tags": ["Permission"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionViewResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionViewResponse"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PermissionViewResponse"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Position/get-all": {"get": {"tags": ["Position"], "responses": {"200": {"description": "OK"}}}}, "/api/Position/get-tree": {"get": {"tags": ["Position"], "responses": {"200": {"description": "OK"}}}}, "/api/Position/Search": {"post": {"tags": ["Position"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchPositionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchPositionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchPositionRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Position/GetPositionById": {"get": {"tags": ["Position"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Position/Create": {"post": {"tags": ["Position"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePositionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePositionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePositionRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Position/Update": {"put": {"tags": ["Position"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePositionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePositionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePositionRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Position/Delete": {"delete": {"tags": ["Position"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeletePositionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeletePositionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeletePositionRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Province/Search": {"post": {"tags": ["Province"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchProvinceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchProvinceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchProvinceRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Province/Create": {"post": {"tags": ["Province"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProvinceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProvinceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProvinceRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Province/Update": {"put": {"tags": ["Province"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProvinceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProvinceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProvinceRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Province/Delete": {"delete": {"tags": ["Province"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteProvinceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteProvinceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteProvinceRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Rank/Search": {"post": {"tags": ["Rank"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchRankRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchRankRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchRankRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Rank/Create": {"post": {"tags": ["Rank"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRankRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRankRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRankRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Rank/Update": {"put": {"tags": ["Rank"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRankRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRankRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRankRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Rank/Delete": {"delete": {"tags": ["Rank"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteRankRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteRankRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteRankRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RewardType/Search": {"post": {"tags": ["RewardType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchRewardTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchRewardTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchRewardTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RewardType/Create": {"post": {"tags": ["RewardType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRewardTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRewardTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRewardTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RewardType/Update": {"put": {"tags": ["RewardType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRewardTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRewardTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRewardTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RewardType/Delete": {"delete": {"tags": ["RewardType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteRewardTypeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteRewardTypeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteRewardTypeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Roles": {"get": {"tags": ["Roles"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Roles"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Roles/{id}": {"put": {"tags": ["Roles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdentityRole"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IdentityRole"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/IdentityRole"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Schedule/Search": {"post": {"tags": ["Schedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Schedule/Create": {"post": {"tags": ["Schedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Schedule/Delete": {"delete": {"tags": ["Schedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Schedule/Update": {"put": {"tags": ["Schedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/SoS/get-all": {"get": {"tags": ["SoS"], "responses": {"200": {"description": "OK"}}}}, "/api/SoS/get-tree": {"get": {"tags": ["SoS"], "responses": {"200": {"description": "OK"}}}}, "/api/SoS/Search": {"post": {"tags": ["SoS"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchSoSRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchSoSRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchSoSRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/SoS/GetPositionById": {"get": {"tags": ["SoS"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/SoS/Create": {"post": {"tags": ["SoS"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSoSRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateSoSRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateSoSRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/SoS/Update": {"put": {"tags": ["SoS"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSoSRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateSoSRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateSoSRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/SoS/Delete": {"delete": {"tags": ["SoS"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteSoSRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteSoSRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteSoSRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Test/SendTestMail": {"post": {"tags": ["Test"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Test/TestNotification": {"post": {"tags": ["Test"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/UserRoles/{userId}": {"get": {"tags": ["UserRoles"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserRoles": {"put": {"tags": ["UserRoles"], "parameters": [{"name": "UserId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "UserRoles", "in": "query", "required": true, "schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserRolesResponse"}}}], "responses": {"200": {"description": "OK"}}}}, "/api/Users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK"}}}}, "/api/Users/<USER>": {"put": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Ward/Search": {"post": {"tags": ["Ward"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchWardRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchWardRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchWardRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Ward/Create": {"post": {"tags": ["Ward"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWardRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateWardRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateWardRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Ward/Update": {"put": {"tags": ["Ward"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWardRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateWardRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateWardRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Ward/Delete": {"delete": {"tags": ["Ward"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteWardRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteWardRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteWardRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingResult/Search": {"post": {"tags": ["WorkingResult"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingResultRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingResultRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchWorkingResultRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingResult/Create": {"post": {"tags": ["WorkingResult"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateWorkingResultRequest"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateWorkingResultRequest"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateWorkingResultRequest"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingResult/Delete": {"delete": {"tags": ["WorkingResult"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteWorkingResultRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteWorkingResultRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteWorkingResultRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingResult/Update": {"put": {"tags": ["WorkingResult"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWorkingResultRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateWorkingResultRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateWorkingResultRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/wrs/search": {"post": {"tags": ["WorkingResultSynthetic"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingResultSyntheticRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingResultSyntheticRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchWorkingResultSyntheticRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/wrs/create": {"post": {"tags": ["WorkingResultSynthetic"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWorkingResultSyntheticRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateWorkingResultSyntheticRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateWorkingResultSyntheticRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/wrs/update": {"put": {"tags": ["WorkingResultSynthetic"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWorkingResultSyntheticRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateWorkingResultSyntheticRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateWorkingResultSyntheticRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/wrs/delete": {"delete": {"tags": ["WorkingResultSynthetic"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteWorkingResultSyntheticRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteWorkingResultSyntheticRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteWorkingResultSyntheticRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/Create": {"post": {"tags": ["WorkingSchedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWorkingScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateWorkingScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateWorkingScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/Search": {"post": {"tags": ["WorkingSchedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/GetWorkingScheduleById": {"get": {"tags": ["WorkingSchedule"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/Update": {"put": {"tags": ["WorkingSchedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWorkingScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateWorkingScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateWorkingScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/Delete": {"delete": {"tags": ["WorkingSchedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteWorkingScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteWorkingScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteWorkingScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/get-place": {"get": {"tags": ["WorkingSchedule"], "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/download-word": {"post": {"tags": ["WorkingSchedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/export-pdf": {"post": {"tags": ["WorkingSchedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/search-announced": {"post": {"tags": ["WorkingSchedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleAnnouncedRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleAnnouncedRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchWorkingScheduleAnnouncedRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/create-announced": {"post": {"tags": ["WorkingSchedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkingScheduleIssueAnnouncedRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkingScheduleIssueAnnouncedRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkingScheduleIssueAnnouncedRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingSchedule/check-duplicate": {"post": {"tags": ["WorkingSchedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWorkingScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateWorkingScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateWorkingScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingScheduleResult/Create": {"post": {"tags": ["WorkingScheduleResult"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWorkingScheduleResultRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateWorkingScheduleResultRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateWorkingScheduleResultRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkingScheduleResult/GetWorkingScheduleResultByWorkingScheduleId": {"get": {"tags": ["WorkingScheduleResult"], "parameters": [{"name": "WorkingScheduleId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"CreateAcademicRankRequest": {"type": "object", "properties": {"academicRankCode": {"type": "string", "nullable": true}, "academicRankName": {"type": "string", "nullable": true}, "academicRankShortName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateAdvertisementRequest": {"type": "object", "properties": {"advertisementCode": {"type": "string", "nullable": true}, "advertisementName": {"type": "string", "nullable": true}, "start": {"type": "string", "format": "date-time", "nullable": true}, "startHour": {"type": "integer", "format": "int32", "nullable": true}, "startMinute": {"type": "integer", "format": "int32", "nullable": true}, "ends": {"type": "string", "format": "date-time", "nullable": true}, "endsHour": {"type": "integer", "format": "int32", "nullable": true}, "endsMinute": {"type": "integer", "format": "int32", "nullable": true}, "year": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateCountryRequest": {"type": "object", "properties": {"countryCode": {"type": "string", "nullable": true}, "countryName": {"type": "string", "nullable": true}, "languageName": {"type": "string", "nullable": true}, "vietnameseName": {"type": "string", "nullable": true}, "class": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateDecisionLevelRequest": {"type": "object", "properties": {"decisionLevelCode": {"type": "string", "nullable": true}, "decisionLevelName": {"type": "string", "nullable": true}, "class": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateDegreeRequest": {"type": "object", "properties": {"degreeCode": {"type": "string", "nullable": true}, "degreeName": {"type": "string", "nullable": true}, "degreeShortName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateDepartmentRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "nameShort": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "address": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "parentIdCode": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "departmentType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateDisciplineTypeRequest": {"type": "object", "properties": {"disciplineTypeCode": {"type": "string", "nullable": true}, "disciplineTypeName": {"type": "string", "nullable": true}, "class": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateDistrictRequest": {"type": "object", "properties": {"provinceId": {"type": "integer", "format": "int32", "nullable": true}, "districtCode": {"type": "string", "nullable": true}, "districtName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateEducationLevelRequest": {"type": "object", "properties": {"educationLevelCode": {"type": "string", "nullable": true}, "educationLevelName": {"type": "string", "nullable": true}, "class": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateEmployeeRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "shortName": {"type": "string", "nullable": true}, "organizationUnitId": {"type": "integer", "format": "int32", "nullable": true}, "classify": {"type": "integer", "format": "int32", "nullable": true}, "employeeCode": {"type": "string", "nullable": true}, "rankId": {"type": "integer", "format": "int32", "nullable": true}, "gender": {"type": "integer", "format": "int32", "nullable": true}, "academicRankId": {"type": "integer", "format": "int32", "nullable": true}, "yearOfAcademicRank": {"type": "integer", "format": "int32", "nullable": true}, "degreeId": {"type": "integer", "format": "int32", "nullable": true}, "yearOfDegree": {"type": "integer", "format": "int32", "nullable": true}, "positionId": {"type": "integer", "format": "int32", "nullable": true}, "positionType": {"type": "integer", "format": "int32", "nullable": true}, "partyPositionId": {"type": "integer", "format": "int32", "nullable": true}, "birthDay": {"type": "string", "nullable": true}, "owned": {"type": "boolean", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "activeAccount": {"type": "boolean", "nullable": true}, "isAdministrator": {"type": "boolean", "nullable": true}, "longFullName": {"type": "string", "nullable": true}, "birthPlace": {"type": "string", "nullable": true}, "homeLand": {"type": "string", "nullable": true}, "nativeAddress": {"type": "string", "nullable": true}, "tel": {"type": "string", "nullable": true}, "homeTel": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "fax": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "officeAddress": {"type": "string", "nullable": true}, "homeAddress": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "idNumber": {"type": "string", "nullable": true}, "issuedBy": {"type": "string", "nullable": true}, "dateBy": {"type": "string", "nullable": true}, "accountNumber": {"type": "string", "nullable": true}, "bank": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateJournalGroupRequest": {"type": "object", "properties": {"journalGroupCode": {"type": "string", "nullable": true}, "journalGroupName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateJournalRequest": {"type": "object", "properties": {"journalCode": {"type": "string", "nullable": true}, "journalName": {"type": "string", "nullable": true}, "issn": {"type": "string", "nullable": true}, "journalTypeId": {"type": "integer", "format": "int32", "nullable": true}, "journalTypeCode": {"type": "string", "nullable": true}, "journalTypeId_AN": {"type": "string", "nullable": true}, "journalGroupId": {"type": "integer", "format": "int32", "nullable": true}, "journalGroupCode": {"type": "string", "nullable": true}, "journalGroupId_AN": {"type": "string", "nullable": true}, "publishingAgency": {"type": "string", "nullable": true}, "pointFrom": {"type": "number", "format": "double", "nullable": true}, "pointTo": {"type": "number", "format": "double", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateJournalTypeRequest": {"type": "object", "properties": {"journalTypeCode": {"type": "string", "nullable": true}, "journalTypeName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateOnDutyCommandRequest": {"type": "object", "properties": {"year": {"type": "integer", "format": "int32", "nullable": true}, "week": {"type": "integer", "format": "int32", "nullable": true}, "date": {"type": "string", "format": "date-time", "nullable": true}, "employeeId_H": {"type": "string", "format": "uuid", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "CreateOrganizationUnitRequest": {"type": "object", "properties": {"organizationUnitCode": {"type": "string", "nullable": true}, "organizationUnitName": {"type": "string", "nullable": true}, "shortOrganizationUnitName": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "classifyGroup": {"type": "integer", "format": "int32", "nullable": true}, "trainingMaterialCode": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreatePositionRequest": {"type": "object", "properties": {"parentId": {"type": "integer", "format": "int32", "nullable": true}, "positionCode": {"type": "string", "nullable": true}, "positionName": {"type": "string", "nullable": true}, "isRoot": {"type": "boolean", "nullable": true}, "classify": {"type": "integer", "format": "int32", "nullable": true}, "shortPositionName": {"type": "string", "nullable": true}, "fullPositionName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateProvinceRequest": {"type": "object", "properties": {"provinceCode": {"type": "string", "nullable": true}, "provinceName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateRankRequest": {"type": "object", "properties": {"rankCode": {"type": "string", "nullable": true}, "rankName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateRewardTypeRequest": {"type": "object", "properties": {"rewardTypeCode": {"type": "string", "nullable": true}, "rewardTypeName": {"type": "string", "nullable": true}, "class": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateScheduleRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date-time", "nullable": true}, "rank": {"type": "string", "nullable": true}, "active": {"type": "integer", "format": "int32", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "CreateSoSRequest": {"type": "object", "properties": {"parentId": {"type": "integer", "format": "int32", "nullable": true}, "soSCode": {"type": "string", "nullable": true}, "soSName": {"type": "string", "nullable": true}, "year": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateUserModel": {"required": ["email", "firstName", "lastName", "password"], "type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "organizationUnitId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "CreateWardRequest": {"type": "object", "properties": {"provinceId": {"type": "integer", "format": "int32", "nullable": true}, "districtId": {"type": "integer", "format": "int32", "nullable": true}, "wardCode": {"type": "string", "nullable": true}, "wardName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CreateWorkingResultRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}, "class": {"type": "string", "nullable": true}, "dateFrom": {"type": "string", "nullable": true}, "dateTo": {"type": "string", "nullable": true}, "organizationUnitId": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "contents": {"type": "string", "nullable": true}, "contents1": {"type": "string", "nullable": true}, "contents2": {"type": "string", "nullable": true}, "contents3": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateWorkingResultSyntheticRequest": {"type": "object", "properties": {"organizationUnitId": {"type": "integer", "format": "int32", "nullable": true}, "year": {"type": "integer", "format": "int32", "nullable": true}, "week": {"type": "integer", "format": "int32", "nullable": true}, "contents": {"type": "string", "nullable": true}, "announced": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true}, "modifiedDate": {"type": "string", "format": "date-time", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}, "createdBy": {"type": "string", "nullable": true}, "modifiedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateWorkingScheduleRequest": {"type": "object", "properties": {"classify": {"type": "string", "nullable": true}, "coChair": {"type": "string", "nullable": true}, "contents": {"type": "string", "nullable": true}, "date": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32", "nullable": true}, "mtEntityState": {"type": "string", "nullable": true}, "member": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "organizationUnitId": {"type": "string", "nullable": true}, "organizationUnitId_Chair": {"type": "string", "nullable": true}, "place": {"type": "string", "nullable": true}, "register": {"type": "string", "nullable": true}, "time": {"type": "string", "nullable": true}, "timeFrom": {"type": "string", "nullable": true}, "timeTo": {"type": "string", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}, "workingScheduleC": {"type": "array", "items": {"type": "string"}, "nullable": true}, "workingScheduleEP": {"type": "array", "items": {"type": "string"}, "nullable": true}, "workingScheduleEPH": {"type": "array", "items": {"type": "string"}, "nullable": true}, "workingScheduleOU": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CreateWorkingScheduleResultRequest": {"type": "object", "properties": {"workingScheduleId": {"type": "integer", "format": "int32", "nullable": true}, "files": {"type": "array", "items": {"$ref": "#/components/schemas/FileDataRequest"}, "nullable": true}, "oldListFile": {"type": "string", "nullable": true}, "date": {"type": "string", "nullable": true}, "result": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeleteAcademicRankRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteAdvertisementRequest": {"type": "object", "properties": {"advertisementIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteCountryRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteDecisionLevelRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteDegreeRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteDepartmentRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteDisciplineTypeRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteDistrictRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteEducationLevelRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteEmployeeRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "DeleteJournalGroupRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteJournalRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteJournalTypeRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteOrganizationUnitRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeletePositionRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteProvinceRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteRankRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteRewardTypeRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteScheduleRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteSoSRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteWardRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteWorkingResultRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteWorkingResultSyntheticRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DeleteWorkingScheduleRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "FileDataRequest": {"type": "object", "properties": {"contents": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}, "fileSize": {"type": "number", "format": "float", "nullable": true}}, "additionalProperties": false}, "IdentityRole": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "normalizedName": {"type": "string", "nullable": true}, "concurrencyStamp": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PermissionViewResponse": {"required": ["roleClaims", "roleId"], "type": "object", "properties": {"roleId": {"type": "string", "nullable": true}, "roleClaims": {"type": "array", "items": {"$ref": "#/components/schemas/RoleClaimsViewResponse"}, "nullable": true}}, "additionalProperties": false}, "RoleClaimsViewResponse": {"required": ["type", "value"], "type": "object", "properties": {"type": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "selected": {"type": "boolean"}}, "additionalProperties": false}, "SearchAcademicRankRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchAdvertisementRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchCountryRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchDecisionLevelRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchDegreeRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchDepartmentRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "createdDateTu": {"type": "string", "format": "date-time", "nullable": true}, "createdDateDen": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchDisciplineTypeRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchDistrictRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "provinceId": {"type": "integer", "format": "int32", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchEducationLevelRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchEmployeeRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchJournalGroupRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchJournalRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchJournalTypeRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchOnDutyCommandRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "week": {"type": "integer", "format": "int32", "nullable": true}, "year": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "SearchOrganizationUnitRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchPositionRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchProvinceRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchRankRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchRewardTypeRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchScheduleRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchSoSRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchWardRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "provinceId": {"type": "integer", "format": "int32", "nullable": true}, "districtId": {"type": "integer", "format": "int32", "nullable": true}, "keyword": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchWorkingResultRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "org": {"type": "string", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchWorkingResultSyntheticRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "org": {"type": "string", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchWorkingScheduleAnnouncedRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "organizationUnitId": {"type": "integer", "format": "int32", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchWorkingScheduleRequest": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sorts": {"type": "string", "nullable": true}, "orders": {"type": "string", "nullable": true}, "queryString": {"type": "string", "nullable": true}, "employeeId": {"type": "string", "format": "uuid"}, "isAll": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "organizationUnitId": {"type": "string", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}, "typeExport": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateAcademicRankRequest": {"type": "object", "properties": {"academicRankCode": {"type": "string", "nullable": true}, "academicRankName": {"type": "string", "nullable": true}, "academicRankShortName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateAdvertisementRequest": {"type": "object", "properties": {"advertisementCode": {"type": "string", "nullable": true}, "advertisementName": {"type": "string", "nullable": true}, "start": {"type": "string", "format": "date-time", "nullable": true}, "startHour": {"type": "integer", "format": "int32", "nullable": true}, "startMinute": {"type": "integer", "format": "int32", "nullable": true}, "ends": {"type": "string", "format": "date-time", "nullable": true}, "endsHour": {"type": "integer", "format": "int32", "nullable": true}, "endsMinute": {"type": "integer", "format": "int32", "nullable": true}, "year": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateCountryRequest": {"type": "object", "properties": {"countryCode": {"type": "string", "nullable": true}, "countryName": {"type": "string", "nullable": true}, "languageName": {"type": "string", "nullable": true}, "vietnameseName": {"type": "string", "nullable": true}, "class": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateDecisionLevelRequest": {"type": "object", "properties": {"decisionLevelCode": {"type": "string", "nullable": true}, "decisionLevelName": {"type": "string", "nullable": true}, "class": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateDegreeRequest": {"type": "object", "properties": {"degreeCode": {"type": "string", "nullable": true}, "degreeName": {"type": "string", "nullable": true}, "degreeShortName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateDepartmentRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "nameShort": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "address": {"type": "string", "nullable": true}, "isActive": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "parentIdCode": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "departmentType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateDisciplineTypeRequest": {"type": "object", "properties": {"disciplineTypeCode": {"type": "string", "nullable": true}, "disciplineTypeName": {"type": "string", "nullable": true}, "class": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateDistrictRequest": {"type": "object", "properties": {"provinceId": {"type": "integer", "format": "int32", "nullable": true}, "districtCode": {"type": "string", "nullable": true}, "districtName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateEducationLevelRequest": {"type": "object", "properties": {"educationLevelCode": {"type": "string", "nullable": true}, "educationLevelName": {"type": "string", "nullable": true}, "class": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateEmployeeRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "shortName": {"type": "string", "nullable": true}, "organizationUnitId": {"type": "integer", "format": "int32", "nullable": true}, "classify": {"type": "integer", "format": "int32", "nullable": true}, "employeeCode": {"type": "string", "nullable": true}, "rankId": {"type": "integer", "format": "int32", "nullable": true}, "gender": {"type": "integer", "format": "int32", "nullable": true}, "academicRankId": {"type": "integer", "format": "int32", "nullable": true}, "yearOfAcademicRank": {"type": "integer", "format": "int32", "nullable": true}, "degreeId": {"type": "integer", "format": "int32", "nullable": true}, "yearOfDegree": {"type": "integer", "format": "int32", "nullable": true}, "positionId": {"type": "integer", "format": "int32", "nullable": true}, "positionType": {"type": "integer", "format": "int32", "nullable": true}, "partyPositionId": {"type": "integer", "format": "int32", "nullable": true}, "birthDay": {"type": "string", "nullable": true}, "owned": {"type": "boolean", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "activeAccount": {"type": "boolean", "nullable": true}, "isAdministrator": {"type": "boolean", "nullable": true}, "longFullName": {"type": "string", "nullable": true}, "birthPlace": {"type": "string", "nullable": true}, "homeLand": {"type": "string", "nullable": true}, "nativeAddress": {"type": "string", "nullable": true}, "tel": {"type": "string", "nullable": true}, "homeTel": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "fax": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "officeAddress": {"type": "string", "nullable": true}, "homeAddress": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "idNumber": {"type": "string", "nullable": true}, "issuedBy": {"type": "string", "nullable": true}, "dateBy": {"type": "string", "nullable": true}, "accountNumber": {"type": "string", "nullable": true}, "bank": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "UpdateJournalGroupRequest": {"type": "object", "properties": {"journalGroupCode": {"type": "string", "nullable": true}, "journalGroupName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateJournalRequest": {"type": "object", "properties": {"journalCode": {"type": "string", "nullable": true}, "journalName": {"type": "string", "nullable": true}, "issn": {"type": "string", "nullable": true}, "journalTypeId": {"type": "integer", "format": "int32", "nullable": true}, "journalTypeCode": {"type": "string", "nullable": true}, "journalTypeId_AN": {"type": "string", "nullable": true}, "journalGroupId": {"type": "integer", "format": "int32", "nullable": true}, "journalGroupCode": {"type": "string", "nullable": true}, "journalGroupId_AN": {"type": "string", "nullable": true}, "publishingAgency": {"type": "string", "nullable": true}, "pointFrom": {"type": "number", "format": "double", "nullable": true}, "pointTo": {"type": "number", "format": "double", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateJournalTypeRequest": {"type": "object", "properties": {"journalTypeCode": {"type": "string", "nullable": true}, "journalTypeName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateOnDutyCommandRequest": {"type": "object", "properties": {"year": {"type": "integer", "format": "int32", "nullable": true}, "week": {"type": "integer", "format": "int32", "nullable": true}, "date": {"type": "string", "format": "date-time", "nullable": true}, "employeeId_H": {"type": "string", "format": "uuid", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateOrganizationUnitRequest": {"type": "object", "properties": {"organizationUnitCode": {"type": "string", "nullable": true}, "organizationUnitName": {"type": "string", "nullable": true}, "shortOrganizationUnitName": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "classifyGroup": {"type": "integer", "format": "int32", "nullable": true}, "trainingMaterialCode": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdatePositionRequest": {"type": "object", "properties": {"parentId": {"type": "integer", "format": "int32", "nullable": true}, "positionCode": {"type": "string", "nullable": true}, "positionName": {"type": "string", "nullable": true}, "isRoot": {"type": "boolean", "nullable": true}, "classify": {"type": "integer", "format": "int32", "nullable": true}, "shortPositionName": {"type": "string", "nullable": true}, "fullPositionName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateProvinceRequest": {"type": "object", "properties": {"provinceCode": {"type": "string", "nullable": true}, "provinceName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateRankRequest": {"type": "object", "properties": {"rankCode": {"type": "string", "nullable": true}, "rankName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateRewardTypeRequest": {"type": "object", "properties": {"rewardTypeCode": {"type": "string", "nullable": true}, "rewardTypeName": {"type": "string", "nullable": true}, "class": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateScheduleRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date-time", "nullable": true}, "rank": {"type": "string", "nullable": true}, "active": {"type": "integer", "format": "int32", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateSoSRequest": {"type": "object", "properties": {"parentId": {"type": "integer", "format": "int32", "nullable": true}, "soSCode": {"type": "string", "nullable": true}, "soSName": {"type": "string", "nullable": true}, "year": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateStatusByIdsIntRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateUserModel": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateWardRequest": {"type": "object", "properties": {"provinceId": {"type": "integer", "format": "int32", "nullable": true}, "districtId": {"type": "integer", "format": "int32", "nullable": true}, "wardCode": {"type": "string", "nullable": true}, "wardName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateWorkingResultRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}, "class": {"type": "string", "nullable": true}, "dateFrom": {"type": "string", "nullable": true}, "dateTo": {"type": "string", "nullable": true}, "organizationUnitId": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "contents": {"type": "string", "nullable": true}, "contents1": {"type": "string", "nullable": true}, "contents2": {"type": "string", "nullable": true}, "contents3": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateWorkingResultSyntheticRequest": {"type": "object", "properties": {"organizationUnitId": {"type": "integer", "format": "int32", "nullable": true}, "year": {"type": "integer", "format": "int32", "nullable": true}, "week": {"type": "integer", "format": "int32", "nullable": true}, "contents": {"type": "string", "nullable": true}, "announced": {"type": "integer", "format": "int32", "nullable": true}, "active": {"type": "boolean", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true}, "modifiedDate": {"type": "string", "format": "date-time", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}, "createdBy": {"type": "string", "nullable": true}, "modifiedBy": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateWorkingScheduleRequest": {"type": "object", "properties": {"classify": {"type": "string", "nullable": true}, "coChair": {"type": "string", "nullable": true}, "contents": {"type": "string", "nullable": true}, "date": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "mtEntityState": {"type": "string", "nullable": true}, "member": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "organizationUnitId": {"type": "string", "nullable": true}, "organizationUnitId_Chair": {"type": "string", "nullable": true}, "place": {"type": "string", "nullable": true}, "register": {"type": "string", "nullable": true}, "time": {"type": "string", "nullable": true}, "timeFrom": {"type": "string", "nullable": true}, "timeTo": {"type": "string", "nullable": true}, "week": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}, "workingScheduleC": {"type": "array", "items": {"type": "string"}, "nullable": true}, "workingScheduleEP": {"type": "array", "items": {"type": "string"}, "nullable": true}, "workingScheduleEPH": {"type": "array", "items": {"type": "string"}, "nullable": true}, "workingScheduleOU": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserRolesResponse": {"required": ["<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"roleName": {"type": "string", "nullable": true}, "selected": {"type": "boolean"}}, "additionalProperties": false}, "WorkingScheduleIssueAnnouncedRequest": {"type": "object", "properties": {"organizationUnitId": {"type": "integer", "format": "int32", "nullable": true}, "year": {"type": "integer", "format": "int32", "nullable": true}, "week": {"type": "integer", "format": "int32"}, "number": {"type": "integer", "format": "int32", "nullable": true}, "sign": {"type": "string", "nullable": true}, "date": {"type": "string", "nullable": true}, "announced": {"type": "integer", "format": "int32", "nullable": true}, "place": {"type": "string", "nullable": true}, "command": {"type": "string", "nullable": true}, "personSigningOther": {"type": "string", "nullable": true}, "personSigning": {"type": "string", "format": "uuid", "nullable": true}, "unitPositionSigning": {"type": "string", "nullable": true}, "receiverIDs": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"oauth": {"type": "oauth2", "flows": {"password": {"tokenUrl": "http://localhost:44395/connect/token", "scopes": {"openid": "Access OpenID", "email": "Access Email", "profile": "Access Profile", "roles": "Access Roles"}}}}}}, "security": [{"oauth": ["openid", "email", "profile", "roles"]}]}