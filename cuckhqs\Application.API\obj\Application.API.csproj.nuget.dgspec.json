{"format": 1, "restore": {"D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\Application.API.csproj": {}}, "projects": {"D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\Application.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\Application.API.csproj", "projectName": "Application.API", "projectPath": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\Application.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Infrastructure\\Application.Infrastructure.csproj": {"projectPath": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Infrastructure\\Application.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Aspose.Words": {"target": "Package", "version": "[25.4.0, )"}, "BoldReports.Net.Core": {"target": "Package", "version": "[6.2.39, )"}, "ClosedXML": {"target": "Package", "version": "[0.104.1, )"}, "EPPlus": {"target": "Package", "version": "[7.5.2, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.ApiDescription.Server": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.1, )"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"target": "Package", "version": "[8.7.0, )"}, "NetEscapades.AspNetCore.SecurityHeaders": {"target": "Package", "version": "[1.0.0-preview.3, )"}, "OpenIddict.Server": {"target": "Package", "version": "[6.3.0, )"}, "OpenIddict.Server.AspNetCore": {"target": "Package", "version": "[6.3.0, )"}, "OpenIddict.Validation.AspNetCore": {"target": "Package", "version": "[6.1.1, )"}, "OpenIddict.Validation.SystemNetHttp": {"target": "Package", "version": "[6.1.1, )"}, "QuestPDF": {"target": "Package", "version": "[2025.4.2, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[3.0.1, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Sinks.ApplicationInsights": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[2.1.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.7.0, )"}, "Swashbuckle.AspNetCore.SwaggerUI": {"target": "Package", "version": "[7.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Infrastructure\\Application.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Infrastructure\\Application.Infrastructure.csproj", "projectName": "Application.Infrastructure", "projectPath": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Infrastructure\\Application.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "DocumentFormat.OpenXml": {"target": "Package", "version": "[3.3.0, )"}, "ExcelDataReader": {"target": "Package", "version": "[3.7.0, )"}, "ExcelDataReader.DataSet": {"target": "Package", "version": "[3.7.0, )"}, "Fido2": {"target": "Package", "version": "[3.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.2, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.9.2, )"}, "MailKit": {"target": "Package", "version": "[4.12.1, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.1, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.FileProviders.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.FileProviders.Physical": {"target": "Package", "version": "[9.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenIddict.EntityFrameworkCore": {"target": "Package", "version": "[6.1.1, )"}, "System.Linq": {"target": "Package", "version": "[4.3.0, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.6.0.2, )"}, "Z.EntityFramework.Plus.EFCore": {"target": "Package", "version": "[9.103.8.1, )"}, "log4net": {"target": "Package", "version": "[2.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}