{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=msi_core;Integrated Security=True;TrustServerCertificate=True;MultipleActiveResultSets=true;Connect Timeout=100"
  },
  "Fido2": {
    "ServerDomain": "localhost",
    "ServerName": "Fido2OpenIddict",
    "Origins": [ "https://localhost:44395" ],
    "TimestampDriftTolerance": 300000,
    "MDSAccessKey": null
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Keycloak": {
    "ServerRealm": "http://localhost:8080/realms/myrealm",
    "Metadata": "http://localhost:8080/realms/myrealm/.well-known/openid-configuration",
    "ClientId": "oidc-code-pkce"
    // "ClientSecret": "--in user secrets or keyvault--"
  },
  "OAuthClientSettings": {
    "AngularClientRedirectUris": [
      "http://localhost:4200/call-back",
      "https://localhost:4200/call-back",
      "http://*************:4200/call-back",
      "https://*************:4200/call-back"
    ],
    "AngularClientPostLogoutRedirectUris": [
      "http://localhost:4200",
      "https://localhost:4200",
      "http://*************:4200",
      "https://*************:4200"
    ]
  },
  "AllowedOrigins": [
    "https://localhost:4200",
    "http://localhost:4200",
    "http://localhost:5095"
  ],
  "AllowedHosts": "*"
}