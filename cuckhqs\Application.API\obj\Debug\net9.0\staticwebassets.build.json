{"Version": 1, "Hash": "8S7HYXJsiyypehgXNrF39QlVtQQx9V2cvO3q7X2jx88=", "Source": "Application.API", "BasePath": "_content/Application.API", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Application.API\\wwwroot", "Source": "Application.API", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\", "BasePath": "_content/Application.API", "Pattern": "**"}], "Assets": [{"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\App_Data\\times.ttf", "SourceId": "Application.API", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\", "BasePath": "_content/Application.API", "RelativePath": "App_Data/times#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nzcq01ozbz", "Integrity": "Q6iQeVrRM9c1aNFpo1kk9AQWvoppUKGgMmMFLsjpW4o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\App_Data\\times.ttf", "FileLength": 1206884, "LastWriteTime": "2025-07-21T14:54:49+00:00"}, {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\Resources\\Mau_bao_cao_tuan.rdlc", "SourceId": "Application.API", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\", "BasePath": "_content/Application.API", "RelativePath": "Resources/Mau_bao_cao_tuan#[.{fingerprint}]?.rdlc", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "56cxbsj16x", "Integrity": "3QbCbtLMWn41naSC3535X99tPkj2jnea5fToLoI9t3c=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Resources\\Mau_bao_cao_tuan.rdlc", "FileLength": 57304, "LastWriteTime": "2025-07-21T14:54:49+00:00"}, {"Identity": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\Templates\\WorkingScheduleTemplate.docx", "SourceId": "Application.API", "SourceType": "Discovered", "ContentRoot": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\", "BasePath": "_content/Application.API", "RelativePath": "Templates/WorkingScheduleTemplate#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cwd10xbcw3", "Integrity": "ZC9DxML8wJOrB9h31RKfEjNikKL0Z628zaYt/BAT4R4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\WorkingScheduleTemplate.docx", "FileLength": 15445, "LastWriteTime": "2025-07-21T14:54:49+00:00"}], "Endpoints": [{"Route": "App_Data/times.nzcq01ozbz.ttf", "AssetFile": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\App_Data\\times.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1206884"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Q6iQeVrRM9c1aNFpo1kk9AQWvoppUKGgMmMFLsjpW4o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nzcq01ozbz"}, {"Name": "label", "Value": "App_Data/times.ttf"}, {"Name": "integrity", "Value": "sha256-Q6iQeVrRM9c1aNFpo1kk9AQWvoppUKGgMmMFLsjpW4o="}]}, {"Route": "App_Data/times.ttf", "AssetFile": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\App_Data\\times.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1206884"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Q6iQeVrRM9c1aNFpo1kk9AQWvoppUKGgMmMFLsjpW4o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Q6iQeVrRM9c1aNFpo1kk9AQWvoppUKGgMmMFLsjpW4o="}]}, {"Route": "Resources/Mau_bao_cao_tuan.56cxbsj16x.rdlc", "AssetFile": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\Resources\\Mau_bao_cao_tuan.rdlc", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "57304"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3QbCbtLMWn41naSC3535X99tPkj2jnea5fToLoI9t3c=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56cxbsj16x"}, {"Name": "label", "Value": "Resources/Mau_bao_cao_tuan.rdlc"}, {"Name": "integrity", "Value": "sha256-3QbCbtLMWn41naSC3535X99tPkj2jnea5fToLoI9t3c="}]}, {"Route": "Resources/Mau_bao_cao_tuan.rdlc", "AssetFile": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\Resources\\Mau_bao_cao_tuan.rdlc", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "57304"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3QbCbtLMWn41naSC3535X99tPkj2jnea5fToLoI9t3c=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3QbCbtLMWn41naSC3535X99tPkj2jnea5fToLoI9t3c="}]}, {"Route": "Templates/WorkingScheduleTemplate.cwd10xbcw3.docx", "AssetFile": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\Templates\\WorkingScheduleTemplate.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15445"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"ZC9DxML8wJOrB9h31RKfEjNikKL0Z628zaYt/BAT4R4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwd10xbcw3"}, {"Name": "label", "Value": "Templates/WorkingScheduleTemplate.docx"}, {"Name": "integrity", "Value": "sha256-ZC9DxML8wJOrB9h31RKfEjNikKL0Z628zaYt/BAT4R4="}]}, {"Route": "Templates/WorkingScheduleTemplate.docx", "AssetFile": "D:\\CUCKHQS_FULL\\cuckhqs\\Application.API\\wwwroot\\Templates\\WorkingScheduleTemplate.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15445"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"ZC9DxML8wJOrB9h31RKfEjNikKL0Z628zaYt/BAT4R4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:54:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZC9DxML8wJOrB9h31RKfEjNikKL0Z628zaYt/BAT4R4="}]}]}