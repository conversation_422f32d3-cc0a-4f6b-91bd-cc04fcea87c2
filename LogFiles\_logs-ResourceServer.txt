2025-07-24 14:16:25.685 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 14:16:26.213 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 14:16:26.275 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 14:16:26.282 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 14:16:28.317 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("121"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:16:28.372 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "f58b6ab8-f88f-446e-af0d-1efa3fdf1067" validation failed: "DuplicateRoleName".
2025-07-24 14:16:28.381 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:16:28.389 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "7fd3ba77-cb61-4d91-a0e1-8f57e336e015" validation failed: "DuplicateRoleName".
2025-07-24 14:16:28.410 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__defaultUser_Id_0='d15bb601-9725-434f-b55e-571ce2939d73' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 14:16:28.426 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 14:16:28.451 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__defaultUser_Id_0='2e29d813-17f5-42c4-ae49-5b2da7cc73bd' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 14:16:28.460 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 14:16:28.467 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:16:28.493 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:16:28.501 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:16:28.507 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:16:28.511 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 14:16:28.514 +07:00 [Information] [app] [] Application Starting
2025-07-24 14:16:28.608 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"http://localhost:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 14:16:28.625 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 14:16:28.685 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 14:16:28.691 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Local"
2025-07-24 14:16:28.694 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
