2025-07-24 14:16:25.685 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 14:16:26.213 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 14:16:26.275 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 14:16:26.282 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 14:16:28.317 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("121"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:16:28.372 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "f58b6ab8-f88f-446e-af0d-1efa3fdf1067" validation failed: "DuplicateRoleName".
2025-07-24 14:16:28.381 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:16:28.389 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "7fd3ba77-cb61-4d91-a0e1-8f57e336e015" validation failed: "DuplicateRoleName".
2025-07-24 14:16:28.410 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__defaultUser_Id_0='d15bb601-9725-434f-b55e-571ce2939d73' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 14:16:28.426 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 14:16:28.451 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__defaultUser_Id_0='2e29d813-17f5-42c4-ae49-5b2da7cc73bd' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 14:16:28.460 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 14:16:28.467 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:16:28.493 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:16:28.501 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:16:28.507 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:16:28.511 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 14:16:28.514 +07:00 [Information] [app] [] Application Starting
2025-07-24 14:16:28.608 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"http://localhost:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 14:16:28.625 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 14:16:28.685 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 14:16:28.691 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Local"
2025-07-24 14:16:28.694 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
2025-07-24 14:36:26.401 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 14:36:26.802 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 14:36:26.854 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 14:36:26.857 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 14:36:27.162 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("19"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:36:27.202 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "e123142e-1699-4983-9db2-e145570f2c9d" validation failed: "DuplicateRoleName".
2025-07-24 14:36:27.208 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:36:27.211 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "1fd1826c-3d90-4c26-89e6-54059790f54a" validation failed: "DuplicateRoleName".
2025-07-24 14:36:27.227 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__defaultUser_Id_0='d1c824e7-ced3-48a5-bdb4-344291fde10c' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 14:36:27.237 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("2"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 14:36:27.260 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__defaultUser_Id_0='608de03c-734f-4486-8996-f28286f6e72a' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 14:36:27.264 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 14:36:27.268 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:36:27.291 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:36:27.294 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:36:27.296 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:36:27.298 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 14:36:27.299 +07:00 [Information] [app] [] Application Starting
2025-07-24 14:36:27.328 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"http://localhost:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 14:36:27.336 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 14:36:27.338 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 14:36:27.339 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Development"
2025-07-24 14:36:27.339 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
2025-07-24 14:37:08.594 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - null null
2025-07-24 14:37:08.665 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 71.49ms
2025-07-24 14:37:08.708 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - null null
2025-07-24 14:37:08.714 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - 200 null "application/javascript;charset=utf-8" 5.9582ms
2025-07-24 14:37:08.878 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - null null
2025-07-24 14:37:08.986 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 107.5278ms
2025-07-24 14:37:14.478 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null null
2025-07-24 14:37:14.484 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:14.487 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 204 null null 9.5166ms
2025-07-24 14:37:14.491 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null 0
2025-07-24 14:37:14.496 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:14.500 +07:00 [Warning] [Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware] [{ Id: 3, Name: "FailedToDeterminePort" }] Failed to determine the https port for redirect.
2025-07-24 14:37:15.251 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 14:37:15.260 +07:00 [Information] [Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationHandler] [{ Id: 7, Name: "AuthenticationSchemeNotAuthenticatedWithFailure" }] "Identity.Application" was not authenticated. Failure message: "Unprotect ticket failed"
2025-07-24 14:37:15.265 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ValidateHostHeader".
2025-07-24 14:37:15.266 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateValidatedTokens".
2025-07-24 14:37:15.268 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromAuthorizationHeader".
2025-07-24 14:37:15.270 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromBodyForm".
2025-07-24 14:37:15.274 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromQueryString".
2025-07-24 14:37:15.276 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 14:37:15.278 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was marked as rejected by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 14:37:15.280 +07:00 [Debug] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 9, Name: "AuthenticationSchemeNotAuthenticated" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was not authenticated.
2025-07-24 14:37:15.283 +07:00 [Information] [Microsoft.AspNetCore.Authorization.DefaultAuthorizationService] [{ Id: 2, Name: "UserAuthorizationFailed" }] Authorization failed. "These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user."
2025-07-24 14:37:15.290 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveHostChallengeProperties".
2025-07-24 14:37:15.293 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachHostChallengeError".
2025-07-24 14:37:15.296 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+AttachDefaultChallengeError".
2025-07-24 14:37:15.299 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+AttachCustomChallengeParameters".
2025-07-24 14:37:15.301 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachHttpResponseCode`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.302 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachCacheControlHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.305 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachWwwAuthenticateHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.310 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The response was successfully returned as a challenge response: "{
  \"error\": \"missing_token\",
  \"error_description\": \"The security token is missing.\",
  \"error_uri\": \"https://documentation.openiddict.com/errors/ID2000\"
}".
2025-07-24 14:37:15.311 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ProcessChallengeErrorResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.312 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was marked as handled by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ProcessChallengeErrorResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.313 +07:00 [Information] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 12, Name: "AuthenticationSchemeChallenged" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was challenged.
2025-07-24 14:37:15.315 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 401 0 null 824.0812ms
2025-07-24 14:37:15.574 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null null
2025-07-24 14:37:15.577 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:15.578 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 204 null null 4.8826ms
2025-07-24 14:37:15.580 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null 0
2025-07-24 14:37:15.583 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:15.585 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 14:37:15.589 +07:00 [Information] [Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationHandler] [{ Id: 7, Name: "AuthenticationSchemeNotAuthenticatedWithFailure" }] "Identity.Application" was not authenticated. Failure message: "Unprotect ticket failed"
2025-07-24 14:37:15.592 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ValidateHostHeader".
2025-07-24 14:37:15.594 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateValidatedTokens".
2025-07-24 14:37:15.597 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromAuthorizationHeader".
2025-07-24 14:37:15.599 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromBodyForm".
2025-07-24 14:37:15.600 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromQueryString".
2025-07-24 14:37:15.602 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 14:37:15.603 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was marked as rejected by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 14:37:15.604 +07:00 [Debug] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 9, Name: "AuthenticationSchemeNotAuthenticated" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was not authenticated.
2025-07-24 14:37:15.606 +07:00 [Information] [Microsoft.AspNetCore.Authorization.DefaultAuthorizationService] [{ Id: 2, Name: "UserAuthorizationFailed" }] Authorization failed. "These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user."
2025-07-24 14:37:15.607 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveHostChallengeProperties".
2025-07-24 14:37:15.609 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachHostChallengeError".
2025-07-24 14:37:15.611 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+AttachDefaultChallengeError".
2025-07-24 14:37:15.612 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+AttachCustomChallengeParameters".
2025-07-24 14:37:15.614 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachHttpResponseCode`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.616 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachCacheControlHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.618 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachWwwAuthenticateHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.619 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The response was successfully returned as a challenge response: "{
  \"error\": \"missing_token\",
  \"error_description\": \"The security token is missing.\",
  \"error_uri\": \"https://documentation.openiddict.com/errors/ID2000\"
}".
2025-07-24 14:37:15.621 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ProcessChallengeErrorResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.623 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was marked as handled by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ProcessChallengeErrorResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.624 +07:00 [Information] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 12, Name: "AuthenticationSchemeChallenged" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was challenged.
2025-07-24 14:37:15.625 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 401 0 null 45.1004ms
2025-07-24 14:37:16.494 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/api/Employee/Search""" - null null
2025-07-24 14:37:16.496 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:16.497 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/api/Employee/Search""" - 204 null null 3.9226ms
2025-07-24 14:37:16.500 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/api/Employee/Search""" - "application/json" 42
2025-07-24 14:37:16.507 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:16.510 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 14:37:16.516 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:16.518 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)"'
2025-07-24 14:37:16.534 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker] [{ Id: 102, Name: "ControllerActionExecuting" }] Route matched with "{action = \"SearchEmployee\", controller = \"Employee\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] SearchEmployeeAsync(Application.Infrastructure.Models.Request.Category.Employee.SearchEmployeeRequest)" on controller "Application.API.Controllers.EmployeeController" ("Application.API").
2025-07-24 14:37:16.804 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("5"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT [o].[Id], [o].[AccountNumber], [o].[Active], [o].[Address], [o].[BankName], [o].[Classify], [o].[ClassifyGroup], [o].[CreatedBy], [o].[CreatedDate], [o].[Description], [o].[Director], [o].[Email], [o].[Fax], [o].[FullOrganizationUnitName], [o].[IPAddress], [o].[IsRoot], [o].[ModifiedBy], [o].[ModifiedDate], [o].[OrganizationUnitName], [o].[OrganizationUnitCode], [o].[ParentCode], [o].[ParentId], [o].[ShortOrganizationUnitName], [o].[SortOrder], [o].[Tel], [o].[TrainingMaterialCode], [o].[Website]
FROM [OrganizationUnit] AS [o]"
2025-07-24 14:37:17.904 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT [e].[Id], [e].[FirstName], [e].[LastName], [e].[Classify], [e].[OrganizationUnitId], NULL AS [OrganizationUnitName], [e].[EmployeeCode], [e].[Fullname], [e].[RankId], [e].[Gender], CASE
    WHEN [e].[Gender] = 1 THEN N'Nam'
    ELSE N'Nữ'
END AS [Gender_Name], [e].[AcademicRankId], [e].[YearOfAcademicRank], [e].[DegreeId], [e].[YearOfDegree], [e].[PositionId], [e].[PositionType], [e].[PartyPositionId], [e].[BirthDay], [e].[Owned], [e].[Active], [e].[ActiveAccount], [e].[IsAdministrator]
FROM [Employee] AS [e]
WHERE [e].[PositionType] IS NOT NULL AND [e].[PositionType] IN (CAST(1 AS smallint), CAST(2 AS smallint))"
2025-07-24 14:37:17.910 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor] [{ Id: 1, Name: "ObjectResultExecuting" }] Executing "OkObjectResult", writing value of type '"Application.Infrastructure.Commons.BaseSearchResponse`1[[Application.Infrastructure.Models.Response.Category.EmployeeResponse, Application.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-24 14:37:17.927 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker] [{ Id: 105, Name: "ActionExecuted" }] Executed action "Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)" in 1387.7515ms
2025-07-24 14:37:17.929 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Id: 1, Name: "ExecutedEndpoint" }] Executed endpoint '"Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)"'
2025-07-24 14:37:17.932 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/api/Employee/Search""" - 200 null "application/json; charset=utf-8" 1432.5831ms
2025-07-24 14:37:33.923 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - null null
2025-07-24 14:37:33.925 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 2.1422ms
2025-07-24 14:37:33.951 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - null null
2025-07-24 14:37:33.955 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - 200 null "application/javascript;charset=utf-8" 3.8099ms
2025-07-24 14:37:34.000 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - null null
2025-07-24 14:37:34.024 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 24.6096ms
