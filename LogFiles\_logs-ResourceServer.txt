2025-07-24 14:16:25.685 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 14:16:26.213 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 14:16:26.275 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 14:16:26.282 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 14:16:28.317 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("121"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:16:28.372 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "f58b6ab8-f88f-446e-af0d-1efa3fdf1067" validation failed: "DuplicateRoleName".
2025-07-24 14:16:28.381 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:16:28.389 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "7fd3ba77-cb61-4d91-a0e1-8f57e336e015" validation failed: "DuplicateRoleName".
2025-07-24 14:16:28.410 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__defaultUser_Id_0='d15bb601-9725-434f-b55e-571ce2939d73' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 14:16:28.426 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 14:16:28.451 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__defaultUser_Id_0='2e29d813-17f5-42c4-ae49-5b2da7cc73bd' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 14:16:28.460 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 14:16:28.467 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:16:28.493 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:16:28.501 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:16:28.507 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:16:28.511 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 14:16:28.514 +07:00 [Information] [app] [] Application Starting
2025-07-24 14:16:28.608 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"http://localhost:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 14:16:28.625 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 14:16:28.685 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 14:16:28.691 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Local"
2025-07-24 14:16:28.694 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
2025-07-24 14:36:26.401 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 14:36:26.802 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 14:36:26.854 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 14:36:26.857 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 14:36:27.162 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("19"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:36:27.202 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "e123142e-1699-4983-9db2-e145570f2c9d" validation failed: "DuplicateRoleName".
2025-07-24 14:36:27.208 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:36:27.211 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "1fd1826c-3d90-4c26-89e6-54059790f54a" validation failed: "DuplicateRoleName".
2025-07-24 14:36:27.227 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__defaultUser_Id_0='d1c824e7-ced3-48a5-bdb4-344291fde10c' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 14:36:27.237 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("2"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 14:36:27.260 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__defaultUser_Id_0='608de03c-734f-4486-8996-f28286f6e72a' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 14:36:27.264 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 14:36:27.268 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 14:36:27.291 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:36:27.294 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:36:27.296 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 14:36:27.298 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 14:36:27.299 +07:00 [Information] [app] [] Application Starting
2025-07-24 14:36:27.328 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"http://localhost:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 14:36:27.336 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 14:36:27.338 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 14:36:27.339 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Development"
2025-07-24 14:36:27.339 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
2025-07-24 14:37:08.594 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - null null
2025-07-24 14:37:08.665 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 71.49ms
2025-07-24 14:37:08.708 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - null null
2025-07-24 14:37:08.714 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - 200 null "application/javascript;charset=utf-8" 5.9582ms
2025-07-24 14:37:08.878 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - null null
2025-07-24 14:37:08.986 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 107.5278ms
2025-07-24 14:37:14.478 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null null
2025-07-24 14:37:14.484 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:14.487 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 204 null null 9.5166ms
2025-07-24 14:37:14.491 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null 0
2025-07-24 14:37:14.496 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:14.500 +07:00 [Warning] [Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware] [{ Id: 3, Name: "FailedToDeterminePort" }] Failed to determine the https port for redirect.
2025-07-24 14:37:15.251 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 14:37:15.260 +07:00 [Information] [Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationHandler] [{ Id: 7, Name: "AuthenticationSchemeNotAuthenticatedWithFailure" }] "Identity.Application" was not authenticated. Failure message: "Unprotect ticket failed"
2025-07-24 14:37:15.265 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ValidateHostHeader".
2025-07-24 14:37:15.266 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateValidatedTokens".
2025-07-24 14:37:15.268 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromAuthorizationHeader".
2025-07-24 14:37:15.270 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromBodyForm".
2025-07-24 14:37:15.274 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromQueryString".
2025-07-24 14:37:15.276 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 14:37:15.278 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was marked as rejected by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 14:37:15.280 +07:00 [Debug] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 9, Name: "AuthenticationSchemeNotAuthenticated" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was not authenticated.
2025-07-24 14:37:15.283 +07:00 [Information] [Microsoft.AspNetCore.Authorization.DefaultAuthorizationService] [{ Id: 2, Name: "UserAuthorizationFailed" }] Authorization failed. "These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user."
2025-07-24 14:37:15.290 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveHostChallengeProperties".
2025-07-24 14:37:15.293 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachHostChallengeError".
2025-07-24 14:37:15.296 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+AttachDefaultChallengeError".
2025-07-24 14:37:15.299 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+AttachCustomChallengeParameters".
2025-07-24 14:37:15.301 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachHttpResponseCode`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.302 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachCacheControlHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.305 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachWwwAuthenticateHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.310 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The response was successfully returned as a challenge response: "{
  \"error\": \"missing_token\",
  \"error_description\": \"The security token is missing.\",
  \"error_uri\": \"https://documentation.openiddict.com/errors/ID2000\"
}".
2025-07-24 14:37:15.311 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ProcessChallengeErrorResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.312 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was marked as handled by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ProcessChallengeErrorResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.313 +07:00 [Information] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 12, Name: "AuthenticationSchemeChallenged" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was challenged.
2025-07-24 14:37:15.315 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 401 0 null 824.0812ms
2025-07-24 14:37:15.574 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null null
2025-07-24 14:37:15.577 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:15.578 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 204 null null 4.8826ms
2025-07-24 14:37:15.580 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null 0
2025-07-24 14:37:15.583 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:15.585 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 14:37:15.589 +07:00 [Information] [Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationHandler] [{ Id: 7, Name: "AuthenticationSchemeNotAuthenticatedWithFailure" }] "Identity.Application" was not authenticated. Failure message: "Unprotect ticket failed"
2025-07-24 14:37:15.592 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ValidateHostHeader".
2025-07-24 14:37:15.594 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateValidatedTokens".
2025-07-24 14:37:15.597 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromAuthorizationHeader".
2025-07-24 14:37:15.599 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromBodyForm".
2025-07-24 14:37:15.600 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromQueryString".
2025-07-24 14:37:15.602 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 14:37:15.603 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was marked as rejected by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 14:37:15.604 +07:00 [Debug] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 9, Name: "AuthenticationSchemeNotAuthenticated" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was not authenticated.
2025-07-24 14:37:15.606 +07:00 [Information] [Microsoft.AspNetCore.Authorization.DefaultAuthorizationService] [{ Id: 2, Name: "UserAuthorizationFailed" }] Authorization failed. "These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user."
2025-07-24 14:37:15.607 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveHostChallengeProperties".
2025-07-24 14:37:15.609 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachHostChallengeError".
2025-07-24 14:37:15.611 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+AttachDefaultChallengeError".
2025-07-24 14:37:15.612 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+AttachCustomChallengeParameters".
2025-07-24 14:37:15.614 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachHttpResponseCode`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.616 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachCacheControlHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.618 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+AttachWwwAuthenticateHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.619 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The response was successfully returned as a challenge response: "{
  \"error\": \"missing_token\",
  \"error_description\": \"The security token is missing.\",
  \"error_uri\": \"https://documentation.openiddict.com/errors/ID2000\"
}".
2025-07-24 14:37:15.621 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ProcessChallengeErrorResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.623 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext" was marked as handled by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ProcessChallengeErrorResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ProcessChallengeContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 14:37:15.624 +07:00 [Information] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 12, Name: "AuthenticationSchemeChallenged" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was challenged.
2025-07-24 14:37:15.625 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 401 0 null 45.1004ms
2025-07-24 14:37:16.494 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/api/Employee/Search""" - null null
2025-07-24 14:37:16.496 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:16.497 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/api/Employee/Search""" - 204 null null 3.9226ms
2025-07-24 14:37:16.500 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/api/Employee/Search""" - "application/json" 42
2025-07-24 14:37:16.507 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:16.510 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 14:37:16.516 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 14:37:16.518 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)"'
2025-07-24 14:37:16.534 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker] [{ Id: 102, Name: "ControllerActionExecuting" }] Route matched with "{action = \"SearchEmployee\", controller = \"Employee\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] SearchEmployeeAsync(Application.Infrastructure.Models.Request.Category.Employee.SearchEmployeeRequest)" on controller "Application.API.Controllers.EmployeeController" ("Application.API").
2025-07-24 14:37:16.804 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("5"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT [o].[Id], [o].[AccountNumber], [o].[Active], [o].[Address], [o].[BankName], [o].[Classify], [o].[ClassifyGroup], [o].[CreatedBy], [o].[CreatedDate], [o].[Description], [o].[Director], [o].[Email], [o].[Fax], [o].[FullOrganizationUnitName], [o].[IPAddress], [o].[IsRoot], [o].[ModifiedBy], [o].[ModifiedDate], [o].[OrganizationUnitName], [o].[OrganizationUnitCode], [o].[ParentCode], [o].[ParentId], [o].[ShortOrganizationUnitName], [o].[SortOrder], [o].[Tel], [o].[TrainingMaterialCode], [o].[Website]
FROM [OrganizationUnit] AS [o]"
2025-07-24 14:37:17.904 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT [e].[Id], [e].[FirstName], [e].[LastName], [e].[Classify], [e].[OrganizationUnitId], NULL AS [OrganizationUnitName], [e].[EmployeeCode], [e].[Fullname], [e].[RankId], [e].[Gender], CASE
    WHEN [e].[Gender] = 1 THEN N'Nam'
    ELSE N'Nữ'
END AS [Gender_Name], [e].[AcademicRankId], [e].[YearOfAcademicRank], [e].[DegreeId], [e].[YearOfDegree], [e].[PositionId], [e].[PositionType], [e].[PartyPositionId], [e].[BirthDay], [e].[Owned], [e].[Active], [e].[ActiveAccount], [e].[IsAdministrator]
FROM [Employee] AS [e]
WHERE [e].[PositionType] IS NOT NULL AND [e].[PositionType] IN (CAST(1 AS smallint), CAST(2 AS smallint))"
2025-07-24 14:37:17.910 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor] [{ Id: 1, Name: "ObjectResultExecuting" }] Executing "OkObjectResult", writing value of type '"Application.Infrastructure.Commons.BaseSearchResponse`1[[Application.Infrastructure.Models.Response.Category.EmployeeResponse, Application.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-24 14:37:17.927 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker] [{ Id: 105, Name: "ActionExecuted" }] Executed action "Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)" in 1387.7515ms
2025-07-24 14:37:17.929 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Id: 1, Name: "ExecutedEndpoint" }] Executed endpoint '"Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)"'
2025-07-24 14:37:17.932 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/api/Employee/Search""" - 200 null "application/json; charset=utf-8" 1432.5831ms
2025-07-24 14:37:33.923 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - null null
2025-07-24 14:37:33.925 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 2.1422ms
2025-07-24 14:37:33.951 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - null null
2025-07-24 14:37:33.955 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - 200 null "application/javascript;charset=utf-8" 3.8099ms
2025-07-24 14:37:34.000 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - null null
2025-07-24 14:37:34.024 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 24.6096ms
2025-07-24 15:04:17.211 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 15:04:17.681 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 15:04:17.734 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:04:17.736 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:04:18.865 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("32"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:04:18.908 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "5fa71392-dcff-4b11-8467-d64ee63665e1" validation failed: "DuplicateRoleName".
2025-07-24 15:04:18.914 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:04:18.917 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "4b309c78-dc1a-4428-9ea3-9fed00ab95d7" validation failed: "DuplicateRoleName".
2025-07-24 15:04:18.936 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("4"ms) [Parameters=["@__defaultUser_Id_0='85649f7d-7962-4dd3-a3db-638f34c469df' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:04:18.946 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:04:18.971 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__defaultUser_Id_0='fb29e4f8-6b1f-4090-9133-0f9e7bb5acf3' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:04:18.974 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:04:18.977 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:04:19.002 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("2"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:04:19.006 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:04:19.009 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:04:19.010 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 15:04:19.011 +07:00 [Information] [app] [] Application Starting
2025-07-24 15:04:19.074 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"https://*************:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 15:04:19.084 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 15:04:19.086 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 15:04:19.087 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Development"
2025-07-24 15:04:19.089 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
2025-07-24 15:11:52.075 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 15:11:52.494 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 15:11:52.552 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:11:52.555 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:11:53.059 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("32"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:11:53.116 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "69708ff4-c7d1-4800-ac69-277f1ecbcbaf" validation failed: "DuplicateRoleName".
2025-07-24 15:11:53.122 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:11:53.124 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "e626f336-9e5f-4738-b64f-a41b069034a5" validation failed: "DuplicateRoleName".
2025-07-24 15:11:53.143 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__defaultUser_Id_0='2fd325ed-9560-4f82-8ae6-2b52e0aa066c' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:11:53.153 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:11:53.179 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__defaultUser_Id_0='39e66f6e-c3c1-4904-999e-34e58192f92f' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:11:53.181 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:11:53.185 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:11:53.211 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:11:53.215 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:11:53.218 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:11:53.219 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 15:11:53.220 +07:00 [Information] [app] [] Application Starting
2025-07-24 15:11:53.281 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"https://*************:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 15:11:53.291 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 15:11:53.292 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 15:11:53.293 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Development"
2025-07-24 15:11:53.294 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
2025-07-24 15:21:52.112 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 15:21:52.591 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 15:21:52.646 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:21:52.649 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:21:53.812 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("58"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:21:53.863 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "0b23c214-4480-4c06-9a95-7c26e2fe90e8" validation failed: "DuplicateRoleName".
2025-07-24 15:21:53.870 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:21:53.873 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "1543ff97-9335-445a-9e58-6e929962ba1b" validation failed: "DuplicateRoleName".
2025-07-24 15:21:53.894 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__defaultUser_Id_0='82d28fda-89a8-4e23-a1d1-04f0f9c73b18' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:21:53.906 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:21:53.934 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__defaultUser_Id_0='c8dc9502-574f-4342-9fe5-d0738120124f' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:21:53.938 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:21:53.940 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:21:53.966 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:21:53.970 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:21:53.972 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:21:53.973 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 15:21:53.974 +07:00 [Information] [app] [] Application Starting
2025-07-24 15:21:54.039 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"https://*************:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 15:21:54.049 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 15:21:54.051 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 15:21:54.052 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Development"
2025-07-24 15:21:54.054 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
2025-07-24 15:24:31.236 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 15:24:31.732 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 15:24:31.797 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:24:31.799 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:24:32.319 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("32"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:24:32.369 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "90b28737-d630-449b-b9d7-ca74c3afd485" validation failed: "DuplicateRoleName".
2025-07-24 15:24:32.374 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:24:32.377 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "881d1d34-215d-4507-bf7c-53984ca4b4af" validation failed: "DuplicateRoleName".
2025-07-24 15:24:32.397 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("4"ms) [Parameters=["@__defaultUser_Id_0='34806d2f-9454-446c-befa-782170d57a60' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:24:32.411 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:24:32.438 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__defaultUser_Id_0='21ff7512-bc73-48f3-94d2-ee27a5639c81' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:24:32.441 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:24:32.444 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:24:32.470 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("2"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:24:32.473 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:24:32.476 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:24:32.477 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 15:24:32.478 +07:00 [Information] [app] [] Application Starting
2025-07-24 15:24:32.551 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"https://*************:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 15:24:32.563 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 15:24:32.566 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 15:24:32.567 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Development"
2025-07-24 15:24:32.569 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
2025-07-24 15:24:56.042 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - null null
2025-07-24 15:24:56.283 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 244.2295ms
2025-07-24 15:24:56.312 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - null null
2025-07-24 15:24:56.322 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - 200 null "application/javascript;charset=utf-8" 10.2183ms
2025-07-24 15:24:56.411 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - null null
2025-07-24 15:24:56.705 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 294.3837ms
2025-07-24 15:27:38.218 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - null null
2025-07-24 15:27:38.221 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 2.7821ms
2025-07-24 15:27:38.252 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - null null
2025-07-24 15:27:38.255 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - 200 null "application/javascript;charset=utf-8" 2.3394ms
2025-07-24 15:27:38.303 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - null null
2025-07-24 15:27:38.337 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 34.3235ms
2025-07-24 15:32:41.795 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null null
2025-07-24 15:32:41.801 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:32:41.810 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 204 null null 14.6877ms
2025-07-24 15:32:41.817 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null 0
2025-07-24 15:32:41.822 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:32:41.829 +07:00 [Warning] [Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware] [{ Id: 3, Name: "FailedToDeterminePort" }] Failed to determine the https port for redirect.
2025-07-24 15:32:41.868 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 15:32:41.880 +07:00 [Information] [Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationHandler] [{ Id: 7, Name: "AuthenticationSchemeNotAuthenticatedWithFailure" }] "Identity.Application" was not authenticated. Failure message: "Unprotect ticket failed"
2025-07-24 15:32:41.886 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ValidateHostHeader".
2025-07-24 15:32:41.888 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateValidatedTokens".
2025-07-24 15:32:41.891 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromAuthorizationHeader".
2025-07-24 15:32:41.895 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromBodyForm".
2025-07-24 15:32:41.897 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromQueryString".
2025-07-24 15:32:41.899 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 15:32:41.939 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+CreateHttpClient`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:41.942 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+PrepareGetHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:41.944 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachHttpVersion`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:41.948 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachJsonAcceptHeaders`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:41.950 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachUserAgentHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:41.958 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachFromHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:41.961 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachHttpParameters`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:41.966 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.LogicalHandler] [{ Id: 100, Name: "RequestPipelineStart" }] Start processing HTTP request "GET" "http://localhost:44395/.well-known/openid-configuration"
2025-07-24 15:32:41.986 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.ClientHandler] [{ Id: 100, Name: "RequestStart" }] Sending HTTP request "GET" "http://localhost:44395/.well-known/openid-configuration"
2025-07-24 15:32:42.090 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.ClientHandler] [{ Id: 101, Name: "RequestEnd" }] Received HTTP response headers after 99.9207ms - 200
2025-07-24 15:32:42.098 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.LogicalHandler] [{ Id: 101, Name: "RequestPipelineEnd" }] End processing HTTP request after 133.5966ms - 200
2025-07-24 15:32:42.103 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ApplyConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+SendHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+ApplyConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.106 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ApplyConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DisposeHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+ApplyConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.110 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The configuration request was successfully sent to http://localhost:44395/.well-known/openid-configuration: "{}".
2025-07-24 15:32:42.129 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DecompressResponseContent`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.153 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ExtractJsonHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.156 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ExtractWwwAuthenticateHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.159 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ValidateHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.162 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DisposeHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.165 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The configuration response returned by http://localhost:44395/.well-known/openid-configuration was successfully extracted: "{
  \"issuer\": \"http://localhost:44395/\",
  \"authorization_endpoint\": \"http://localhost:44395/connect/authorize\",
  \"token_endpoint\": \"http://localhost:44395/connect/token\",
  \"introspection_endpoint\": \"http://localhost:44395/connect/introspect\",
  \"end_session_endpoint\": \"http://localhost:44395/connect/logout\",
  \"userinfo_endpoint\": \"http://localhost:44395/connect/userinfo\",
  \"jwks_uri\": \"http://localhost:44395/.well-known/jwks\",
  \"grant_types_supported\": [
    \"authorization_code\",
    \"implicit\",
    \"client_credentials\",
    \"refresh_token\",
    \"password\"
  ],
  \"response_types_supported\": [
    \"code\",
    \"code id_token\",
    \"code id_token token\",
    \"code token\"
  ],
  \"response_modes_supported\": [
    \"query\",
    \"form_post\",
    \"fragment\"
  ],
  \"scopes_supported\": [
    \"openid\",
    \"offline_access\",
    \"email\",
    \"profile\",
    \"roles\",
    \"dataEventRecords\"
  ],
  \"claims_supported\": [
    \"aud\",
    \"exp\",
    \"iat\",
    \"iss\",
    \"sub\"
  ],
  \"id_token_signing_alg_values_supported\": [
    \"RS256\"
  ],
  \"code_challenge_methods_supported\": [
    \"plain\",
    \"S256\"
  ],
  \"subject_types_supported\": [
    \"public\"
  ],
  \"prompt_values_supported\": [
    \"consent\",
    \"login\",
    \"none\",
    \"select_account\"
  ],
  \"token_endpoint_auth_methods_supported\": [
    \"client_secret_post\",
    \"private_key_jwt\",
    \"client_secret_basic\"
  ],
  \"introspection_endpoint_auth_methods_supported\": [
    \"client_secret_post\",
    \"private_key_jwt\",
    \"client_secret_basic\"
  ],
  \"require_pushed_authorization_requests\": false,
  \"claims_parameter_supported\": false,
  \"request_parameter_supported\": false,
  \"request_uri_parameter_supported\": false,
  \"tls_client_certificate_bound_access_tokens\": false,
  \"authorization_response_iss_parameter_supported\": true
}".
2025-07-24 15:32:42.170 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ValidateWellKnownConfigurationParameters".
2025-07-24 15:32:42.172 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+HandleConfigurationErrorResponse".
2025-07-24 15:32:42.175 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ValidateIssuer".
2025-07-24 15:32:42.177 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ExtractJsonWebKeySetEndpoint".
2025-07-24 15:32:42.179 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ExtractIntrospectionEndpoint".
2025-07-24 15:32:42.181 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ExtractMtlsIntrospectionEndpoint".
2025-07-24 15:32:42.184 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ExtractIntrospectionEndpointClientAuthenticationMethods".
2025-07-24 15:32:42.187 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+CreateHttpClient`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.189 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+PrepareGetHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.191 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachHttpVersion`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.193 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachJsonAcceptHeaders`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.195 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachUserAgentHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.196 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachFromHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.198 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachHttpParameters`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.201 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.LogicalHandler] [{ Id: 100, Name: "RequestPipelineStart" }] Start processing HTTP request "GET" "http://localhost:44395/.well-known/jwks"
2025-07-24 15:32:42.202 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.ClientHandler] [{ Id: 100, Name: "RequestStart" }] Sending HTTP request "GET" "http://localhost:44395/.well-known/jwks"
2025-07-24 15:32:42.206 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.ClientHandler] [{ Id: 101, Name: "RequestEnd" }] Received HTTP response headers after 2.7814ms - 200
2025-07-24 15:32:42.209 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.LogicalHandler] [{ Id: 101, Name: "RequestPipelineEnd" }] End processing HTTP request after 8.2109ms - 200
2025-07-24 15:32:42.211 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ApplyJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+SendHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+ApplyJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.213 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ApplyJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DisposeHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+ApplyJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.215 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The JSON Web Key Set request was successfully sent to http://localhost:44395/.well-known/jwks: "{}".
2025-07-24 15:32:42.218 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DecompressResponseContent`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.220 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ExtractJsonHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.222 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ExtractWwwAuthenticateHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.224 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ValidateHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.227 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DisposeHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:32:42.229 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The JSON Web Key Set response returned by http://localhost:44395/.well-known/jwks was successfully extracted: "{
  \"keys\": [
    {
      \"kid\": \"2468D7B7263DFCCCA7336E72595BDFE0A60EEFC9\",
      \"use\": \"sig\",
      \"kty\": \"RSA\",
      \"alg\": \"RS256\",
      \"e\": \"AQAB\",
      \"n\": \"s84wbiZaD3_-RMD9to6qrUeXeBKzcQA225jxa-W1lXmqd-fNS6BqvUkVtAyC6ZRTZj3kiMWJWCsLoudOHN1MeEP-FrRIAvBY8lFus7KLVrUMu210b_beQltpGq-u4kgaabG5EzFIJ4VJbKYX1BLiH3GSpJu55463tinqKaG4gdi7qzdG5ExhjTpdsVYfYlvBa6rjnHPf3nwxg2Xt2eXe38ANebL5gRwJnUhZpHQxZySC6yPmBioYuCyeQ1_OeEpdEiQ-HnnePI-wlAkNBku6e8hVoS2zIV7alL3OBRjP6mq8hV_20_CanphTRRwVlpIITJbFY7eo4BVj0w2qFTbInQ\",
      \"x5t\": \"JGjXtyY9_MynM25yWVvf4KYO78k\",
      \"x5c\": [
        \"MIIC7DCCAdSgAwIBAgIQaPEhJxuv1LRPNDyz3f8VwTANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDEwlsb2NhbGhvc3QwHhcNMjUwNzAyMDI0MTMxWhcNMzAwNzAyMDAwMDAwWjAUMRIwEAYDVQQDEwlsb2NhbGhvc3QwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCzzjBuJloPf/5EwP22jqqtR5d4ErNxADbbmPFr5bWVeap3581LoGq9SRW0DILplFNmPeSIxYlYKwui504c3Ux4Q/4WtEgC8FjyUW6zsotWtQy7bXRv9t5CW2kar67iSBppsbkTMUgnhUlsphfUEuIfcZKkm7nnjre2KeopobiB2LurN0bkTGGNOl2xVh9iW8FrquOcc9/efDGDZe3Z5d7fwA15svmBHAmdSFmkdDFnJILrI+YGKhi4LJ5DX854Sl0SJD4eed48j7CUCQ0GS7p7yFWhLbMhXtqUvc4FGM/qaryFX/bT8JqemFNFHBWWkghMlsVjt6jgFWPTDaoVNsidAgMBAAGjOjA4MAsGA1UdDwQEAwIEsDATBgNVHSUEDDAKBggrBgEFBQcDATAUBgNVHREEDTALgglsb2NhbGhvc3QwDQYJKoZIhvcNAQELBQADggEBAJHNGfbW3IvriL5o5hAbtvfbc5k2D7ZDqW/+5N1HfZi5VVPYWGcchE2eJdnDQuWgShVmPtk8+de9wgHIfiqCRKyqt9ksDv/prv39Xs1AkwrDFSf8n+SG3JEf5p2satacgsOLlxG3VkfHW1BTFEdQwzZuYyTbK7d6xTyVr2nfRkV3Rq+PPf5oa2gJgUJvtlTBSHeLpCxBypikSO930isep3RxCqSSKmNKJiFVqHGzlRiRImFClSmtBfbWkF1F8cmBRQYxMHhIrZ5MVPm7vqT7klCH9uKvnpfYYWY0NzT5BwBhvwgmhx7xrFa9oC6+DEWBSBb+HhmyMezeyc+8p2lH7r4=\"
      ]
    },
    {
      \"kid\": \"319451997D7F2A562073E617012F271EF6708889\",
      \"use\": \"sig\",
      \"kty\": \"RSA\",
      \"alg\": \"RS256\",
      \"e\": \"AQAB\",
      \"n\": \"7aexhMeAPlGw9WZY8C4HI7zoI1N_AZ62Kks8BLUXkGhrKPYv5fN6cURIMHffSTpfjCs0CkDFxvMgoigaXTlxi_mig4kaf04QFT_yF1ZaSPjExH59l9hinv840Sg5zYu7acAZbShjxEiGGs_MiNZY1vIpSJyojhi-gvQndInwKYpbykhGzl_vMaUscc3AVh66NMLaxjuRq5CuQWd2wRxbb1qHPRzFFgyJwtJSjlqNwSuCYRuLI5hL5o2AMw0ZPyo94cNomyiL6sgCW5DT_5y0NyxxmiPni1EYrwdENvg4psYVmnkMxKfl1ZlmR3dENXH1q5HLb5zdcCWGO3W54kywSQ\",
      \"x5t\": \"MZRRmX1_KlYgc-YXAS8nHvZwiIk\",
      \"x5c\": [
        \"MIIC9DCCAdygAwIBAgIIfzUYxb/QsYgwDQYJKoZIhvcNAQELBQAwMDEuMCwGA1UEAxMlT3BlbklkZGljdCBTZXJ2ZXIgU2lnbmluZyBDZXJ0aWZpY2F0ZTAeFw0yNTA3MjEwNTM5MjFaFw0yNzA3MjEwNTM5MjFaMDAxLjAsBgNVBAMTJU9wZW5JZGRpY3QgU2VydmVyIFNpZ25pbmcgQ2VydGlmaWNhdGUwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDtp7GEx4A+UbD1ZljwLgcjvOgjU38BnrYqSzwEtReQaGso9i/l83pxREgwd99JOl+MKzQKQMXG8yCiKBpdOXGL+aKDiRp/ThAVP/IXVlpI+MTEfn2X2GKe/zjRKDnNi7tpwBltKGPESIYaz8yI1ljW8ilInKiOGL6C9Cd0ifApilvKSEbOX+8xpSxxzcBWHro0wtrGO5GrkK5BZ3bBHFtvWoc9HMUWDInC0lKOWo3BK4JhG4sjmEvmjYAzDRk/Kj3hw2ibKIvqyAJbkNP/nLQ3LHGaI+eLURivB0Q2+DimxhWaeQzEp+XVmWZHd0Q1cfWrkctvnN1wJYY7dbniTLBJAgMBAAGjEjAQMA4GA1UdDwEB/wQEAwIHgDANBgkqhkiG9w0BAQsFAAOCAQEAFw4DDqmhpaM0uYlwnFLa+6u2mBeyqQiL6E5qv2uadZCYz0IARDSO+Y200j2Yv3ZCN8aVna/5FbFYCaN8q9PTb/m8WcGBoYaxXrrpgbas7EQjp69P/U8REmEBClIAtAYk1c9lEM4luW4eyWTBmGIMV6Nnnwe42LeEmiQDlw7l7uMpJUQge5WeK09De0V8dIYrUM+G56icO1uNSTtuPAXVI+OnJvlfM36wFbXW0GcP87B1XoMgfMvnZs2XLtXo/LibWkdtlNzto3QQaNBNkZebU0yIn/B5aO+jDsFmHy7TezZKrQEYJC4dK+rDirK3tNhOd3tDMuKU7JsVqllDLwqI0g==\"
      ]
    }
  ]
}".
2025-07-24 15:32:42.232 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ValidateWellKnownJsonWebKeySetParameters".
2025-07-24 15:32:42.234 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+HandleJsonWebKeySetErrorResponse".
2025-07-24 15:32:42.238 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ExtractSigningKeys".
2025-07-24 15:32:42.252 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ResolveServerConfiguration".
2025-07-24 15:32:42.254 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateIntrospectionRequest".
2025-07-24 15:32:42.257 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachNonDefaultIntrospectionEndpointClientAuthenticationMethod".
2025-07-24 15:32:42.265 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ResolveTokenValidationParameters".
2025-07-24 15:32:42.267 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+RemoveDisallowedCharacters".
2025-07-24 15:32:42.298 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateIdentityModelToken".
2025-07-24 15:32:42.302 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+NormalizeScopeClaims".
2025-07-24 15:32:42.306 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+MapInternalClaims".
2025-07-24 15:32:42.308 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidatePrincipal".
2025-07-24 15:32:42.310 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateExpirationDate".
2025-07-24 15:32:42.312 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateAudience".
2025-07-24 15:32:42.314 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateAccessToken".
2025-07-24 15:32:42.317 +07:00 [Debug] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 8, Name: "AuthenticationSchemeAuthenticated" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was successfully authenticated.
2025-07-24 15:32:42.322 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:32:42.324 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"/dataHub/negotiate"'
2025-07-24 15:32:42.330 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Id: 1, Name: "ExecutedEndpoint" }] Executed endpoint '"/dataHub/negotiate"'
2025-07-24 15:32:42.333 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 200 316 "application/json" 516.0466ms
2025-07-24 15:32:42.500 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/dataHub""?id=QSibQe0GBo2KHUnwy6Rzow&access_token=eyJhbGciOiJSUzI1NiIsImtpZCI6IjI0NjhEN0I3MjYzREZDQ0NBNzMzNkU3MjU5NUJERkUwQTYwRUVGQzkiLCJ4NXQiOiJKR2pYdHlZOV9NeW5NMjV5V1Z2ZjRLWU83OGsiLCJ0eXAiOiJhdCtqd3QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j-7CrifUsWJSAj5UI7hKIzf2HzrfSSCQE9QLFfchlwnaVzXTwPIeQjTOrFb9i8gjyItZVW3ux4KnMZqiL5Q9kRD3oHbdc0ErCQ7C-hfP94H-YWvnT83ASLLEwgz5YdM5q-2sTrAvcePaz91V-jJKvwGoR0_RyzfKQg2aHpCtu-_W8-IRVQBOw11HkD3bspjqh1H2m7sCvIhnq1dDIJcczADmxz9POha0vrBZqQZkhK418oEmeMpRG3QLFyVNqbCJ4KPrLWmcmetKxeZHB5iX6ATcs2tm3m-i1QsgQBgb_bbEB1o2wiQ7-SgiObzQ8A9pv8Mjsj5o37IOq8Y5vcjTSg" - null null
2025-07-24 15:32:42.502 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:32:42.505 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 15:32:42.508 +07:00 [Information] [Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationHandler] [{ Id: 7, Name: "AuthenticationSchemeNotAuthenticatedWithFailure" }] "Identity.Application" was not authenticated. Failure message: "Unprotect ticket failed"
2025-07-24 15:32:42.510 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ValidateHostHeader".
2025-07-24 15:32:42.512 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateValidatedTokens".
2025-07-24 15:32:42.514 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromAuthorizationHeader".
2025-07-24 15:32:42.516 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromBodyForm".
2025-07-24 15:32:42.517 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromQueryString".
2025-07-24 15:32:42.519 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 15:32:42.521 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ResolveServerConfiguration".
2025-07-24 15:32:42.522 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateIntrospectionRequest".
2025-07-24 15:32:42.525 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachNonDefaultIntrospectionEndpointClientAuthenticationMethod".
2025-07-24 15:32:42.527 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ResolveTokenValidationParameters".
2025-07-24 15:32:42.529 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+RemoveDisallowedCharacters".
2025-07-24 15:32:42.532 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateIdentityModelToken".
2025-07-24 15:32:42.533 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+NormalizeScopeClaims".
2025-07-24 15:32:42.535 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+MapInternalClaims".
2025-07-24 15:32:42.537 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidatePrincipal".
2025-07-24 15:32:42.539 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateExpirationDate".
2025-07-24 15:32:42.541 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateAudience".
2025-07-24 15:32:42.544 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateAccessToken".
2025-07-24 15:32:42.546 +07:00 [Debug] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 8, Name: "AuthenticationSchemeAuthenticated" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was successfully authenticated.
2025-07-24 15:32:42.548 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:32:42.550 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"/dataHub"'
2025-07-24 15:32:42.772 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/api/Employee/Search""" - null null
2025-07-24 15:32:42.774 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:32:42.810 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/api/Employee/Search""" - 204 null null 37.7309ms
2025-07-24 15:32:42.812 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/api/Employee/Search""" - "application/json" 42
2025-07-24 15:32:42.818 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:32:42.820 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 15:32:42.824 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:32:42.825 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)"'
2025-07-24 15:32:42.839 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker] [{ Id: 102, Name: "ControllerActionExecuting" }] Route matched with "{action = \"SearchEmployee\", controller = \"Employee\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] SearchEmployeeAsync(Application.Infrastructure.Models.Request.Category.Employee.SearchEmployeeRequest)" on controller "Application.API.Controllers.EmployeeController" ("Application.API").
2025-07-24 15:32:42.987 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT [o].[Id], [o].[AccountNumber], [o].[Active], [o].[Address], [o].[BankName], [o].[Classify], [o].[ClassifyGroup], [o].[CreatedBy], [o].[CreatedDate], [o].[Description], [o].[Director], [o].[Email], [o].[Fax], [o].[FullOrganizationUnitName], [o].[IPAddress], [o].[IsRoot], [o].[ModifiedBy], [o].[ModifiedDate], [o].[OrganizationUnitName], [o].[OrganizationUnitCode], [o].[ParentCode], [o].[ParentId], [o].[ShortOrganizationUnitName], [o].[SortOrder], [o].[Tel], [o].[TrainingMaterialCode], [o].[Website]
FROM [OrganizationUnit] AS [o]"
2025-07-24 15:32:43.389 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("4"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT [e].[Id], [e].[FirstName], [e].[LastName], [e].[Classify], [e].[OrganizationUnitId], NULL AS [OrganizationUnitName], [e].[EmployeeCode], [e].[Fullname], [e].[RankId], [e].[Gender], CASE
    WHEN [e].[Gender] = 1 THEN N'Nam'
    ELSE N'Nữ'
END AS [Gender_Name], [e].[AcademicRankId], [e].[YearOfAcademicRank], [e].[DegreeId], [e].[YearOfDegree], [e].[PositionId], [e].[PositionType], [e].[PartyPositionId], [e].[BirthDay], [e].[Owned], [e].[Active], [e].[ActiveAccount], [e].[IsAdministrator]
FROM [Employee] AS [e]
WHERE [e].[PositionType] IS NOT NULL AND [e].[PositionType] IN (CAST(1 AS smallint), CAST(2 AS smallint))"
2025-07-24 15:32:43.394 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor] [{ Id: 1, Name: "ObjectResultExecuting" }] Executing "OkObjectResult", writing value of type '"Application.Infrastructure.Commons.BaseSearchResponse`1[[Application.Infrastructure.Models.Response.Category.EmployeeResponse, Application.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-24 15:32:43.409 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker] [{ Id: 105, Name: "ActionExecuted" }] Executed action "Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)" in 566.7007ms
2025-07-24 15:32:43.411 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Id: 1, Name: "ExecutedEndpoint" }] Executed endpoint '"Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)"'
2025-07-24 15:32:43.415 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/api/Employee/Search""" - 200 null "application/json; charset=utf-8" 602.6802ms
2025-07-24 15:36:12.617 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - null null
2025-07-24 15:36:12.619 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 2.1006ms
2025-07-24 15:36:12.647 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - null null
2025-07-24 15:36:12.649 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - 200 null "application/javascript;charset=utf-8" 1.8284ms
2025-07-24 15:36:12.732 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - null null
2025-07-24 15:36:12.756 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 24.0192ms
2025-07-24 15:39:41.117 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 15:39:41.648 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 15:39:41.720 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:39:41.724 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:39:42.200 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("21"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:39:42.249 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "20ecbcc1-02c4-4035-8945-1a5b664a1d2e" validation failed: "DuplicateRoleName".
2025-07-24 15:39:42.254 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:39:42.260 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "bc700a29-235d-4ac1-80b7-45ebc70d215a" validation failed: "DuplicateRoleName".
2025-07-24 15:39:42.278 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__defaultUser_Id_0='1ef00825-f433-410c-8a25-e03fdc150c91' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:39:42.294 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("2"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:39:42.321 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__defaultUser_Id_0='ff1558be-adc8-4de5-b91d-c77d79d5cb4f' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:39:42.326 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:39:42.331 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:39:42.361 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:39:42.366 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:39:42.369 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:39:42.371 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 15:39:42.372 +07:00 [Information] [app] [] Application Starting
2025-07-24 15:39:42.403 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"http://localhost:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 15:39:42.417 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 15:39:42.419 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 15:39:42.420 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Development"
2025-07-24 15:39:42.421 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
2025-07-24 15:41:42.195 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null null
2025-07-24 15:41:42.228 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:42.236 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 204 null null 41.8054ms
2025-07-24 15:41:42.242 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null 0
2025-07-24 15:41:42.245 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:42.254 +07:00 [Warning] [Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware] [{ Id: 3, Name: "FailedToDeterminePort" }] Failed to determine the https port for redirect.
2025-07-24 15:41:42.270 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 15:41:42.277 +07:00 [Information] [Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationHandler] [{ Id: 7, Name: "AuthenticationSchemeNotAuthenticatedWithFailure" }] "Identity.Application" was not authenticated. Failure message: "Unprotect ticket failed"
2025-07-24 15:41:42.284 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ValidateHostHeader".
2025-07-24 15:41:42.288 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateValidatedTokens".
2025-07-24 15:41:42.292 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromAuthorizationHeader".
2025-07-24 15:41:42.296 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromBodyForm".
2025-07-24 15:41:42.299 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromQueryString".
2025-07-24 15:41:42.302 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 15:41:42.326 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+CreateHttpClient`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.328 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+PrepareGetHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.330 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachHttpVersion`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.334 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachJsonAcceptHeaders`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.336 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachUserAgentHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.339 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachFromHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.341 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachHttpParameters`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.345 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.LogicalHandler] [{ Id: 100, Name: "RequestPipelineStart" }] Start processing HTTP request "GET" "http://localhost:44395/.well-known/openid-configuration"
2025-07-24 15:41:42.355 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.ClientHandler] [{ Id: 100, Name: "RequestStart" }] Sending HTTP request "GET" "http://localhost:44395/.well-known/openid-configuration"
2025-07-24 15:41:42.383 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.ClientHandler] [{ Id: 101, Name: "RequestEnd" }] Received HTTP response headers after 24.4595ms - 200
2025-07-24 15:41:42.392 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.LogicalHandler] [{ Id: 101, Name: "RequestPipelineEnd" }] End processing HTTP request after 47.5594ms - 200
2025-07-24 15:41:42.396 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ApplyConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+SendHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+ApplyConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.400 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ApplyConfigurationRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DisposeHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+ApplyConfigurationRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.402 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The configuration request was successfully sent to http://localhost:44395/.well-known/openid-configuration: "{}".
2025-07-24 15:41:42.406 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DecompressResponseContent`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.415 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ExtractJsonHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.419 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ExtractWwwAuthenticateHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.421 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ValidateHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.422 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DisposeHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractConfigurationResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.424 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The configuration response returned by http://localhost:44395/.well-known/openid-configuration was successfully extracted: "{
  \"issuer\": \"http://localhost:44395/\",
  \"authorization_endpoint\": \"http://localhost:44395/connect/authorize\",
  \"token_endpoint\": \"http://localhost:44395/connect/token\",
  \"introspection_endpoint\": \"http://localhost:44395/connect/introspect\",
  \"end_session_endpoint\": \"http://localhost:44395/connect/logout\",
  \"userinfo_endpoint\": \"http://localhost:44395/connect/userinfo\",
  \"jwks_uri\": \"http://localhost:44395/.well-known/jwks\",
  \"grant_types_supported\": [
    \"authorization_code\",
    \"implicit\",
    \"client_credentials\",
    \"refresh_token\",
    \"password\"
  ],
  \"response_types_supported\": [
    \"code\",
    \"code id_token\",
    \"code id_token token\",
    \"code token\"
  ],
  \"response_modes_supported\": [
    \"query\",
    \"form_post\",
    \"fragment\"
  ],
  \"scopes_supported\": [
    \"openid\",
    \"offline_access\",
    \"email\",
    \"profile\",
    \"roles\",
    \"dataEventRecords\"
  ],
  \"claims_supported\": [
    \"aud\",
    \"exp\",
    \"iat\",
    \"iss\",
    \"sub\"
  ],
  \"id_token_signing_alg_values_supported\": [
    \"RS256\"
  ],
  \"code_challenge_methods_supported\": [
    \"plain\",
    \"S256\"
  ],
  \"subject_types_supported\": [
    \"public\"
  ],
  \"prompt_values_supported\": [
    \"consent\",
    \"login\",
    \"none\",
    \"select_account\"
  ],
  \"token_endpoint_auth_methods_supported\": [
    \"client_secret_post\",
    \"private_key_jwt\",
    \"client_secret_basic\"
  ],
  \"introspection_endpoint_auth_methods_supported\": [
    \"client_secret_post\",
    \"private_key_jwt\",
    \"client_secret_basic\"
  ],
  \"require_pushed_authorization_requests\": false,
  \"claims_parameter_supported\": false,
  \"request_parameter_supported\": false,
  \"request_uri_parameter_supported\": false,
  \"tls_client_certificate_bound_access_tokens\": false,
  \"authorization_response_iss_parameter_supported\": true
}".
2025-07-24 15:41:42.427 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ValidateWellKnownConfigurationParameters".
2025-07-24 15:41:42.429 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+HandleConfigurationErrorResponse".
2025-07-24 15:41:42.430 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ValidateIssuer".
2025-07-24 15:41:42.433 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ExtractJsonWebKeySetEndpoint".
2025-07-24 15:41:42.435 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ExtractIntrospectionEndpoint".
2025-07-24 15:41:42.437 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ExtractMtlsIntrospectionEndpoint".
2025-07-24 15:41:42.439 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleConfigurationResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ExtractIntrospectionEndpointClientAuthenticationMethods".
2025-07-24 15:41:42.441 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+CreateHttpClient`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.444 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+PrepareGetHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.449 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachHttpVersion`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.453 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachJsonAcceptHeaders`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.455 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachUserAgentHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.457 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachFromHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.461 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachHttpParameters`1[[OpenIddict.Validation.OpenIddictValidationEvents+PrepareJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.465 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.LogicalHandler] [{ Id: 100, Name: "RequestPipelineStart" }] Start processing HTTP request "GET" "http://localhost:44395/.well-known/jwks"
2025-07-24 15:41:42.468 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.ClientHandler] [{ Id: 100, Name: "RequestStart" }] Sending HTTP request "GET" "http://localhost:44395/.well-known/jwks"
2025-07-24 15:41:42.477 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.ClientHandler] [{ Id: 101, Name: "RequestEnd" }] Received HTTP response headers after 6.0081ms - 200
2025-07-24 15:41:42.480 +07:00 [Information] [System.Net.Http.HttpClient.OpenIddict.Validation.SystemNetHttp.LogicalHandler] [{ Id: 101, Name: "RequestPipelineEnd" }] End processing HTTP request after 15.0581ms - 200
2025-07-24 15:41:42.483 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ApplyJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+SendHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+ApplyJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.485 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ApplyJsonWebKeySetRequestContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DisposeHttpRequest`1[[OpenIddict.Validation.OpenIddictValidationEvents+ApplyJsonWebKeySetRequestContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.488 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The JSON Web Key Set request was successfully sent to http://localhost:44395/.well-known/jwks: "{}".
2025-07-24 15:41:42.492 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DecompressResponseContent`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.494 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ExtractJsonHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.495 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ExtractWwwAuthenticateHeader`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.496 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+ValidateHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.499 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+DisposeHttpResponse`1[[OpenIddict.Validation.OpenIddictValidationEvents+ExtractJsonWebKeySetResponseContext, OpenIddict.Validation, Version=*******, Culture=neutral, PublicKeyToken=35a561290d20de2f]]".
2025-07-24 15:41:42.502 +07:00 [Information] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The JSON Web Key Set response returned by http://localhost:44395/.well-known/jwks was successfully extracted: "{
  \"keys\": [
    {
      \"kid\": \"2468D7B7263DFCCCA7336E72595BDFE0A60EEFC9\",
      \"use\": \"sig\",
      \"kty\": \"RSA\",
      \"alg\": \"RS256\",
      \"e\": \"AQAB\",
      \"n\": \"s84wbiZaD3_-RMD9to6qrUeXeBKzcQA225jxa-W1lXmqd-fNS6BqvUkVtAyC6ZRTZj3kiMWJWCsLoudOHN1MeEP-FrRIAvBY8lFus7KLVrUMu210b_beQltpGq-u4kgaabG5EzFIJ4VJbKYX1BLiH3GSpJu55463tinqKaG4gdi7qzdG5ExhjTpdsVYfYlvBa6rjnHPf3nwxg2Xt2eXe38ANebL5gRwJnUhZpHQxZySC6yPmBioYuCyeQ1_OeEpdEiQ-HnnePI-wlAkNBku6e8hVoS2zIV7alL3OBRjP6mq8hV_20_CanphTRRwVlpIITJbFY7eo4BVj0w2qFTbInQ\",
      \"x5t\": \"JGjXtyY9_MynM25yWVvf4KYO78k\",
      \"x5c\": [
        \"MIIC7DCCAdSgAwIBAgIQaPEhJxuv1LRPNDyz3f8VwTANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDEwlsb2NhbGhvc3QwHhcNMjUwNzAyMDI0MTMxWhcNMzAwNzAyMDAwMDAwWjAUMRIwEAYDVQQDEwlsb2NhbGhvc3QwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCzzjBuJloPf/5EwP22jqqtR5d4ErNxADbbmPFr5bWVeap3581LoGq9SRW0DILplFNmPeSIxYlYKwui504c3Ux4Q/4WtEgC8FjyUW6zsotWtQy7bXRv9t5CW2kar67iSBppsbkTMUgnhUlsphfUEuIfcZKkm7nnjre2KeopobiB2LurN0bkTGGNOl2xVh9iW8FrquOcc9/efDGDZe3Z5d7fwA15svmBHAmdSFmkdDFnJILrI+YGKhi4LJ5DX854Sl0SJD4eed48j7CUCQ0GS7p7yFWhLbMhXtqUvc4FGM/qaryFX/bT8JqemFNFHBWWkghMlsVjt6jgFWPTDaoVNsidAgMBAAGjOjA4MAsGA1UdDwQEAwIEsDATBgNVHSUEDDAKBggrBgEFBQcDATAUBgNVHREEDTALgglsb2NhbGhvc3QwDQYJKoZIhvcNAQELBQADggEBAJHNGfbW3IvriL5o5hAbtvfbc5k2D7ZDqW/+5N1HfZi5VVPYWGcchE2eJdnDQuWgShVmPtk8+de9wgHIfiqCRKyqt9ksDv/prv39Xs1AkwrDFSf8n+SG3JEf5p2satacgsOLlxG3VkfHW1BTFEdQwzZuYyTbK7d6xTyVr2nfRkV3Rq+PPf5oa2gJgUJvtlTBSHeLpCxBypikSO930isep3RxCqSSKmNKJiFVqHGzlRiRImFClSmtBfbWkF1F8cmBRQYxMHhIrZ5MVPm7vqT7klCH9uKvnpfYYWY0NzT5BwBhvwgmhx7xrFa9oC6+DEWBSBb+HhmyMezeyc+8p2lH7r4=\"
      ]
    },
    {
      \"kid\": \"319451997D7F2A562073E617012F271EF6708889\",
      \"use\": \"sig\",
      \"kty\": \"RSA\",
      \"alg\": \"RS256\",
      \"e\": \"AQAB\",
      \"n\": \"7aexhMeAPlGw9WZY8C4HI7zoI1N_AZ62Kks8BLUXkGhrKPYv5fN6cURIMHffSTpfjCs0CkDFxvMgoigaXTlxi_mig4kaf04QFT_yF1ZaSPjExH59l9hinv840Sg5zYu7acAZbShjxEiGGs_MiNZY1vIpSJyojhi-gvQndInwKYpbykhGzl_vMaUscc3AVh66NMLaxjuRq5CuQWd2wRxbb1qHPRzFFgyJwtJSjlqNwSuCYRuLI5hL5o2AMw0ZPyo94cNomyiL6sgCW5DT_5y0NyxxmiPni1EYrwdENvg4psYVmnkMxKfl1ZlmR3dENXH1q5HLb5zdcCWGO3W54kywSQ\",
      \"x5t\": \"MZRRmX1_KlYgc-YXAS8nHvZwiIk\",
      \"x5c\": [
        \"MIIC9DCCAdygAwIBAgIIfzUYxb/QsYgwDQYJKoZIhvcNAQELBQAwMDEuMCwGA1UEAxMlT3BlbklkZGljdCBTZXJ2ZXIgU2lnbmluZyBDZXJ0aWZpY2F0ZTAeFw0yNTA3MjEwNTM5MjFaFw0yNzA3MjEwNTM5MjFaMDAxLjAsBgNVBAMTJU9wZW5JZGRpY3QgU2VydmVyIFNpZ25pbmcgQ2VydGlmaWNhdGUwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDtp7GEx4A+UbD1ZljwLgcjvOgjU38BnrYqSzwEtReQaGso9i/l83pxREgwd99JOl+MKzQKQMXG8yCiKBpdOXGL+aKDiRp/ThAVP/IXVlpI+MTEfn2X2GKe/zjRKDnNi7tpwBltKGPESIYaz8yI1ljW8ilInKiOGL6C9Cd0ifApilvKSEbOX+8xpSxxzcBWHro0wtrGO5GrkK5BZ3bBHFtvWoc9HMUWDInC0lKOWo3BK4JhG4sjmEvmjYAzDRk/Kj3hw2ibKIvqyAJbkNP/nLQ3LHGaI+eLURivB0Q2+DimxhWaeQzEp+XVmWZHd0Q1cfWrkctvnN1wJYY7dbniTLBJAgMBAAGjEjAQMA4GA1UdDwEB/wQEAwIHgDANBgkqhkiG9w0BAQsFAAOCAQEAFw4DDqmhpaM0uYlwnFLa+6u2mBeyqQiL6E5qv2uadZCYz0IARDSO+Y200j2Yv3ZCN8aVna/5FbFYCaN8q9PTb/m8WcGBoYaxXrrpgbas7EQjp69P/U8REmEBClIAtAYk1c9lEM4luW4eyWTBmGIMV6Nnnwe42LeEmiQDlw7l7uMpJUQge5WeK09De0V8dIYrUM+G56icO1uNSTtuPAXVI+OnJvlfM36wFbXW0GcP87B1XoMgfMvnZs2XLtXo/LibWkdtlNzto3QQaNBNkZebU0yIn/B5aO+jDsFmHy7TezZKrQEYJC4dK+rDirK3tNhOd3tDMuKU7JsVqllDLwqI0g==\"
      ]
    }
  ]
}".
2025-07-24 15:41:42.505 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ValidateWellKnownJsonWebKeySetParameters".
2025-07-24 15:41:42.506 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+HandleJsonWebKeySetErrorResponse".
2025-07-24 15:41:42.508 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+HandleJsonWebKeySetResponseContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Discovery+ExtractSigningKeys".
2025-07-24 15:41:42.513 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ResolveServerConfiguration".
2025-07-24 15:41:42.515 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateIntrospectionRequest".
2025-07-24 15:41:42.517 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachNonDefaultIntrospectionEndpointClientAuthenticationMethod".
2025-07-24 15:41:42.521 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ResolveTokenValidationParameters".
2025-07-24 15:41:42.522 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+RemoveDisallowedCharacters".
2025-07-24 15:41:42.549 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateIdentityModelToken".
2025-07-24 15:41:42.552 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+NormalizeScopeClaims".
2025-07-24 15:41:42.555 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+MapInternalClaims".
2025-07-24 15:41:42.556 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidatePrincipal".
2025-07-24 15:41:42.557 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateExpirationDate".
2025-07-24 15:41:42.559 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateAudience".
2025-07-24 15:41:42.560 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateAccessToken".
2025-07-24 15:41:42.562 +07:00 [Debug] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 8, Name: "AuthenticationSchemeAuthenticated" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was successfully authenticated.
2025-07-24 15:41:42.566 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:42.568 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"/dataHub/negotiate"'
2025-07-24 15:41:42.574 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Id: 1, Name: "ExecutedEndpoint" }] Executed endpoint '"/dataHub/negotiate"'
2025-07-24 15:41:42.577 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 200 316 "application/json" 334.6807ms
2025-07-24 15:41:42.581 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/dataHub""?id=iR7v53AGfqD2IJc1o3wLYA&access_token=eyJhbGciOiJSUzI1NiIsImtpZCI6IjI0NjhEN0I3MjYzREZDQ0NBNzMzNkU3MjU5NUJERkUwQTYwRUVGQzkiLCJ4NXQiOiJKR2pYdHlZOV9NeW5NMjV5V1Z2ZjRLWU83OGsiLCJ0eXAiOiJhdCtqd3QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j-7CrifUsWJSAj5UI7hKIzf2HzrfSSCQE9QLFfchlwnaVzXTwPIeQjTOrFb9i8gjyItZVW3ux4KnMZqiL5Q9kRD3oHbdc0ErCQ7C-hfP94H-YWvnT83ASLLEwgz5YdM5q-2sTrAvcePaz91V-jJKvwGoR0_RyzfKQg2aHpCtu-_W8-IRVQBOw11HkD3bspjqh1H2m7sCvIhnq1dDIJcczADmxz9POha0vrBZqQZkhK418oEmeMpRG3QLFyVNqbCJ4KPrLWmcmetKxeZHB5iX6ATcs2tm3m-i1QsgQBgb_bbEB1o2wiQ7-SgiObzQ8A9pv8Mjsj5o37IOq8Y5vcjTSg" - null null
2025-07-24 15:41:42.585 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:42.588 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 15:41:42.591 +07:00 [Information] [Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationHandler] [{ Id: 7, Name: "AuthenticationSchemeNotAuthenticatedWithFailure" }] "Identity.Application" was not authenticated. Failure message: "Unprotect ticket failed"
2025-07-24 15:41:42.593 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ValidateHostHeader".
2025-07-24 15:41:42.594 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateValidatedTokens".
2025-07-24 15:41:42.596 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromAuthorizationHeader".
2025-07-24 15:41:42.599 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromBodyForm".
2025-07-24 15:41:42.600 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromQueryString".
2025-07-24 15:41:42.601 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 15:41:42.603 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ResolveServerConfiguration".
2025-07-24 15:41:42.604 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateIntrospectionRequest".
2025-07-24 15:41:42.605 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachNonDefaultIntrospectionEndpointClientAuthenticationMethod".
2025-07-24 15:41:42.606 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ResolveTokenValidationParameters".
2025-07-24 15:41:42.607 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+RemoveDisallowedCharacters".
2025-07-24 15:41:42.608 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateIdentityModelToken".
2025-07-24 15:41:42.609 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+NormalizeScopeClaims".
2025-07-24 15:41:42.610 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+MapInternalClaims".
2025-07-24 15:41:42.612 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidatePrincipal".
2025-07-24 15:41:42.612 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateExpirationDate".
2025-07-24 15:41:42.614 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateAudience".
2025-07-24 15:41:42.616 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateAccessToken".
2025-07-24 15:41:42.617 +07:00 [Debug] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 8, Name: "AuthenticationSchemeAuthenticated" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was successfully authenticated.
2025-07-24 15:41:42.619 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:42.619 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"/dataHub"'
2025-07-24 15:41:42.825 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/api/Employee/Search""" - null null
2025-07-24 15:41:42.828 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:42.829 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/api/Employee/Search""" - 204 null null 4.5033ms
2025-07-24 15:41:42.835 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/api/Employee/Search""" - "application/json" 42
2025-07-24 15:41:42.841 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:42.843 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 15:41:42.847 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:42.850 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)"'
2025-07-24 15:41:42.861 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker] [{ Id: 102, Name: "ControllerActionExecuting" }] Route matched with "{action = \"SearchEmployee\", controller = \"Employee\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] SearchEmployeeAsync(Application.Infrastructure.Models.Request.Category.Employee.SearchEmployeeRequest)" on controller "Application.API.Controllers.EmployeeController" ("Application.API").
2025-07-24 15:41:42.919 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT [o].[Id], [o].[AccountNumber], [o].[Active], [o].[Address], [o].[BankName], [o].[Classify], [o].[ClassifyGroup], [o].[CreatedBy], [o].[CreatedDate], [o].[Description], [o].[Director], [o].[Email], [o].[Fax], [o].[FullOrganizationUnitName], [o].[IPAddress], [o].[IsRoot], [o].[ModifiedBy], [o].[ModifiedDate], [o].[OrganizationUnitName], [o].[OrganizationUnitCode], [o].[ParentCode], [o].[ParentId], [o].[ShortOrganizationUnitName], [o].[SortOrder], [o].[Tel], [o].[TrainingMaterialCode], [o].[Website]
FROM [OrganizationUnit] AS [o]"
2025-07-24 15:41:43.225 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT [e].[Id], [e].[FirstName], [e].[LastName], [e].[Classify], [e].[OrganizationUnitId], NULL AS [OrganizationUnitName], [e].[EmployeeCode], [e].[Fullname], [e].[RankId], [e].[Gender], CASE
    WHEN [e].[Gender] = 1 THEN N'Nam'
    ELSE N'Nữ'
END AS [Gender_Name], [e].[AcademicRankId], [e].[YearOfAcademicRank], [e].[DegreeId], [e].[YearOfDegree], [e].[PositionId], [e].[PositionType], [e].[PartyPositionId], [e].[BirthDay], [e].[Owned], [e].[Active], [e].[ActiveAccount], [e].[IsAdministrator]
FROM [Employee] AS [e]
WHERE [e].[PositionType] IS NOT NULL AND [e].[PositionType] IN (CAST(1 AS smallint), CAST(2 AS smallint))"
2025-07-24 15:41:43.232 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor] [{ Id: 1, Name: "ObjectResultExecuting" }] Executing "OkObjectResult", writing value of type '"Application.Infrastructure.Commons.BaseSearchResponse`1[[Application.Infrastructure.Models.Response.Category.EmployeeResponse, Application.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-24 15:41:43.248 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker] [{ Id: 105, Name: "ActionExecuted" }] Executed action "Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)" in 383.0818ms
2025-07-24 15:41:43.250 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Id: 1, Name: "ExecutedEndpoint" }] Executed endpoint '"Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)"'
2025-07-24 15:41:43.253 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/api/Employee/Search""" - 200 null "application/json; charset=utf-8" 417.6594ms
2025-07-24 15:41:56.831 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - null null
2025-07-24 15:41:56.856 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 24.5069ms
2025-07-24 15:41:56.879 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - null null
2025-07-24 15:41:56.883 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/index.js""" - 200 null "application/javascript;charset=utf-8" 3.6445ms
2025-07-24 15:41:56.964 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - null null
2025-07-24 15:41:57.062 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 98.0253ms
2025-07-24 15:41:58.858 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Id: 1, Name: "ExecutedEndpoint" }] Executed endpoint '"/dataHub"'
2025-07-24 15:41:58.864 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "GET" "http"://"localhost:6583""""/dataHub""?id=iR7v53AGfqD2IJc1o3wLYA&access_token=eyJhbGciOiJSUzI1NiIsImtpZCI6IjI0NjhEN0I3MjYzREZDQ0NBNzMzNkU3MjU5NUJERkUwQTYwRUVGQzkiLCJ4NXQiOiJKR2pYdHlZOV9NeW5NMjV5V1Z2ZjRLWU83OGsiLCJ0eXAiOiJhdCtqd3QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j-7CrifUsWJSAj5UI7hKIzf2HzrfSSCQE9QLFfchlwnaVzXTwPIeQjTOrFb9i8gjyItZVW3ux4KnMZqiL5Q9kRD3oHbdc0ErCQ7C-hfP94H-YWvnT83ASLLEwgz5YdM5q-2sTrAvcePaz91V-jJKvwGoR0_RyzfKQg2aHpCtu-_W8-IRVQBOw11HkD3bspjqh1H2m7sCvIhnq1dDIJcczADmxz9POha0vrBZqQZkhK418oEmeMpRG3QLFyVNqbCJ4KPrLWmcmetKxeZHB5iX6ATcs2tm3m-i1QsgQBgb_bbEB1o2wiQ7-SgiObzQ8A9pv8Mjsj5o37IOq8Y5vcjTSg" - 101 null null 16282.6906ms
2025-07-24 15:41:58.948 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null null
2025-07-24 15:41:58.955 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:58.958 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 204 null null 9.8529ms
2025-07-24 15:41:58.959 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - null 0
2025-07-24 15:41:58.970 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:58.975 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 15:41:58.979 +07:00 [Information] [Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationHandler] [{ Id: 7, Name: "AuthenticationSchemeNotAuthenticatedWithFailure" }] "Identity.Application" was not authenticated. Failure message: "Unprotect ticket failed"
2025-07-24 15:41:58.982 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ValidateHostHeader".
2025-07-24 15:41:58.984 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateValidatedTokens".
2025-07-24 15:41:58.986 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromAuthorizationHeader".
2025-07-24 15:41:58.988 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromBodyForm".
2025-07-24 15:41:58.990 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromQueryString".
2025-07-24 15:41:58.992 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 15:41:58.996 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ResolveServerConfiguration".
2025-07-24 15:41:58.999 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateIntrospectionRequest".
2025-07-24 15:41:59.001 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachNonDefaultIntrospectionEndpointClientAuthenticationMethod".
2025-07-24 15:41:59.005 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ResolveTokenValidationParameters".
2025-07-24 15:41:59.010 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+RemoveDisallowedCharacters".
2025-07-24 15:41:59.013 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateIdentityModelToken".
2025-07-24 15:41:59.016 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+NormalizeScopeClaims".
2025-07-24 15:41:59.020 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+MapInternalClaims".
2025-07-24 15:41:59.022 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidatePrincipal".
2025-07-24 15:41:59.024 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateExpirationDate".
2025-07-24 15:41:59.026 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateAudience".
2025-07-24 15:41:59.028 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateAccessToken".
2025-07-24 15:41:59.030 +07:00 [Debug] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 8, Name: "AuthenticationSchemeAuthenticated" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was successfully authenticated.
2025-07-24 15:41:59.032 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:59.033 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"/dataHub/negotiate"'
2025-07-24 15:41:59.035 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Id: 1, Name: "ExecutedEndpoint" }] Executed endpoint '"/dataHub/negotiate"'
2025-07-24 15:41:59.037 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/dataHub/negotiate""?negotiateVersion=1" - 200 316 "application/json" 77.9719ms
2025-07-24 15:41:59.039 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "GET" "http"://"localhost:6583""""/dataHub""?id=alfMaS4MbwDNFCisJaGXxA&access_token=eyJhbGciOiJSUzI1NiIsImtpZCI6IjI0NjhEN0I3MjYzREZDQ0NBNzMzNkU3MjU5NUJERkUwQTYwRUVGQzkiLCJ4NXQiOiJKR2pYdHlZOV9NeW5NMjV5V1Z2ZjRLWU83OGsiLCJ0eXAiOiJhdCtqd3QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j-7CrifUsWJSAj5UI7hKIzf2HzrfSSCQE9QLFfchlwnaVzXTwPIeQjTOrFb9i8gjyItZVW3ux4KnMZqiL5Q9kRD3oHbdc0ErCQ7C-hfP94H-YWvnT83ASLLEwgz5YdM5q-2sTrAvcePaz91V-jJKvwGoR0_RyzfKQg2aHpCtu-_W8-IRVQBOw11HkD3bspjqh1H2m7sCvIhnq1dDIJcczADmxz9POha0vrBZqQZkhK418oEmeMpRG3QLFyVNqbCJ4KPrLWmcmetKxeZHB5iX6ATcs2tm3m-i1QsgQBgb_bbEB1o2wiQ7-SgiObzQ8A9pv8Mjsj5o37IOq8Y5vcjTSg" - null null
2025-07-24 15:41:59.049 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:59.051 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 15:41:59.055 +07:00 [Information] [Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationHandler] [{ Id: 7, Name: "AuthenticationSchemeNotAuthenticatedWithFailure" }] "Identity.Application" was not authenticated. Failure message: "Unprotect ticket failed"
2025-07-24 15:41:59.058 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ValidateHostHeader".
2025-07-24 15:41:59.061 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateValidatedTokens".
2025-07-24 15:41:59.064 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromAuthorizationHeader".
2025-07-24 15:41:59.065 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromBodyForm".
2025-07-24 15:41:59.067 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ExtractAccessTokenFromQueryString".
2025-07-24 15:41:59.070 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateRequiredTokens".
2025-07-24 15:41:59.072 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ResolveServerConfiguration".
2025-07-24 15:41:59.074 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+EvaluateIntrospectionRequest".
2025-07-24 15:41:59.077 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.SystemNetHttp.OpenIddictValidationSystemNetHttpHandlers+AttachNonDefaultIntrospectionEndpointClientAuthenticationMethod".
2025-07-24 15:41:59.079 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ResolveTokenValidationParameters".
2025-07-24 15:41:59.081 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+RemoveDisallowedCharacters".
2025-07-24 15:41:59.083 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateIdentityModelToken".
2025-07-24 15:41:59.085 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+NormalizeScopeClaims".
2025-07-24 15:41:59.088 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+MapInternalClaims".
2025-07-24 15:41:59.091 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidatePrincipal".
2025-07-24 15:41:59.094 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateExpirationDate".
2025-07-24 15:41:59.097 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ValidateTokenContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+Protection+ValidateAudience".
2025-07-24 15:41:59.101 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessAuthenticationContext" was successfully processed by "OpenIddict.Validation.OpenIddictValidationHandlers+ValidateAccessToken".
2025-07-24 15:41:59.104 +07:00 [Debug] [OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandler] [{ Id: 8, Name: "AuthenticationSchemeAuthenticated" }] AuthenticationScheme: "OpenIddict.Validation.AspNetCore" was successfully authenticated.
2025-07-24 15:41:59.107 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:59.110 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"/dataHub"'
2025-07-24 15:41:59.512 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/api/Employee/Search""" - null null
2025-07-24 15:41:59.517 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:59.519 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "OPTIONS" "http"://"localhost:6583""""/api/Employee/Search""" - 204 null null 6.9089ms
2025-07-24 15:41:59.528 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 1 }] Request starting "HTTP/1.1" "POST" "http"://"localhost:6583""""/api/Employee/Search""" - "application/json" 42
2025-07-24 15:41:59.531 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:59.533 +07:00 [Debug] [OpenIddict.Validation.OpenIddictValidationDispatcher] [] The event "OpenIddict.Validation.OpenIddictValidationEvents+ProcessRequestContext" was successfully processed by "OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreHandlers+ResolveRequestUri".
2025-07-24 15:41:59.537 +07:00 [Information] [Microsoft.AspNetCore.Cors.Infrastructure.CorsService] [{ Id: 4, Name: "PolicySuccess" }] CORS policy execution successful.
2025-07-24 15:41:59.539 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Name: "ExecutingEndpoint" }] Executing endpoint '"Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)"'
2025-07-24 15:41:59.543 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker] [{ Id: 102, Name: "ControllerActionExecuting" }] Route matched with "{action = \"SearchEmployee\", controller = \"Employee\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] SearchEmployeeAsync(Application.Infrastructure.Models.Request.Category.Employee.SearchEmployeeRequest)" on controller "Application.API.Controllers.EmployeeController" ("Application.API").
2025-07-24 15:41:59.591 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT [o].[Id], [o].[AccountNumber], [o].[Active], [o].[Address], [o].[BankName], [o].[Classify], [o].[ClassifyGroup], [o].[CreatedBy], [o].[CreatedDate], [o].[Description], [o].[Director], [o].[Email], [o].[Fax], [o].[FullOrganizationUnitName], [o].[IPAddress], [o].[IsRoot], [o].[ModifiedBy], [o].[ModifiedDate], [o].[OrganizationUnitName], [o].[OrganizationUnitCode], [o].[ParentCode], [o].[ParentId], [o].[ShortOrganizationUnitName], [o].[SortOrder], [o].[Tel], [o].[TrainingMaterialCode], [o].[Website]
FROM [OrganizationUnit] AS [o]"
2025-07-24 15:41:59.614 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT [e].[Id], [e].[FirstName], [e].[LastName], [e].[Classify], [e].[OrganizationUnitId], NULL AS [OrganizationUnitName], [e].[EmployeeCode], [e].[Fullname], [e].[RankId], [e].[Gender], CASE
    WHEN [e].[Gender] = 1 THEN N'Nam'
    ELSE N'Nữ'
END AS [Gender_Name], [e].[AcademicRankId], [e].[YearOfAcademicRank], [e].[DegreeId], [e].[YearOfDegree], [e].[PositionId], [e].[PositionType], [e].[PartyPositionId], [e].[BirthDay], [e].[Owned], [e].[Active], [e].[ActiveAccount], [e].[IsAdministrator]
FROM [Employee] AS [e]
WHERE [e].[PositionType] IS NOT NULL AND [e].[PositionType] IN (CAST(1 AS smallint), CAST(2 AS smallint))"
2025-07-24 15:41:59.630 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor] [{ Id: 1, Name: "ObjectResultExecuting" }] Executing "OkObjectResult", writing value of type '"Application.Infrastructure.Commons.BaseSearchResponse`1[[Application.Infrastructure.Models.Response.Category.EmployeeResponse, Application.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-24 15:41:59.640 +07:00 [Information] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker] [{ Id: 105, Name: "ActionExecuted" }] Executed action "Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)" in 91.9252ms
2025-07-24 15:41:59.647 +07:00 [Information] [Microsoft.AspNetCore.Routing.EndpointMiddleware] [{ Id: 1, Name: "ExecutedEndpoint" }] Executed endpoint '"Application.API.Controllers.EmployeeController.SearchEmployeeAsync (Application.API)"'
2025-07-24 15:41:59.653 +07:00 [Information] [Microsoft.AspNetCore.Hosting.Diagnostics] [{ Id: 2 }] Request finished "HTTP/1.1" "POST" "http"://"localhost:6583""""/api/Employee/Search""" - 200 null "application/json; charset=utf-8" 125.5006ms
2025-07-24 15:42:17.674 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 15:42:18.105 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 15:42:18.159 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:42:18.161 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:42:18.584 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("31"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:42:18.627 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "a4d47b7a-697f-40a5-b1e7-7872dd8c9b57" validation failed: "DuplicateRoleName".
2025-07-24 15:42:18.633 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:42:18.635 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "c0273386-6b9b-4adf-96b7-cb5a6c3e8dd7" validation failed: "DuplicateRoleName".
2025-07-24 15:42:18.655 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__defaultUser_Id_0='a95ffcea-e1fe-413c-8838-5fd5f3b28cb0' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:42:18.665 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("2"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:42:18.689 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__defaultUser_Id_0='eba9617b-6766-4273-8db0-9ee859c657af' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:42:18.692 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:42:18.695 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:42:18.719 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("2"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:42:18.723 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:42:18.726 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:42:18.727 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 15:42:18.728 +07:00 [Information] [app] [] Application Starting
2025-07-24 15:42:18.790 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"https://*************:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 15:42:18.803 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 15:42:18.806 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 15:42:18.807 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Development"
2025-07-24 15:42:18.809 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
2025-07-24 15:43:18.424 +07:00 [Information] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager] [{ Id: 63, Name: "UsingProfileAsKeyRepositoryWithDPAPI" }] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-24 15:43:18.878 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 10400, Name: "Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning" }] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-24 15:43:18.931 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointFrom"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:43:18.933 +07:00 [Warning] [Microsoft.EntityFrameworkCore.Model.Validation] [{ Id: 30000, Name: "Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning" }] No store type was specified for the decimal property '"PointTo"' on entity type '"JournalEntity"'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-24 15:43:19.361 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("41"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:43:19.402 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "ea79031f-f50e-4cc9-92ed-31245e236ca0" validation failed: "DuplicateRoleName".
2025-07-24 15:43:19.407 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedName_0='USER' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:43:19.410 +07:00 [Warning] [Microsoft.AspNetCore.Identity.RoleManager] [{ Name: "RoleValidationFailed" }] Role "bccf9112-fb65-4c04-a592-4344a8df5bfd" validation failed: "DuplicateRoleName".
2025-07-24 15:43:19.429 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("3"ms) [Parameters=["@__defaultUser_Id_0='4e443423-da1b-4fb2-bed3-594d895f151c' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:43:19.439 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("2"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:43:19.464 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__defaultUser_Id_0='90368934-fe95-42b0-9c66-e18fbea8c51e' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT CASE
    WHEN NOT EXISTS (
        SELECT 1
        FROM [AspNetUsers] AS [a]
        WHERE [a].[Id] = @__defaultUser_Id_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END"
2025-07-24 15:43:19.467 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("1"ms) [Parameters=["@__normalizedEmail_0='<EMAIL>' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[FirstName], [a].[LastName], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[OrganizationUnitId], [a].[PasswordHash], [a].[PasswordLastChanged], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0"
2025-07-24 15:43:19.471 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__normalizedName_0='ADMIN' (Size = 256)"], CommandType='Text', CommandTimeout='30']"
""SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0"
2025-07-24 15:43:19.496 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("2"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:43:19.500 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:43:19.502 +07:00 [Information] [Microsoft.EntityFrameworkCore.Database.Command] [{ Id: 20101, Name: "Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted" }] Executed DbCommand ("0"ms) [Parameters=["@__role_Id_0='6b40bffe-31dd-4f69-bfc2-4e754f4a0240' (Size = 450)"], CommandType='Text', CommandTimeout='30']"
""SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0"
2025-07-24 15:43:19.504 +07:00 [Information] [app] [] Finished Seeding Default Data
2025-07-24 15:43:19.505 +07:00 [Information] [app] [] Application Starting
2025-07-24 15:43:19.566 +07:00 [Warning] [Microsoft.AspNetCore.Server.Kestrel] [] Overriding address(es) '"https://*************:5095"'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-24 15:43:19.575 +07:00 [Information] [Microsoft.Hosting.Lifetime] [{ Id: 14, Name: "ListeningOnAddress" }] Now listening on: "http://[::]:6583"
2025-07-24 15:43:19.578 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Application started. Press Ctrl+C to shut down.
2025-07-24 15:43:19.579 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Hosting environment: "Development"
2025-07-24 15:43:19.581 +07:00 [Information] [Microsoft.Hosting.Lifetime] [] Content root path: "D:\CUCKHQS_FULL\cuckhqs\Application.API"
