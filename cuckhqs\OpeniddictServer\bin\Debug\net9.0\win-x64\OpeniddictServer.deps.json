{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0/win-x64", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NET", "NET9_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NET6_0_OR_GREATER", "NET7_0_OR_GREATER", "NET8_0_OR_GREATER", "NET9_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "13.0", "platform": "x64", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v9.0": {"OpeniddictServer/1.0.0": {"dependencies": {"Application.Infrastructure": "1.0.0", "Fido2": "3.0.1", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "9.0.0", "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": "9.0.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.0", "Microsoft.AspNetCore.Identity.UI": "9.0.0", "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": "9.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.0", "Microsoft.EntityFrameworkCore.Tools": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.Design": "9.0.0", "OpenIddict.AspNetCore": "6.1.1", "OpenIddict.EntityFrameworkCore": "6.1.1", "OpenIddict.Quartz": "6.1.1", "Quartz.Extensions.Hosting": "3.13.1", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Extensions.Logging": "9.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "Serilog.Sinks.Seq": "8.0.0", "Swashbuckle.AspNetCore": "7.2.0", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions": "*******", "Microsoft.AspNetCore.Authentication.BearerToken": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core": "*******", "Microsoft.AspNetCore.Authentication": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Endpoints": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.Cryptography.Internal.Reference": "*******", "Microsoft.AspNetCore.Cryptography.KeyDerivation.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions": "*******", "Microsoft.AspNetCore.DataProtection": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions": "*******", "Microsoft.AspNetCore.Hosting": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "*******", "Microsoft.AspNetCore.Html.Abstractions": "*******", "Microsoft.AspNetCore.Http.Abstractions": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http": "*******", "Microsoft.AspNetCore.Http.Extensions": "*******", "Microsoft.AspNetCore.Http.Features": "*******", "Microsoft.AspNetCore.Http.Results": "*******", "Microsoft.AspNetCore.HttpLogging": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.OutputCaching": "*******", "Microsoft.AspNetCore.RateLimiting": "*******", "Microsoft.AspNetCore.Razor": "*******", "Microsoft.AspNetCore.Razor.Runtime": "*******", "Microsoft.AspNetCore.RequestDecompression": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions": "*******", "Microsoft.AspNetCore.Routing": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticAssets": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities": "*******", "Microsoft.CSharp": "*******", "Microsoft.Extensions.Caching.Abstractions.Reference": "*******", "Microsoft.Extensions.Caching.Memory.Reference": "*******", "Microsoft.Extensions.Configuration.Abstractions.Reference": "*******", "Microsoft.Extensions.Configuration.Binder.Reference": "*******", "Microsoft.Extensions.Configuration.CommandLine": "*******", "Microsoft.Extensions.Configuration.Reference": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables": "*******", "Microsoft.Extensions.Configuration.FileExtensions": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "*******", "Microsoft.Extensions.DependencyInjection.Reference": "*******", "Microsoft.Extensions.Diagnostics.Abstractions.Reference": "*******", "Microsoft.Extensions.Diagnostics.Reference": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.Features": "*******", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "*******", "Microsoft.Extensions.FileProviders.Composite": "*******", "Microsoft.Extensions.FileProviders.Embedded.Reference": "*******", "Microsoft.Extensions.FileProviders.Physical.Reference": "*******", "Microsoft.Extensions.FileSystemGlobbing.Reference": "*******", "Microsoft.Extensions.Hosting.Abstractions.Reference": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Http.Reference": "*******", "Microsoft.Extensions.Identity.Core.Reference": "*******", "Microsoft.Extensions.Identity.Stores.Reference": "*******", "Microsoft.Extensions.Localization.Abstractions": "*******", "Microsoft.Extensions.Localization": "*******", "Microsoft.Extensions.Logging.Abstractions.Reference": "*******", "Microsoft.Extensions.Logging.Configuration.Reference": "*******", "Microsoft.Extensions.Logging.Console": "*******", "Microsoft.Extensions.Logging.Debug": "*******", "Microsoft.Extensions.Logging.Reference": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool.Reference": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions.Reference": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.Options.Reference": "*******", "Microsoft.Extensions.Primitives.Reference": "*******", "Microsoft.Extensions.WebEncoders": "*******", "Microsoft.JSInterop": "*******", "Microsoft.Net.Http.Headers.Reference": "*******", "Microsoft.VisualBasic.Core": "1*******", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives": "*******", "Microsoft.Win32.Registry": "*******", "mscorlib": "*******", "netstandard": "*******", "System.AppContext": "*******", "System.Buffers": "*******", "System.Collections.Concurrent": "*******", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "*******", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Annotations": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Configuration": "*******", "System.Console": "*******", "System.Core": "*******", "System.Data.Common": "*******", "System.Data.DataSetExtensions.Reference": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.DiagnosticSource.Reference": "*******", "System.Diagnostics.EventLog.Reference": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Formats.Asn1.Reference": "*******", "System.Formats.Tar": "*******", "System.Globalization.Calendars": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.AccessControl": "*******", "System.IO.FileSystem": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines.Reference": "*******", "System.IO.Pipes.AccessControl": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Memory.Reference": "*******", "System.Net": "*******", "System.Net.Http": "*******", "System.Net.Http.Json": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives": "*******", "System.Net.Quic": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors.Reference": "*******", "System.ObjectModel": "*******", "System.Reflection.DispatchProxy": "*******", "System.Reflection.Reference": "*******", "System.Reflection.Emit": "*******", "System.Reflection.Emit.ILGeneration": "*******", "System.Reflection.Emit.Lightweight": "*******", "System.Reflection.Extensions": "*******", "System.Reflection.Metadata.Reference": "*******", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe.Reference": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles": "*******", "System.Runtime.InteropServices": "*******", "System.Runtime.InteropServices.JavaScript": "*******", "System.Runtime.InteropServices.RuntimeInformation": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader": "*******", "System.Runtime.Numerics": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.AccessControl": "*******", "System.Security.Claims": "*******", "System.Security.Cryptography.Algorithms": "*******", "System.Security.Cryptography.Cng": "*******", "System.Security.Cryptography.Csp": "*******", "System.Security.Cryptography": "*******", "System.Security.Cryptography.Encoding": "*******", "System.Security.Cryptography.OpenSsl": "*******", "System.Security.Cryptography.Primitives": "*******", "System.Security.Cryptography.X509Certificates": "*******", "System.Security.Cryptography.Xml": "*******", "System.Security": "*******", "System.Security.Principal": "*******", "System.Security.Principal.Windows.Reference": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions": "*******", "System.Text.Encodings.Web.Reference": "*******", "System.Text.Json.Reference": "*******", "System.Text.RegularExpressions": "*******", "System.Threading.Channels.Reference": "*******", "System.Threading": "*******", "System.Threading.Overlapped": "*******", "System.Threading.RateLimiting.Reference": "*******", "System.Threading.Tasks.Dataflow.Reference": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "*******", "System.Threading.Timer": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument": "*******", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******"}, "compile": {"OpeniddictServer.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/*******": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"compile": {"Microsoft.AspNetCore.Authentication.BearerToken.dll": {}}}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}}, "Microsoft.AspNetCore.Authentication.Core/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}}, "Microsoft.AspNetCore.Authentication/*******": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}}, "Microsoft.AspNetCore.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}}, "Microsoft.AspNetCore.Components.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}}, "Microsoft.AspNetCore.Components/*******": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"compile": {"Microsoft.AspNetCore.Components.Endpoints.dll": {}}}, "Microsoft.AspNetCore.Components.Forms/*******": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}}, "Microsoft.AspNetCore.Components.Server/*******": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}}, "Microsoft.AspNetCore.Components.Web/*******": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}}, "Microsoft.AspNetCore.CookiePolicy/*******": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}}, "Microsoft.AspNetCore.Cors/*******": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}}, "Microsoft.AspNetCore.Cryptography.Internal.Reference/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.Internal.dll": {}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation.Reference/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}}, "Microsoft.AspNetCore.DataProtection/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Diagnostics/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}}, "Microsoft.AspNetCore/*******": {"compile": {"Microsoft.AspNetCore.dll": {}}}, "Microsoft.AspNetCore.HostFiltering/*******": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}}, "Microsoft.AspNetCore.Hosting.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Hosting/*******": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}}, "Microsoft.AspNetCore.Http.Connections/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}}, "Microsoft.AspNetCore.Http/*******": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}}, "Microsoft.AspNetCore.Http.Extensions/*******": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}}, "Microsoft.AspNetCore.Http.Features/*******": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}}, "Microsoft.AspNetCore.Http.Results/*******": {"compile": {"Microsoft.AspNetCore.Http.Results.dll": {}}}, "Microsoft.AspNetCore.HttpLogging/*******": {"compile": {"Microsoft.AspNetCore.HttpLogging.dll": {}}}, "Microsoft.AspNetCore.HttpOverrides/*******": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}}, "Microsoft.AspNetCore.Identity/*******": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}}, "Microsoft.AspNetCore.Localization/*******": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}}, "Microsoft.AspNetCore.Localization.Routing/*******": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}}, "Microsoft.AspNetCore.Metadata/*******": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}}, "Microsoft.AspNetCore.Mvc.Core/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}}, "Microsoft.AspNetCore.Mvc/*******": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}}, "Microsoft.AspNetCore.OutputCaching/*******": {"compile": {"Microsoft.AspNetCore.OutputCaching.dll": {}}}, "Microsoft.AspNetCore.RateLimiting/*******": {"compile": {"Microsoft.AspNetCore.RateLimiting.dll": {}}}, "Microsoft.AspNetCore.Razor/*******": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}}, "Microsoft.AspNetCore.RequestDecompression/*******": {"compile": {"Microsoft.AspNetCore.RequestDecompression.dll": {}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}}, "Microsoft.AspNetCore.ResponseCaching/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}}, "Microsoft.AspNetCore.ResponseCompression/*******": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}}, "Microsoft.AspNetCore.Rewrite/*******": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Routing/*******": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}}, "Microsoft.AspNetCore.Server.IIS/*******": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}}, "Microsoft.AspNetCore.Session/*******": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}}, "Microsoft.AspNetCore.SignalR.Common/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}}, "Microsoft.AspNetCore.SignalR.Core/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}}, "Microsoft.AspNetCore.SignalR/*******": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}}, "Microsoft.AspNetCore.StaticAssets/*******": {"compile": {"Microsoft.AspNetCore.StaticAssets.dll": {}}}, "Microsoft.AspNetCore.StaticFiles/*******": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}}, "Microsoft.AspNetCore.WebSockets/*******": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/*******": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}}, "Microsoft.CSharp/*******": {"compile": {"Microsoft.CSharp.dll": {}}}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Binder.dll": {}}}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}}, "Microsoft.Extensions.Configuration.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/*******": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}}, "Microsoft.Extensions.Configuration.Ini/*******": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}}, "Microsoft.Extensions.Configuration.Json/*******": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}}, "Microsoft.Extensions.Configuration.Xml/*******": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Diagnostics.Abstractions.dll": {}}}, "Microsoft.Extensions.Diagnostics.Reference/*******": {"compile": {"Microsoft.Extensions.Diagnostics.dll": {}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}}, "Microsoft.Extensions.Features/*******": {"compile": {"Microsoft.Extensions.Features.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}}, "Microsoft.Extensions.FileProviders.Composite/*******": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}}, "Microsoft.Extensions.FileProviders.Embedded.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Embedded.dll": {}}}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}}, "Microsoft.Extensions.Hosting/*******": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}}, "Microsoft.Extensions.Http.Reference/*******": {"compile": {"Microsoft.Extensions.Http.dll": {}}}, "Microsoft.Extensions.Identity.Core.Reference/*******": {"compile": {"Microsoft.Extensions.Identity.Core.dll": {}}}, "Microsoft.Extensions.Identity.Stores.Reference/*******": {"compile": {"Microsoft.Extensions.Identity.Stores.dll": {}}}, "Microsoft.Extensions.Localization.Abstractions/*******": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}}, "Microsoft.Extensions.Localization/*******": {"compile": {"Microsoft.Extensions.Localization.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging.Configuration.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}}, "Microsoft.Extensions.Logging.Console/*******": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}}, "Microsoft.Extensions.Logging.Debug/*******": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}}, "Microsoft.Extensions.Logging.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.EventLog/*******": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}}, "Microsoft.Extensions.Logging.EventSource/*******": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}}, "Microsoft.Extensions.Logging.TraceSource/*******": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}}, "Microsoft.Extensions.Options.Reference/*******": {"compile": {"Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Primitives.Reference/*******": {"compile": {"Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.WebEncoders/*******": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}}, "Microsoft.JSInterop/*******": {"compile": {"Microsoft.JSInterop.dll": {}}}, "Microsoft.Net.Http.Headers.Reference/*******": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}}, "Microsoft.VisualBasic.Core/1*******": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}}, "Microsoft.Win32.Primitives/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}}, "Microsoft.Win32.Registry/*******": {"compile": {"Microsoft.Win32.Registry.dll": {}}}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}}, "netstandard/*******": {"compile": {"netstandard.dll": {}}}, "System.AppContext/*******": {"compile": {"System.AppContext.dll": {}}}, "System.Buffers/*******": {"compile": {"System.Buffers.dll": {}}}, "System.Collections.Concurrent/*******": {"compile": {"System.Collections.Concurrent.dll": {}}}, "System.Collections.Reference/*******": {"compile": {"System.Collections.dll": {}}}, "System.Collections.Immutable.Reference/*******": {"compile": {"System.Collections.Immutable.dll": {}}}, "System.Collections.NonGeneric/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/*******": {"compile": {"System.Collections.Specialized.dll": {}}}, "System.ComponentModel.Annotations/*******": {"compile": {"System.ComponentModel.Annotations.dll": {}}}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}}, "System.ComponentModel/*******": {"compile": {"System.ComponentModel.dll": {}}}, "System.ComponentModel.EventBasedAsync/*******": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}}, "System.ComponentModel.Primitives/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}}, "System.Console/*******": {"compile": {"System.Console.dll": {}}}, "System.Core/*******": {"compile": {"System.Core.dll": {}}}, "System.Data.Common/*******": {"compile": {"System.Data.Common.dll": {}}}, "System.Data.DataSetExtensions.Reference/*******": {"compile": {"System.Data.DataSetExtensions.dll": {}}}, "System.Data/*******": {"compile": {"System.Data.dll": {}}}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}}, "System.Diagnostics.Debug.Reference/*******": {"compile": {"System.Diagnostics.Debug.dll": {}}}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"compile": {"System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Diagnostics.EventLog.Reference/*******": {"compile": {"System.Diagnostics.EventLog.dll": {}}}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}}, "System.Diagnostics.Tools/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}}, "System.Diagnostics.Tracing/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}}, "System/*******": {"compile": {"System.dll": {}}}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}}, "System.Dynamic.Runtime/*******": {"compile": {"System.Dynamic.Runtime.dll": {}}}, "System.Formats.Asn1.Reference/*******": {"compile": {"System.Formats.Asn1.dll": {}}}, "System.Formats.Tar/*******": {"compile": {"System.Formats.Tar.dll": {}}}, "System.Globalization.Calendars/*******": {"compile": {"System.Globalization.Calendars.dll": {}}}, "System.Globalization.Reference/*******": {"compile": {"System.Globalization.dll": {}}}, "System.Globalization.Extensions/*******": {"compile": {"System.Globalization.Extensions.dll": {}}}, "System.IO.Compression.Brotli/*******": {"compile": {"System.IO.Compression.Brotli.dll": {}}}, "System.IO.Compression/*******": {"compile": {"System.IO.Compression.dll": {}}}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}}, "System.IO.Compression.ZipFile/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}}, "System.IO.Reference/*******": {"compile": {"System.IO.dll": {}}}, "System.IO.FileSystem.AccessControl/*******": {"compile": {"System.IO.FileSystem.AccessControl.dll": {}}}, "System.IO.FileSystem/*******": {"compile": {"System.IO.FileSystem.dll": {}}}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}}, "System.IO.FileSystem.Primitives/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}}, "System.IO.Pipelines.Reference/*******": {"compile": {"System.IO.Pipelines.dll": {}}}, "System.IO.Pipes.AccessControl/*******": {"compile": {"System.IO.Pipes.AccessControl.dll": {}}}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}}, "System.Linq.Reference/*******": {"compile": {"System.Linq.dll": {}}}, "System.Linq.Expressions/*******": {"compile": {"System.Linq.Expressions.dll": {}}}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}}, "System.Linq.Queryable/*******": {"compile": {"System.Linq.Queryable.dll": {}}}, "System.Memory.Reference/*******": {"compile": {"System.Memory.dll": {}}}, "System.Net/*******": {"compile": {"System.Net.dll": {}}}, "System.Net.Http/*******": {"compile": {"System.Net.Http.dll": {}}}, "System.Net.Http.Json/*******": {"compile": {"System.Net.Http.Json.dll": {}}}, "System.Net.HttpListener/*******": {"compile": {"System.Net.HttpListener.dll": {}}}, "System.Net.Mail/*******": {"compile": {"System.Net.Mail.dll": {}}}, "System.Net.NameResolution/*******": {"compile": {"System.Net.NameResolution.dll": {}}}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}}, "System.Net.Primitives/*******": {"compile": {"System.Net.Primitives.dll": {}}}, "System.Net.Quic/*******": {"compile": {"System.Net.Quic.dll": {}}}, "System.Net.Requests/*******": {"compile": {"System.Net.Requests.dll": {}}}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}}, "System.Net.ServicePoint/*******": {"compile": {"System.Net.ServicePoint.dll": {}}}, "System.Net.Sockets/*******": {"compile": {"System.Net.Sockets.dll": {}}}, "System.Net.WebClient/*******": {"compile": {"System.Net.WebClient.dll": {}}}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}}, "System.Net.WebProxy/*******": {"compile": {"System.Net.WebProxy.dll": {}}}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}}, "System.Net.WebSockets/*******": {"compile": {"System.Net.WebSockets.dll": {}}}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}}, "System.Numerics.Vectors.Reference/*******": {"compile": {"System.Numerics.Vectors.dll": {}}}, "System.ObjectModel/*******": {"compile": {"System.ObjectModel.dll": {}}}, "System.Reflection.DispatchProxy/*******": {"compile": {"System.Reflection.DispatchProxy.dll": {}}}, "System.Reflection.Reference/*******": {"compile": {"System.Reflection.dll": {}}}, "System.Reflection.Emit/*******": {"compile": {"System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/*******": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/*******": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/*******": {"compile": {"System.Reflection.Extensions.dll": {}}}, "System.Reflection.Metadata.Reference/*******": {"compile": {"System.Reflection.Metadata.dll": {}}}, "System.Reflection.Primitives.Reference/*******": {"compile": {"System.Reflection.Primitives.dll": {}}}, "System.Reflection.TypeExtensions/*******": {"compile": {"System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}}, "System.Resources.ResourceManager.Reference/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}}, "System.Runtime.Reference/*******": {"compile": {"System.Runtime.dll": {}}}, "System.Runtime.Extensions.Reference/*******": {"compile": {"System.Runtime.Extensions.dll": {}}}, "System.Runtime.Handles/*******": {"compile": {"System.Runtime.Handles.dll": {}}}, "System.Runtime.InteropServices/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}}, "System.Runtime.InteropServices.JavaScript/*******": {"compile": {"System.Runtime.InteropServices.JavaScript.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}}, "System.Runtime.Intrinsics/*******": {"compile": {"System.Runtime.Intrinsics.dll": {}}}, "System.Runtime.Loader/*******": {"compile": {"System.Runtime.Loader.dll": {}}}, "System.Runtime.Numerics/*******": {"compile": {"System.Runtime.Numerics.dll": {}}}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}}, "System.Runtime.Serialization.Json/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}}, "System.Runtime.Serialization.Primitives/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}}, "System.Runtime.Serialization.Xml/*******": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}}, "System.Security.AccessControl/*******": {"compile": {"System.Security.AccessControl.dll": {}}}, "System.Security.Claims/*******": {"compile": {"System.Security.Claims.dll": {}}}, "System.Security.Cryptography.Algorithms/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}}, "System.Security.Cryptography.Cng/*******": {"compile": {"System.Security.Cryptography.Cng.dll": {}}}, "System.Security.Cryptography.Csp/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}}, "System.Security.Cryptography/*******": {"compile": {"System.Security.Cryptography.dll": {}}}, "System.Security.Cryptography.Encoding/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}}, "System.Security.Cryptography.OpenSsl/*******": {"compile": {"System.Security.Cryptography.OpenSsl.dll": {}}}, "System.Security.Cryptography.Primitives/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.X509Certificates/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}}, "System.Security.Cryptography.Xml/*******": {"compile": {"System.Security.Cryptography.Xml.dll": {}}}, "System.Security/*******": {"compile": {"System.Security.dll": {}}}, "System.Security.Principal/*******": {"compile": {"System.Security.Principal.dll": {}}}, "System.Security.Principal.Windows.Reference/*******": {"compile": {"System.Security.Principal.Windows.dll": {}}}, "System.Security.SecureString/*******": {"compile": {"System.Security.SecureString.dll": {}}}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}}, "System.Text.Encoding.CodePages/*******": {"compile": {"System.Text.Encoding.CodePages.dll": {}}}, "System.Text.Encoding.Reference/*******": {"compile": {"System.Text.Encoding.dll": {}}}, "System.Text.Encoding.Extensions/*******": {"compile": {"System.Text.Encoding.Extensions.dll": {}}}, "System.Text.Encodings.Web.Reference/*******": {"compile": {"System.Text.Encodings.Web.dll": {}}}, "System.Text.Json.Reference/*******": {"compile": {"System.Text.Json.dll": {}}}, "System.Text.RegularExpressions/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}}, "System.Threading.Channels.Reference/*******": {"compile": {"System.Threading.Channels.dll": {}}}, "System.Threading/*******": {"compile": {"System.Threading.dll": {}}}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}}, "System.Threading.RateLimiting.Reference/*******": {"compile": {"System.Threading.RateLimiting.dll": {}}}, "System.Threading.Tasks.Dataflow.Reference/*******": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}}, "System.Threading.Tasks.Reference/*******": {"compile": {"System.Threading.Tasks.dll": {}}}, "System.Threading.Tasks.Extensions.Reference/*******": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}}, "System.Threading.Thread/*******": {"compile": {"System.Threading.Thread.dll": {}}}, "System.Threading.ThreadPool/*******": {"compile": {"System.Threading.ThreadPool.dll": {}}}, "System.Threading.Timer/*******": {"compile": {"System.Threading.Timer.dll": {}}}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}}, "System.Transactions.Local/*******": {"compile": {"System.Transactions.Local.dll": {}}}, "System.ValueTuple/*******": {"compile": {"System.ValueTuple.dll": {}}}, "System.Web/*******": {"compile": {"System.Web.dll": {}}}, "System.Web.HttpUtility/*******": {"compile": {"System.Web.HttpUtility.dll": {}}}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}}, "System.Xml.ReaderWriter/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}}, "System.Xml.XDocument/*******": {"compile": {"System.Xml.XDocument.dll": {}}}, "System.Xml.XmlDocument/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}}, "System.Xml.XmlSerializer/*******": {"compile": {"System.Xml.XmlSerializer.dll": {}}}, "System.Xml.XPath/*******": {"compile": {"System.Xml.XPath.dll": {}}}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net6.0/Azure.Core.dll": {}}}, "Azure.Identity/1.11.3": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.60.3", "Microsoft.Identity.Client.Extensions.Msal": "4.60.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {}}}, "BCrypt.Net-Next/4.0.3": {"compile": {"lib/net6.0/BCrypt.Net-Next.dll": {}}}, "BouncyCastle.Cryptography/2.5.1": {"compile": {"lib/net6.0/BouncyCastle.Cryptography.dll": {}}}, "DocumentFormat.OpenXml/3.3.0": {"dependencies": {"DocumentFormat.OpenXml.Framework": "3.3.0"}, "compile": {"lib/net8.0/DocumentFormat.OpenXml.dll": {}}}, "DocumentFormat.OpenXml.Framework/3.3.0": {"dependencies": {"System.IO.Packaging": "8.0.1"}, "compile": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {}}}, "ExcelDataReader/3.7.0": {"compile": {"lib/netstandard2.1/ExcelDataReader.dll": {}}}, "ExcelDataReader.DataSet/3.7.0": {"dependencies": {"ExcelDataReader": "3.7.0"}, "compile": {"lib/netstandard2.1/ExcelDataReader.DataSet.dll": {}}}, "Fido2/3.0.1": {"dependencies": {"Fido2.Models": "3.0.1", "Microsoft.Extensions.Http": "9.0.2", "NSec.Cryptography": "22.4.0", "System.Formats.Cbor": "6.0.0", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "compile": {"lib/net6.0/Fido2.dll": {}}}, "Fido2.Models/3.0.1": {"compile": {"lib/net6.0/Fido2.Models.dll": {}}}, "FluentValidation/11.9.2": {"compile": {"lib/net8.0/FluentValidation.dll": {}}}, "FluentValidation.AspNetCore/11.3.0": {"dependencies": {"FluentValidation": "11.9.2", "FluentValidation.DependencyInjectionExtensions": "11.9.2"}, "compile": {"lib/net6.0/FluentValidation.AspNetCore.dll": {}}}, "FluentValidation.DependencyInjectionExtensions/11.9.2": {"dependencies": {"FluentValidation": "11.9.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "compile": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {}}}, "Humanizer/2.14.1": {"dependencies": {"Humanizer.Core.af": "2.14.1", "Humanizer.Core.ar": "2.14.1", "Humanizer.Core.az": "2.14.1", "Humanizer.Core.bg": "2.14.1", "Humanizer.Core.bn-BD": "2.14.1", "Humanizer.Core.cs": "2.14.1", "Humanizer.Core.da": "2.14.1", "Humanizer.Core.de": "2.14.1", "Humanizer.Core.el": "2.14.1", "Humanizer.Core.es": "2.14.1", "Humanizer.Core.fa": "2.14.1", "Humanizer.Core.fi-FI": "2.14.1", "Humanizer.Core.fr": "2.14.1", "Humanizer.Core.fr-BE": "2.14.1", "Humanizer.Core.he": "2.14.1", "Humanizer.Core.hr": "2.14.1", "Humanizer.Core.hu": "2.14.1", "Humanizer.Core.hy": "2.14.1", "Humanizer.Core.id": "2.14.1", "Humanizer.Core.is": "2.14.1", "Humanizer.Core.it": "2.14.1", "Humanizer.Core.ja": "2.14.1", "Humanizer.Core.ko-KR": "2.14.1", "Humanizer.Core.ku": "2.14.1", "Humanizer.Core.lv": "2.14.1", "Humanizer.Core.ms-MY": "2.14.1", "Humanizer.Core.mt": "2.14.1", "Humanizer.Core.nb": "2.14.1", "Humanizer.Core.nb-NO": "2.14.1", "Humanizer.Core.nl": "2.14.1", "Humanizer.Core.pl": "2.14.1", "Humanizer.Core.pt": "2.14.1", "Humanizer.Core.ro": "2.14.1", "Humanizer.Core.ru": "2.14.1", "Humanizer.Core.sk": "2.14.1", "Humanizer.Core.sl": "2.14.1", "Humanizer.Core.sr": "2.14.1", "Humanizer.Core.sr-Latn": "2.14.1", "Humanizer.Core.sv": "2.14.1", "Humanizer.Core.th-TH": "2.14.1", "Humanizer.Core.tr": "2.14.1", "Humanizer.Core.uk": "2.14.1", "Humanizer.Core.uz-Cyrl-UZ": "2.14.1", "Humanizer.Core.uz-Latn-UZ": "2.14.1", "Humanizer.Core.vi": "2.14.1", "Humanizer.Core.zh-CN": "2.14.1", "Humanizer.Core.zh-Hans": "2.14.1", "Humanizer.Core.zh-Hant": "2.14.1"}}, "Humanizer.Core/2.14.1": {"compile": {"lib/net6.0/Humanizer.dll": {}}}, "Humanizer.Core.af/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ar/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.az/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.bg/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.bn-BD/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.cs/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.da/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.de/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.el/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.es/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.fa/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.fi-FI/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.fr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.fr-BE/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.he/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.hr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.hu/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.hy/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.id/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.is/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.it/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ja/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ko-KR/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ku/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.lv/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ms-MY/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.mt/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.nb/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.nb-NO/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.nl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.pl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.pt/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ro/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ru/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.sk/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.sl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.sr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.sr-Latn/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.sv/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.th-TH/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.tr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.uk/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.uz-Cyrl-UZ/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.uz-Latn-UZ/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.vi/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.zh-CN/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.zh-Hans/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.zh-Hant/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "libsodium/********": {}, "log4net/2.0.17": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0"}, "compile": {"lib/netstandard2.0/log4net.dll": {}}}, "MailKit/4.12.1": {"dependencies": {"MimeKit": "4.12.0", "System.Formats.Asn1": "9.0.0"}, "compile": {"lib/net8.0/MailKit.dll": {}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/9.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {}}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.0": {}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.0"}}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore.dll": {}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Microsoft.Extensions.Identity.Stores": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {}}}, "Microsoft.AspNetCore.Identity.UI/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Microsoft.Extensions.Identity.Stores": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Identity.UI.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.CodeAnalysis.Razor": "6.0.24"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.Extensions.DependencyModel": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {}}}, "Microsoft.AspNetCore.Razor.Language/6.0.24": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.Build/17.10.4": {"dependencies": {"Microsoft.Build.Framework": "17.10.4", "Microsoft.NET.StringTools": "17.10.4", "System.Collections.Immutable": "8.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Reflection.Metadata": "8.0.0", "System.Reflection.MetadataLoadContext": "8.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Threading.Tasks.Dataflow": "8.0.0"}, "compile": {"ref/net8.0/Microsoft.Build.dll": {}}}, "Microsoft.Build.Framework/17.10.4": {"compile": {"ref/net8.0/Microsoft.Build.Framework.dll": {}}}, "Microsoft.Build.Locator/1.7.8": {"compile": {"lib/net6.0/Microsoft.Build.Locator.dll": {}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.AnalyzerUtilities/3.3.0": {"compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.AnalyzerUtilities.dll": {}}}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CodeAnalysis.CSharp.Features/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Features.dll": {}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {}}}, "Microsoft.CodeAnalysis.Elfie/1.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0", "System.Data.DataSetExtensions": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Elfie.dll": {}}}, "Microsoft.CodeAnalysis.Features/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.AnalyzerUtilities": "3.3.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Elfie": "1.0.0", "Microsoft.CodeAnalysis.Scripting.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DiaSymReader": "2.0.0", "System.Text.Json": "9.0.0"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.Features.dll": {}}}, "Microsoft.CodeAnalysis.Razor/6.0.24": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0"}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CodeAnalysis.Scripting.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.Scripting.dll": {}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "9.0.2", "System.Threading.Channels": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.10.4", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.0"}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {}}}, "Microsoft.Data.SqlClient/5.2.1": {"dependencies": {"Azure.Identity": "1.11.3", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.60.3", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Runtime.Caching": "8.0.0"}, "compile": {"ref/net8.0/Microsoft.Data.SqlClient.dll": {}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {}, "Microsoft.DiaSymReader/2.0.0": {"compile": {"lib/netstandard2.0/Microsoft.DiaSymReader.dll": {}}}, "Microsoft.DotNet.Scaffolding.Shared/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.DotNet.Scaffolding.Shared.dll": {}}}, "Microsoft.EntityFrameworkCore/9.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.2", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.2": {"compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.2": {}, "Microsoft.EntityFrameworkCore.Design/9.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.10.4", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.Logging": "9.0.2", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.1", "Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "System.Formats.Asn1": "9.0.0", "System.Text.Json": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {}}}, "Microsoft.EntityFrameworkCore.Tools/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.0"}}, "Microsoft.Extensions.AmbientMetadata.Application/9.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Caching.Memory/9.0.2": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Compliance.Abstractions/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Configuration.Binder/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2"}}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.2.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.Diagnostics/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.2"}}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {}, "Microsoft.Extensions.Hosting.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2"}}, "Microsoft.Extensions.Http/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Diagnostics": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.Extensions.Http.Diagnostics/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.2.0", "Microsoft.Extensions.Http": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2", "Microsoft.Extensions.Telemetry": "9.2.0", "System.IO.Pipelines": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {}}}, "Microsoft.Extensions.Http.Polly/9.0.2": {"dependencies": {"Microsoft.Extensions.Http": "9.0.2", "Polly": "7.2.4", "Polly.Extensions.Http": "3.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {}}}, "Microsoft.Extensions.Http.Resilience/9.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.Http.Diagnostics": "9.2.0", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Resilience": "9.2.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {}}}, "Microsoft.Extensions.Identity.Core/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.0", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.Extensions.Identity.Stores/9.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.2", "Microsoft.Extensions.Identity.Core": "9.0.0", "Microsoft.Extensions.Logging": "9.0.2"}}, "Microsoft.Extensions.Logging/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}}, "Microsoft.Extensions.Logging.Configuration/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}}, "Microsoft.Extensions.ObjectPool/9.0.2": {}, "Microsoft.Extensions.Options/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Primitives/9.0.2": {}, "Microsoft.Extensions.Resilience/9.2.0": {"dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.2", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2", "Microsoft.Extensions.Telemetry.Abstractions": "9.2.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {}}}, "Microsoft.Extensions.Telemetry/9.2.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.2.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.2.0", "Microsoft.Extensions.Logging.Configuration": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Telemetry.Abstractions": "9.2.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.2.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.2.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {}}}, "Microsoft.Identity.Client/4.60.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.4.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {}}}, "Microsoft.Identity.Client.Extensions.Msal/4.60.3": {"dependencies": {"Microsoft.Identity.Client": "4.60.3", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {}}}, "Microsoft.IdentityModel.Abstractions/8.4.0": {"compile": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {}}}, "Microsoft.IdentityModel.JsonWebTokens/8.4.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.4.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/8.4.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.4.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/8.4.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.4.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.4.0", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/8.4.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.IdentityModel.Logging": "8.4.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.Net.Http.Headers/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.NET.StringTools/17.10.4": {"compile": {"ref/net8.0/Microsoft.NET.StringTools.dll": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.6.22": {"compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Microsoft.SqlServer.Server/1.0.0": {"compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.Templating": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGenerators.Mvc": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/net8.0/dotnet-aspnet-codegenerator-design.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.Core": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.Utils": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll": {}}}, "MimeKit/4.12.0": {"dependencies": {"BouncyCastle.Cryptography": "2.5.1", "System.Security.Cryptography.Pkcs": "8.0.1"}, "compile": {"lib/net8.0/MimeKit.dll": {}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "compile": {"lib/net6.0/Mono.TextTemplating.dll": {}}}, "Newtonsoft.Json/13.0.3": {"compile": {"lib/net6.0/Newtonsoft.Json.dll": {}}}, "NSec.Cryptography/22.4.0": {"dependencies": {"libsodium": "********"}, "compile": {"lib/net5.0/NSec.Cryptography.dll": {}}}, "NuGet.Common/6.11.0": {"dependencies": {"NuGet.Frameworks": "6.11.0"}, "compile": {"lib/netstandard2.0/NuGet.Common.dll": {}}}, "NuGet.Configuration/6.11.0": {"dependencies": {"NuGet.Common": "6.11.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/netstandard2.0/NuGet.Configuration.dll": {}}}, "NuGet.DependencyResolver.Core/6.11.0": {"dependencies": {"NuGet.Configuration": "6.11.0", "NuGet.LibraryModel": "6.11.0", "NuGet.Protocol": "6.11.0"}, "compile": {"lib/net5.0/NuGet.DependencyResolver.Core.dll": {}}}, "NuGet.Frameworks/6.11.0": {"compile": {"lib/netstandard2.0/NuGet.Frameworks.dll": {}}}, "NuGet.LibraryModel/6.11.0": {"dependencies": {"NuGet.Common": "6.11.0", "NuGet.Versioning": "6.11.0"}, "compile": {"lib/netstandard2.0/NuGet.LibraryModel.dll": {}}}, "NuGet.Packaging/6.11.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "NuGet.Configuration": "6.11.0", "NuGet.Versioning": "6.11.0", "System.Security.Cryptography.Pkcs": "8.0.1"}, "compile": {"lib/net5.0/NuGet.Packaging.dll": {}}}, "NuGet.ProjectModel/6.11.0": {"dependencies": {"NuGet.DependencyResolver.Core": "6.11.0"}, "compile": {"lib/net5.0/NuGet.ProjectModel.dll": {}}}, "NuGet.Protocol/6.11.0": {"dependencies": {"NuGet.Packaging": "6.11.0"}, "compile": {"lib/net5.0/NuGet.Protocol.dll": {}}}, "NuGet.Versioning/6.11.0": {"compile": {"lib/netstandard2.0/NuGet.Versioning.dll": {}}}, "OpenIddict/6.1.1": {"dependencies": {"OpenIddict.Abstractions": "6.1.1", "OpenIddict.Client": "6.1.1", "OpenIddict.Client.SystemIntegration": "6.1.1", "OpenIddict.Client.SystemNetHttp": "6.1.1", "OpenIddict.Client.WebIntegration": "6.1.1", "OpenIddict.Core": "6.1.1", "OpenIddict.Server": "6.1.1", "OpenIddict.Validation": "6.1.1", "OpenIddict.Validation.ServerIntegration": "6.1.1", "OpenIddict.Validation.SystemNetHttp": "6.1.1"}}, "OpenIddict.Abstractions/6.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2", "Microsoft.IdentityModel.Tokens": "8.4.0"}, "compile": {"lib/net9.0/OpenIddict.Abstractions.dll": {}}}, "OpenIddict.AspNetCore/6.1.1": {"dependencies": {"OpenIddict": "6.1.1", "OpenIddict.Client.AspNetCore": "6.1.1", "OpenIddict.Client.DataProtection": "6.1.1", "OpenIddict.Server.AspNetCore": "6.1.1", "OpenIddict.Server.DataProtection": "6.1.1", "OpenIddict.Validation.AspNetCore": "6.1.1", "OpenIddict.Validation.DataProtection": "6.1.1"}}, "OpenIddict.Client/6.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Protocols": "8.4.0", "OpenIddict.Abstractions": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Client.dll": {}}}, "OpenIddict.Client.AspNetCore/6.1.1": {"dependencies": {"OpenIddict.Client": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Client.AspNetCore.dll": {}}}, "OpenIddict.Client.DataProtection/6.1.1": {"dependencies": {"OpenIddict.Client": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Client.DataProtection.dll": {}}}, "OpenIddict.Client.SystemIntegration/6.1.1": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Microsoft.Net.Http.Headers": "9.0.2", "OpenIddict.Client": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Client.SystemIntegration.dll": {}}}, "OpenIddict.Client.SystemNetHttp/6.1.1": {"dependencies": {"Microsoft.Extensions.Http.Polly": "9.0.2", "Microsoft.Extensions.Http.Resilience": "9.2.0", "OpenIddict.Client": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Client.SystemNetHttp.dll": {}}}, "OpenIddict.Client.WebIntegration/6.1.1": {"dependencies": {"OpenIddict.Client": "6.1.1", "OpenIddict.Client.SystemNetHttp": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Client.WebIntegration.dll": {}}}, "OpenIddict.Core/6.1.1": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "OpenIddict.Abstractions": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Core.dll": {}}}, "OpenIddict.EntityFrameworkCore/6.1.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "OpenIddict.Core": "6.1.1", "OpenIddict.EntityFrameworkCore.Models": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.EntityFrameworkCore.dll": {}}}, "OpenIddict.EntityFrameworkCore.Models/6.1.1": {"compile": {"lib/net9.0/OpenIddict.EntityFrameworkCore.Models.dll": {}}}, "OpenIddict.Quartz/6.1.1": {"dependencies": {"OpenIddict.Core": "6.1.1", "Quartz.Extensions.DependencyInjection": "3.13.1"}, "compile": {"lib/net9.0/OpenIddict.Quartz.dll": {}}}, "OpenIddict.Server/6.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "OpenIddict.Abstractions": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Server.dll": {}}}, "OpenIddict.Server.AspNetCore/6.1.1": {"dependencies": {"OpenIddict.Server": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Server.AspNetCore.dll": {}}}, "OpenIddict.Server.DataProtection/6.1.1": {"dependencies": {"OpenIddict.Server": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Server.DataProtection.dll": {}}}, "OpenIddict.Validation/6.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Protocols": "8.4.0", "OpenIddict.Abstractions": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Validation.dll": {}}}, "OpenIddict.Validation.AspNetCore/6.1.1": {"dependencies": {"OpenIddict.Validation": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Validation.AspNetCore.dll": {}}}, "OpenIddict.Validation.DataProtection/6.1.1": {"dependencies": {"OpenIddict.Validation": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Validation.DataProtection.dll": {}}}, "OpenIddict.Validation.ServerIntegration/6.1.1": {"dependencies": {"OpenIddict.Server": "6.1.1", "OpenIddict.Validation": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Validation.ServerIntegration.dll": {}}}, "OpenIddict.Validation.SystemNetHttp/6.1.1": {"dependencies": {"Microsoft.Extensions.Http.Polly": "9.0.2", "Microsoft.Extensions.Http.Resilience": "9.2.0", "OpenIddict.Validation": "6.1.1"}, "compile": {"lib/net9.0/OpenIddict.Validation.SystemNetHttp.dll": {}}}, "Polly/7.2.4": {"compile": {"lib/netstandard2.0/Polly.dll": {}}}, "Polly.Core/8.4.2": {"compile": {"lib/net8.0/Polly.Core.dll": {}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Polly.Core": "8.4.2"}, "compile": {"lib/net8.0/Polly.Extensions.dll": {}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.4"}, "compile": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.4.2", "System.Threading.RateLimiting": "8.0.0"}, "compile": {"lib/net8.0/Polly.RateLimiting.dll": {}}}, "Quartz/3.13.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.2"}, "compile": {"lib/net6.0/Quartz.dll": {}}}, "Quartz.Extensions.DependencyInjection/3.13.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Quartz": "3.13.1"}, "compile": {"lib/net8.0/Quartz.Extensions.DependencyInjection.dll": {}}}, "Quartz.Extensions.Hosting/3.13.1": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Quartz.Extensions.DependencyInjection": "3.13.1"}, "compile": {"lib/net8.0/Quartz.Extensions.Hosting.dll": {}}}, "runtime.any.System.Collections/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "runtime.any.System.Globalization/4.3.0": {}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Resources.ResourceManager/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "runtime.win.System.Diagnostics.Debug/4.3.0": {}, "runtime.win.System.Runtime.Extensions/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "Serilog/4.2.0": {"compile": {"lib/net9.0/Serilog.dll": {}}}, "Serilog.AspNetCore/9.0.0": {"dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "compile": {"lib/net9.0/Serilog.AspNetCore.dll": {}}}, "Serilog.Extensions.Hosting/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}, "compile": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {}}}, "Serilog.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Serilog": "4.2.0"}, "compile": {"lib/net9.0/Serilog.Extensions.Logging.dll": {}}}, "Serilog.Formatting.Compact/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "compile": {"lib/net8.0/Serilog.Formatting.Compact.dll": {}}}, "Serilog.Settings.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.0"}, "compile": {"lib/net9.0/Serilog.Settings.Configuration.dll": {}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Console.dll": {}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Debug.dll": {}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "compile": {"lib/net8.0/Serilog.Sinks.File.dll": {}}}, "Serilog.Sinks.Seq/8.0.0": {"dependencies": {"Serilog": "4.2.0", "Serilog.Sinks.File": "6.0.0"}, "compile": {"lib/net6.0/Serilog.Sinks.Seq.dll": {}}}, "Swashbuckle.AspNetCore/7.2.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "7.2.0", "Swashbuckle.AspNetCore.SwaggerGen": "7.2.0", "Swashbuckle.AspNetCore.SwaggerUI": "7.2.0"}}, "Swashbuckle.AspNetCore.Swagger/7.2.0": {"dependencies": {"Microsoft.OpenApi": "1.6.22"}, "compile": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/7.2.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "7.2.0"}, "compile": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/7.2.0": {"compile": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "9.0.0"}, "compile": {"lib/net6.0/System.ClientModel.dll": {}}}, "System.CodeDom/6.0.0": {"compile": {"lib/net6.0/System.CodeDom.dll": {}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Collections": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"compile": {"lib/net7.0/System.Composition.AttributedModel.dll": {}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "compile": {"lib/net7.0/System.Composition.Convention.dll": {}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "compile": {"lib/net7.0/System.Composition.Hosting.dll": {}}}, "System.Composition.Runtime/7.0.0": {"compile": {"lib/net7.0/System.Composition.Runtime.dll": {}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "compile": {"lib/net7.0/System.Composition.TypedParts.dll": {}}}, "System.Configuration.ConfigurationManager/9.0.0": {"dependencies": {"System.Diagnostics.EventLog": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "compile": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Data.DataSetExtensions/4.5.0": {}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Diagnostics.Debug": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/9.0.0": {}, "System.Formats.Asn1/9.0.0": {}, "System.Formats.Cbor/6.0.0": {"compile": {"lib/net6.0/System.Formats.Cbor.dll": {}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Tokens": "8.4.0"}, "compile": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.IO.Packaging/8.0.1": {"compile": {"lib/net8.0/System.IO.Packaging.dll": {}}}, "System.IO.Pipelines/9.0.2": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/*******": {"compile": {"lib/net9.0/System.Linq.Dynamic.Core.dll": {}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "9.0.0"}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Metadata/8.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0"}}, "System.Reflection.MetadataLoadContext/8.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "compile": {"lib/net8.0/System.Reflection.MetadataLoadContext.dll": {}}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Resources.ResourceManager": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.Caching/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Runtime.Extensions": "4.3.0"}}, "System.Security.Cryptography.Pkcs/8.0.1": {"compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {}}}, "System.Security.Cryptography.ProtectedData/9.0.0": {"compile": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/9.0.0": {}, "System.Threading.Channels/7.0.0": {}, "System.Threading.RateLimiting/8.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks.Dataflow/8.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "Z.EntityFramework.Extensions.EFCore/*********": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "System.Configuration.ConfigurationManager": "9.0.0"}, "compile": {"lib/net8.0/Z.EntityFramework.Extensions.EFCore.dll": {}}}, "Z.EntityFramework.Plus.EFCore/*********": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Z.EntityFramework.Extensions.EFCore": "*********", "Z.Expressions.Eval": "6.2.10"}, "compile": {"lib/net8.0/Z.EntityFramework.Plus.EFCore.dll": {}}}, "Z.Expressions.Eval/6.2.10": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "9.0.2", "System.Configuration.ConfigurationManager": "9.0.0"}, "compile": {"lib/net8.0/Z.Expressions.Eval.dll": {}}}, "Application.Infrastructure/1.0.0": {"dependencies": {"BCrypt.Net-Next": "4.0.3", "DocumentFormat.OpenXml": "3.3.0", "ExcelDataReader": "3.7.0", "ExcelDataReader.DataSet": "3.7.0", "Fido2": "3.0.1", "FluentValidation": "11.9.2", "FluentValidation.AspNetCore": "11.3.0", "FluentValidation.DependencyInjectionExtensions": "11.9.2", "MailKit": "4.12.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.0", "Microsoft.Data.SqlClient": "5.2.1", "Microsoft.EntityFrameworkCore": "9.0.2", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Newtonsoft.Json": "13.0.3", "OpenIddict.EntityFrameworkCore": "6.1.1", "System.Linq": "4.3.0", "System.Linq.Dynamic.Core": "*******", "Z.EntityFramework.Plus.EFCore": "*********", "log4net": "2.0.17"}, "compile": {"Application.Infrastructure.dll": {}}}}, ".NETCoreApp,Version=v9.0/win-x64": {"OpeniddictServer/1.0.0": {"dependencies": {"Application.Infrastructure": "1.0.0", "Fido2": "3.0.1", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "9.0.0", "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": "9.0.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.0", "Microsoft.AspNetCore.Identity.UI": "9.0.0", "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": "9.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.0", "Microsoft.EntityFrameworkCore.Tools": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.Design": "9.0.0", "OpenIddict.AspNetCore": "6.1.1", "OpenIddict.EntityFrameworkCore": "6.1.1", "OpenIddict.Quartz": "6.1.1", "Quartz.Extensions.Hosting": "3.13.1", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Extensions.Logging": "9.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "Serilog.Sinks.Seq": "8.0.0", "Swashbuckle.AspNetCore": "7.2.0", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions": "*******", "Microsoft.AspNetCore.Authentication.BearerToken": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core": "*******", "Microsoft.AspNetCore.Authentication": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Endpoints": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.Cryptography.Internal.Reference": "*******", "Microsoft.AspNetCore.Cryptography.KeyDerivation.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions": "*******", "Microsoft.AspNetCore.DataProtection": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions": "*******", "Microsoft.AspNetCore.Hosting": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "*******", "Microsoft.AspNetCore.Html.Abstractions": "*******", "Microsoft.AspNetCore.Http.Abstractions": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http": "*******", "Microsoft.AspNetCore.Http.Extensions": "*******", "Microsoft.AspNetCore.Http.Features": "*******", "Microsoft.AspNetCore.Http.Results": "*******", "Microsoft.AspNetCore.HttpLogging": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.OutputCaching": "*******", "Microsoft.AspNetCore.RateLimiting": "*******", "Microsoft.AspNetCore.Razor": "*******", "Microsoft.AspNetCore.Razor.Runtime": "*******", "Microsoft.AspNetCore.RequestDecompression": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions": "*******", "Microsoft.AspNetCore.Routing": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticAssets": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities": "*******", "Microsoft.CSharp": "*******", "Microsoft.Extensions.Caching.Abstractions.Reference": "*******", "Microsoft.Extensions.Caching.Memory.Reference": "*******", "Microsoft.Extensions.Configuration.Abstractions.Reference": "*******", "Microsoft.Extensions.Configuration.Binder.Reference": "*******", "Microsoft.Extensions.Configuration.CommandLine": "*******", "Microsoft.Extensions.Configuration.Reference": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables": "*******", "Microsoft.Extensions.Configuration.FileExtensions": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "*******", "Microsoft.Extensions.DependencyInjection.Reference": "*******", "Microsoft.Extensions.Diagnostics.Abstractions.Reference": "*******", "Microsoft.Extensions.Diagnostics.Reference": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.Features": "*******", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "*******", "Microsoft.Extensions.FileProviders.Composite": "*******", "Microsoft.Extensions.FileProviders.Embedded.Reference": "*******", "Microsoft.Extensions.FileProviders.Physical.Reference": "*******", "Microsoft.Extensions.FileSystemGlobbing.Reference": "*******", "Microsoft.Extensions.Hosting.Abstractions.Reference": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Http.Reference": "*******", "Microsoft.Extensions.Identity.Core.Reference": "*******", "Microsoft.Extensions.Identity.Stores.Reference": "*******", "Microsoft.Extensions.Localization.Abstractions": "*******", "Microsoft.Extensions.Localization": "*******", "Microsoft.Extensions.Logging.Abstractions.Reference": "*******", "Microsoft.Extensions.Logging.Configuration.Reference": "*******", "Microsoft.Extensions.Logging.Console": "*******", "Microsoft.Extensions.Logging.Debug": "*******", "Microsoft.Extensions.Logging.Reference": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool.Reference": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions.Reference": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.Options.Reference": "*******", "Microsoft.Extensions.Primitives.Reference": "*******", "Microsoft.Extensions.WebEncoders": "*******", "Microsoft.JSInterop": "*******", "Microsoft.Net.Http.Headers.Reference": "*******", "Microsoft.VisualBasic.Core": "1*******", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives": "*******", "Microsoft.Win32.Registry": "*******", "mscorlib": "*******", "netstandard": "*******", "System.AppContext": "*******", "System.Buffers": "*******", "System.Collections.Concurrent": "*******", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "*******", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Annotations": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Configuration": "*******", "System.Console": "*******", "System.Core": "*******", "System.Data.Common": "*******", "System.Data.DataSetExtensions.Reference": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.DiagnosticSource.Reference": "*******", "System.Diagnostics.EventLog.Reference": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Formats.Asn1.Reference": "*******", "System.Formats.Tar": "*******", "System.Globalization.Calendars": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.AccessControl": "*******", "System.IO.FileSystem": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines.Reference": "*******", "System.IO.Pipes.AccessControl": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Memory.Reference": "*******", "System.Net": "*******", "System.Net.Http": "*******", "System.Net.Http.Json": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives": "*******", "System.Net.Quic": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors.Reference": "*******", "System.ObjectModel": "*******", "System.Reflection.DispatchProxy": "*******", "System.Reflection.Reference": "*******", "System.Reflection.Emit": "*******", "System.Reflection.Emit.ILGeneration": "*******", "System.Reflection.Emit.Lightweight": "*******", "System.Reflection.Extensions": "*******", "System.Reflection.Metadata.Reference": "*******", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe.Reference": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles": "*******", "System.Runtime.InteropServices": "*******", "System.Runtime.InteropServices.JavaScript": "*******", "System.Runtime.InteropServices.RuntimeInformation": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader": "*******", "System.Runtime.Numerics": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.AccessControl": "*******", "System.Security.Claims": "*******", "System.Security.Cryptography.Algorithms": "*******", "System.Security.Cryptography.Cng": "*******", "System.Security.Cryptography.Csp": "*******", "System.Security.Cryptography": "*******", "System.Security.Cryptography.Encoding": "*******", "System.Security.Cryptography.OpenSsl": "*******", "System.Security.Cryptography.Primitives": "*******", "System.Security.Cryptography.X509Certificates": "*******", "System.Security.Cryptography.Xml": "*******", "System.Security": "*******", "System.Security.Principal": "*******", "System.Security.Principal.Windows.Reference": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions": "*******", "System.Text.Encodings.Web.Reference": "*******", "System.Text.Json.Reference": "*******", "System.Text.RegularExpressions": "*******", "System.Threading.Channels.Reference": "*******", "System.Threading": "*******", "System.Threading.Overlapped": "*******", "System.Threading.RateLimiting.Reference": "*******", "System.Threading.Tasks.Dataflow.Reference": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "*******", "System.Threading.Timer": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument": "*******", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "9.0.7", "runtimepack.Microsoft.AspNetCore.App.Runtime.win-x64": "9.0.7"}, "runtime": {"OpeniddictServer.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.7": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "14.0.725.31616"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "9.0.725.31616"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "9.0.725.31616"}, "clretwrc.dll": {"fileVersion": "9.0.725.31616"}, "clrgc.dll": {"fileVersion": "9.0.725.31616"}, "clrgcexp.dll": {"fileVersion": "9.0.725.31616"}, "clrjit.dll": {"fileVersion": "9.0.725.31616"}, "coreclr.dll": {"fileVersion": "9.0.725.31616"}, "createdump.exe": {"fileVersion": "9.0.725.31616"}, "hostfxr.dll": {"fileVersion": "9.0.725.31616"}, "hostpolicy.dll": {"fileVersion": "9.0.725.31616"}, "mscordaccore.dll": {"fileVersion": "9.0.725.31616"}, "mscordaccore_amd64_amd64_9.0.725.31616.dll": {"fileVersion": "9.0.725.31616"}, "mscordbi.dll": {"fileVersion": "9.0.725.31616"}, "mscorrc.dll": {"fileVersion": "9.0.725.31616"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.AspNetCore.App.Runtime.win-x64/9.0.7": {"runtime": {"Microsoft.AspNetCore.Antiforgery.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Authentication.BearerToken.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Authentication.Cookies.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Authentication.OAuth.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Authentication.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Authorization.Policy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Components.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Components.Endpoints.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Components.Server.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.CookiePolicy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Cors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.DataProtection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.HostFiltering.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Html.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Http.Connections.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Http.Results.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.HttpLogging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.HttpOverrides.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.HttpsPolicy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Identity.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Localization.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.Cors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.RazorPages.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.TagHelpers.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.OutputCaching.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Razor.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.RequestDecompression.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.ResponseCaching.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.ResponseCompression.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Rewrite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Server.HttpSys.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Server.IIS.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Server.IISIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Server.Kestrel.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Server.Kestrel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.Session.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.SignalR.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.SignalR.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.StaticAssets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.StaticFiles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Configuration.Ini.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Configuration.KeyPerFile.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Configuration.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Logging.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Options.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Extensions.WebEncoders.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}, "native": {"aspnetcorev2_inprocess.dll": {"fileVersion": "19.0.25168.7"}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.3": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.60.3", "Microsoft.Identity.Client.Extensions.Msal": "4.60.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.3.0", "fileVersion": "1.1100.324.25704"}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "BouncyCastle.Cryptography/2.5.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.5.1.28965"}}}, "DocumentFormat.OpenXml/3.3.0": {"dependencies": {"DocumentFormat.OpenXml.Framework": "3.3.0"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "DocumentFormat.OpenXml.Framework/3.3.0": {"dependencies": {"System.IO.Packaging": "8.0.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "ExcelDataReader/3.7.0": {"runtime": {"lib/netstandard2.1/ExcelDataReader.dll": {"assemblyVersion": "3.7.0.0", "fileVersion": "3.7.0.0"}}}, "ExcelDataReader.DataSet/3.7.0": {"dependencies": {"ExcelDataReader": "3.7.0"}, "runtime": {"lib/netstandard2.1/ExcelDataReader.DataSet.dll": {"assemblyVersion": "3.7.0.0", "fileVersion": "3.7.0.0"}}}, "Fido2/3.0.1": {"dependencies": {"Fido2.Models": "3.0.1", "Microsoft.Extensions.Http": "9.0.2", "NSec.Cryptography": "22.4.0", "System.Formats.Cbor": "6.0.0", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net6.0/Fido2.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "Fido2.Models/3.0.1": {"runtime": {"lib/net6.0/Fido2.Models.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "FluentValidation/11.9.2": {"runtime": {"lib/net8.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "11.9.2.0"}}}, "FluentValidation.AspNetCore/11.3.0": {"dependencies": {"FluentValidation": "11.9.2", "FluentValidation.DependencyInjectionExtensions": "11.9.2"}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"assemblyVersion": "1*******", "fileVersion": "11.3.0.0"}}}, "FluentValidation.DependencyInjectionExtensions/11.9.2": {"dependencies": {"FluentValidation": "11.9.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "1*******", "fileVersion": "11.9.2.0"}}}, "Humanizer/2.14.1": {"dependencies": {"Humanizer.Core.af": "2.14.1", "Humanizer.Core.ar": "2.14.1", "Humanizer.Core.az": "2.14.1", "Humanizer.Core.bg": "2.14.1", "Humanizer.Core.bn-BD": "2.14.1", "Humanizer.Core.cs": "2.14.1", "Humanizer.Core.da": "2.14.1", "Humanizer.Core.de": "2.14.1", "Humanizer.Core.el": "2.14.1", "Humanizer.Core.es": "2.14.1", "Humanizer.Core.fa": "2.14.1", "Humanizer.Core.fi-FI": "2.14.1", "Humanizer.Core.fr": "2.14.1", "Humanizer.Core.fr-BE": "2.14.1", "Humanizer.Core.he": "2.14.1", "Humanizer.Core.hr": "2.14.1", "Humanizer.Core.hu": "2.14.1", "Humanizer.Core.hy": "2.14.1", "Humanizer.Core.id": "2.14.1", "Humanizer.Core.is": "2.14.1", "Humanizer.Core.it": "2.14.1", "Humanizer.Core.ja": "2.14.1", "Humanizer.Core.ko-KR": "2.14.1", "Humanizer.Core.ku": "2.14.1", "Humanizer.Core.lv": "2.14.1", "Humanizer.Core.ms-MY": "2.14.1", "Humanizer.Core.mt": "2.14.1", "Humanizer.Core.nb": "2.14.1", "Humanizer.Core.nb-NO": "2.14.1", "Humanizer.Core.nl": "2.14.1", "Humanizer.Core.pl": "2.14.1", "Humanizer.Core.pt": "2.14.1", "Humanizer.Core.ro": "2.14.1", "Humanizer.Core.ru": "2.14.1", "Humanizer.Core.sk": "2.14.1", "Humanizer.Core.sl": "2.14.1", "Humanizer.Core.sr": "2.14.1", "Humanizer.Core.sr-Latn": "2.14.1", "Humanizer.Core.sv": "2.14.1", "Humanizer.Core.th-TH": "2.14.1", "Humanizer.Core.tr": "2.14.1", "Humanizer.Core.uk": "2.14.1", "Humanizer.Core.uz-Cyrl-UZ": "2.14.1", "Humanizer.Core.uz-Latn-UZ": "2.14.1", "Humanizer.Core.vi": "2.14.1", "Humanizer.Core.zh-CN": "2.14.1", "Humanizer.Core.zh-Hans": "2.14.1", "Humanizer.Core.zh-Hant": "2.14.1"}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Humanizer.Core.af/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ar/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.az/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.bg/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.bn-BD/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.cs/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.da/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.de/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.el/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.es/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.fa/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.fi-FI/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.fr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/fr/Humanizer.resources.dll": {"locale": "fr"}}}, "Humanizer.Core.fr-BE/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.he/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.hr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.hu/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.hy/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.id/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.is/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.it/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ja/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ko-KR/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ku/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.lv/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ms-MY/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.mt/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.nb/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.nb-NO/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.nl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.pl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.pt/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ro/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.ru/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.sk/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.sl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.sr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.sr-Latn/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.sv/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.th-TH/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.tr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.uk/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.uz-Cyrl-UZ/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.uz-Latn-UZ/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.vi/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.zh-CN/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.zh-Hans/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "Humanizer.Core.zh-Hant/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}}, "libsodium/********": {"native": {"runtimes/win-x64/native/libsodium.dll": {"fileVersion": "1.0.18.0"}}}, "log4net/2.0.17": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"lib/netstandard2.0/log4net.dll": {"assemblyVersion": "2.0.17.0", "fileVersion": "2.0.17.0"}}}, "MailKit/4.12.1": {"dependencies": {"MimeKit": "4.12.0", "System.Formats.Asn1": "9.0.0"}, "runtime": {"lib/net8.0/MailKit.dll": {"assemblyVersion": "4.12.0.0", "fileVersion": "4.12.1.0"}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/9.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.0": {}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.0"}}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Microsoft.Extensions.Identity.Stores": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Identity.UI/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Microsoft.Extensions.Identity.Stores": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Identity.UI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.CodeAnalysis.Razor": "6.0.24"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.Extensions.DependencyModel": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Razor.Language/6.0.24": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "********", "fileVersion": "6.0.2423.51812"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Build/17.10.4": {"dependencies": {"Microsoft.Build.Framework": "17.10.4", "Microsoft.NET.StringTools": "17.10.4", "System.Collections.Immutable": "8.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Reflection.Metadata": "8.0.0", "System.Reflection.MetadataLoadContext": "8.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Threading.Tasks.Dataflow": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Build.dll": {"assemblyVersion": "********", "fileVersion": "17.10.4.21802"}}}, "Microsoft.Build.Framework/17.10.4": {"runtime": {"lib/net8.0/Microsoft.Build.Framework.dll": {"assemblyVersion": "********", "fileVersion": "17.10.4.21802"}}}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.8.28074"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.AnalyzerUtilities/3.3.0": {"runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.AnalyzerUtilities.dll": {"assemblyVersion": "3.3.2.30504", "fileVersion": "3.3.2.30504"}}}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}}}, "Microsoft.CodeAnalysis.CSharp.Features/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "fr"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}}}, "Microsoft.CodeAnalysis.Elfie/1.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0", "System.Data.DataSetExtensions": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Elfie.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Microsoft.CodeAnalysis.Features/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.AnalyzerUtilities": "3.3.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Elfie": "1.0.0", "Microsoft.CodeAnalysis.Scripting.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DiaSymReader": "2.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Features.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/fr/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "fr"}}}, "Microsoft.CodeAnalysis.Razor/6.0.24": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "********", "fileVersion": "6.0.2423.51812"}}}, "Microsoft.CodeAnalysis.Scripting.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Scripting.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/fr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "fr"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "9.0.2", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.10.4", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}}}, "Microsoft.Data.SqlClient/5.2.1": {"dependencies": {"Azure.Identity": "1.11.3", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.60.3", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Runtime.Caching": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.21.24152.3"}}, "resources": {"lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"native": {"runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"fileVersion": "*******"}}}, "Microsoft.DiaSymReader/2.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.DiaSymReader.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.23.22804"}}}, "Microsoft.DotNet.Scaffolding.Shared/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.DotNet.Scaffolding.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.EntityFrameworkCore/9.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.2", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6701"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.2": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6701"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.2": {}, "Microsoft.EntityFrameworkCore.Design/9.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.10.4", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.Logging": "9.0.2", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6701"}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.1", "Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "System.Formats.Asn1": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}}, "Microsoft.EntityFrameworkCore.Tools/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.0"}}, "Microsoft.Extensions.AmbientMetadata.Application/9.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Caching.Memory/9.0.2": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Compliance.Abstractions/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.Configuration/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Configuration.Binder/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2"}}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.2.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Diagnostics/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.2"}}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {}, "Microsoft.Extensions.Hosting.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2"}}, "Microsoft.Extensions.Http/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Diagnostics": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.Extensions.Http.Diagnostics/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.2.0", "Microsoft.Extensions.Http": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2", "Microsoft.Extensions.Telemetry": "9.2.0", "System.IO.Pipelines": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.Http.Polly/9.0.2": {"dependencies": {"Microsoft.Extensions.Http": "9.0.2", "Polly": "7.2.4", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}}, "Microsoft.Extensions.Http.Resilience/9.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.Http.Diagnostics": "9.2.0", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Resilience": "9.2.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.Identity.Core/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.0", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.Extensions.Identity.Stores/9.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.2", "Microsoft.Extensions.Identity.Core": "9.0.0", "Microsoft.Extensions.Logging": "9.0.2"}}, "Microsoft.Extensions.Logging/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}}, "Microsoft.Extensions.Logging.Configuration/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2"}}, "Microsoft.Extensions.ObjectPool/9.0.2": {}, "Microsoft.Extensions.Options/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.Extensions.Primitives/9.0.2": {}, "Microsoft.Extensions.Resilience/9.2.0": {"dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.2", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.2", "Microsoft.Extensions.Telemetry.Abstractions": "9.2.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.Telemetry/9.2.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.2.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.2.0", "Microsoft.Extensions.Logging.Configuration": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Telemetry.Abstractions": "9.2.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.2.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.2.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}}, "Microsoft.Identity.Client/4.60.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.4.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.60.3.0", "fileVersion": "4.60.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.60.3": {"dependencies": {"Microsoft.Identity.Client": "4.60.3", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.60.3.0", "fileVersion": "4.60.3.0"}}}, "Microsoft.IdentityModel.Abstractions/8.4.0": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.4.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.4.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}}, "Microsoft.IdentityModel.Logging/8.4.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.4.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}}, "Microsoft.IdentityModel.Protocols/8.4.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.4.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.4.0", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.4.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.IdentityModel.Logging": "8.4.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}}, "Microsoft.Net.Http.Headers/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}}, "Microsoft.NET.StringTools/17.10.4": {"runtime": {"lib/net8.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "*******", "fileVersion": "17.10.4.21802"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.6.22": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.22.0", "fileVersion": "1.6.22.0"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.VisualStudio.Web.CodeGeneration/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.Templating": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGenerators.Mvc": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net8.0/dotnet-aspnet-codegenerator-design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.Core": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.Utils": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "MimeKit/4.12.0": {"dependencies": {"BouncyCastle.Cryptography": "2.5.1", "System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/MimeKit.dll": {"assemblyVersion": "4.12.0.0", "fileVersion": "4.12.0.0"}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.0.1"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "NSec.Cryptography/22.4.0": {"dependencies": {"libsodium": "********"}, "runtime": {"lib/net5.0/NSec.Cryptography.dll": {"assemblyVersion": "22.4.0.0", "fileVersion": "22.4.0.0"}}}, "NuGet.Common/6.11.0": {"dependencies": {"NuGet.Frameworks": "6.11.0"}, "runtime": {"lib/netstandard2.0/NuGet.Common.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.Configuration/6.11.0": {"dependencies": {"NuGet.Common": "6.11.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/netstandard2.0/NuGet.Configuration.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.DependencyResolver.Core/6.11.0": {"dependencies": {"NuGet.Configuration": "6.11.0", "NuGet.LibraryModel": "6.11.0", "NuGet.Protocol": "6.11.0"}, "runtime": {"lib/net5.0/NuGet.DependencyResolver.Core.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.Frameworks/6.11.0": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.LibraryModel/6.11.0": {"dependencies": {"NuGet.Common": "6.11.0", "NuGet.Versioning": "6.11.0"}, "runtime": {"lib/netstandard2.0/NuGet.LibraryModel.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.Packaging/6.11.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "NuGet.Configuration": "6.11.0", "NuGet.Versioning": "6.11.0", "System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net5.0/NuGet.Packaging.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.ProjectModel/6.11.0": {"dependencies": {"NuGet.DependencyResolver.Core": "6.11.0"}, "runtime": {"lib/net5.0/NuGet.ProjectModel.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.Protocol/6.11.0": {"dependencies": {"NuGet.Packaging": "6.11.0"}, "runtime": {"lib/net5.0/NuGet.Protocol.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.Versioning/6.11.0": {"runtime": {"lib/netstandard2.0/NuGet.Versioning.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "OpenIddict/6.1.1": {"dependencies": {"OpenIddict.Abstractions": "6.1.1", "OpenIddict.Client": "6.1.1", "OpenIddict.Client.SystemIntegration": "6.1.1", "OpenIddict.Client.SystemNetHttp": "6.1.1", "OpenIddict.Client.WebIntegration": "6.1.1", "OpenIddict.Core": "6.1.1", "OpenIddict.Server": "6.1.1", "OpenIddict.Validation": "6.1.1", "OpenIddict.Validation.ServerIntegration": "6.1.1", "OpenIddict.Validation.SystemNetHttp": "6.1.1"}}, "OpenIddict.Abstractions/6.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2", "Microsoft.IdentityModel.Tokens": "8.4.0"}, "runtime": {"lib/net9.0/OpenIddict.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.AspNetCore/6.1.1": {"dependencies": {"OpenIddict": "6.1.1", "OpenIddict.Client.AspNetCore": "6.1.1", "OpenIddict.Client.DataProtection": "6.1.1", "OpenIddict.Server.AspNetCore": "6.1.1", "OpenIddict.Server.DataProtection": "6.1.1", "OpenIddict.Validation.AspNetCore": "6.1.1", "OpenIddict.Validation.DataProtection": "6.1.1"}}, "OpenIddict.Client/6.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Protocols": "8.4.0", "OpenIddict.Abstractions": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Client.AspNetCore/6.1.1": {"dependencies": {"OpenIddict.Client": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Client.DataProtection/6.1.1": {"dependencies": {"OpenIddict.Client": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Client.SystemIntegration/6.1.1": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Microsoft.Net.Http.Headers": "9.0.2", "OpenIddict.Client": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.SystemIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Client.SystemNetHttp/6.1.1": {"dependencies": {"Microsoft.Extensions.Http.Polly": "9.0.2", "Microsoft.Extensions.Http.Resilience": "9.2.0", "OpenIddict.Client": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.SystemNetHttp.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Client.WebIntegration/6.1.1": {"dependencies": {"OpenIddict.Client": "6.1.1", "OpenIddict.Client.SystemNetHttp": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.WebIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Core/6.1.1": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "OpenIddict.Abstractions": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.EntityFrameworkCore/6.1.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "OpenIddict.Core": "6.1.1", "OpenIddict.EntityFrameworkCore.Models": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.EntityFrameworkCore.Models/6.1.1": {"runtime": {"lib/net9.0/OpenIddict.EntityFrameworkCore.Models.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Quartz/6.1.1": {"dependencies": {"OpenIddict.Core": "6.1.1", "Quartz.Extensions.DependencyInjection": "3.13.1"}, "runtime": {"lib/net9.0/OpenIddict.Quartz.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Server/6.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "OpenIddict.Abstractions": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Server.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Server.AspNetCore/6.1.1": {"dependencies": {"OpenIddict.Server": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Server.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Server.DataProtection/6.1.1": {"dependencies": {"OpenIddict.Server": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Server.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Validation/6.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Protocols": "8.4.0", "OpenIddict.Abstractions": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Validation.AspNetCore/6.1.1": {"dependencies": {"OpenIddict.Validation": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Validation.DataProtection/6.1.1": {"dependencies": {"OpenIddict.Validation": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Validation.ServerIntegration/6.1.1": {"dependencies": {"OpenIddict.Server": "6.1.1", "OpenIddict.Validation": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.ServerIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "OpenIddict.Validation.SystemNetHttp/6.1.1": {"dependencies": {"Microsoft.Extensions.Http.Polly": "9.0.2", "Microsoft.Extensions.Http.Resilience": "9.2.0", "OpenIddict.Validation": "6.1.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.SystemNetHttp.dll": {"assemblyVersion": "*******", "fileVersion": "6.100.125.12678"}}}, "Polly/7.2.4": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.4.982"}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.4"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.4.2", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Quartz/3.13.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.2"}, "runtime": {"lib/net6.0/Quartz.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Quartz.Extensions.DependencyInjection/3.13.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Quartz": "3.13.1"}, "runtime": {"lib/net8.0/Quartz.Extensions.DependencyInjection.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Quartz.Extensions.Hosting/3.13.1": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Quartz.Extensions.DependencyInjection": "3.13.1"}, "runtime": {"lib/net8.0/Quartz.Extensions.Hosting.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "runtime.any.System.Collections/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "runtime.any.System.Globalization/4.3.0": {}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Resources.ResourceManager/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "runtime.win.System.Diagnostics.Debug/4.3.0": {}, "runtime.win.System.Runtime.Extensions/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "Serilog/4.2.0": {"runtime": {"lib/net9.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/9.0.0": {"dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net9.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Hosting.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.2", "Serilog": "4.2.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.2", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.0"}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Seq/8.0.0": {"dependencies": {"Serilog": "4.2.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.Seq.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/7.2.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "7.2.0", "Swashbuckle.AspNetCore.SwaggerGen": "7.2.0", "Swashbuckle.AspNetCore.SwaggerUI": "7.2.0"}}, "Swashbuckle.AspNetCore.Swagger/7.2.0": {"dependencies": {"Microsoft.OpenApi": "1.6.22"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.0.956"}}}, "Swashbuckle.AspNetCore.SwaggerGen/7.2.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "7.2.0"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.0.956"}}}, "Swashbuckle.AspNetCore.SwaggerUI/7.2.0": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.0.956"}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Collections": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Configuration.ConfigurationManager/9.0.0": {"dependencies": {"System.Diagnostics.EventLog": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Data.DataSetExtensions/4.5.0": {}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Diagnostics.Debug": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/9.0.0": {}, "System.Formats.Asn1/9.0.0": {}, "System.Formats.Cbor/6.0.0": {"runtime": {"lib/net6.0/System.Formats.Cbor.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Tokens": "8.4.0"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.IO.Packaging/8.0.1": {"runtime": {"lib/net8.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.IO.Pipelines/9.0.2": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/*******": {"runtime": {"lib/net9.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "9.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Metadata/8.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0"}}, "System.Reflection.MetadataLoadContext/8.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net8.0/System.Reflection.MetadataLoadContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Resources.ResourceManager": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.Caching/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Runtime.Extensions": "4.3.0"}}, "System.Security.Cryptography.Pkcs/8.0.1": {}, "System.Security.Cryptography.ProtectedData/9.0.0": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/9.0.0": {}, "System.Threading.Channels/7.0.0": {}, "System.Threading.RateLimiting/8.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks.Dataflow/8.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "Z.EntityFramework.Extensions.EFCore/*********": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"lib/net8.0/Z.EntityFramework.Extensions.EFCore.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Z.EntityFramework.Plus.EFCore/*********": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.2", "Z.EntityFramework.Extensions.EFCore": "*********", "Z.Expressions.Eval": "6.2.10"}, "runtime": {"lib/net8.0/Z.EntityFramework.Plus.EFCore.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Z.Expressions.Eval/6.2.10": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "9.0.2", "System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"lib/net8.0/Z.Expressions.Eval.dll": {"assemblyVersion": "6.2.10.0", "fileVersion": "6.2.10.0"}}}, "Application.Infrastructure/1.0.0": {"dependencies": {"BCrypt.Net-Next": "4.0.3", "DocumentFormat.OpenXml": "3.3.0", "ExcelDataReader": "3.7.0", "ExcelDataReader.DataSet": "3.7.0", "Fido2": "3.0.1", "FluentValidation": "11.9.2", "FluentValidation.AspNetCore": "11.3.0", "FluentValidation.DependencyInjectionExtensions": "11.9.2", "MailKit": "4.12.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.0", "Microsoft.Data.SqlClient": "5.2.1", "Microsoft.EntityFrameworkCore": "9.0.2", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Newtonsoft.Json": "13.0.3", "OpenIddict.EntityFrameworkCore": "6.1.1", "System.Linq": "4.3.0", "System.Linq.Dynamic.Core": "*******", "Z.EntityFramework.Plus.EFCore": "*********", "log4net": "2.0.17"}, "runtime": {"Application.Infrastructure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"OpeniddictServer/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.7": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.AspNetCore.App.Runtime.win-x64/9.0.7": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.3": {"type": "package", "serviceable": true, "sha512": "sha512-4EsGMAr+oog5UqHs46qwA7S/lJiwpXjPBY3t9tQBmJ8nsgmT/LLnrc32eiTlfOdfKxUz4fxBD2YjSnVZacu97w==", "path": "azure.identity/1.11.3", "hashPath": "azure.identity.1.11.3.nupkg.sha512"}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "BouncyCastle.Cryptography/2.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-zy8TMeTP+1FH2NrLaNZtdRbBdq7u5MI+NFZQOBSM69u5RFkciinwzV2eveY6Kjf5MzgsYvvl6kTStsj3JrXqkg==", "path": "bouncycastle.cryptography/2.5.1", "hashPath": "bouncycastle.cryptography.2.5.1.nupkg.sha512"}, "DocumentFormat.OpenXml/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JogRPJNiE6kKvbuCqVRX691pPWeGMqdQgjrUwRYkdpfkMmtElfqAgcRR73geYj7OtBeEpstldZXXzJw27LUI9w==", "path": "documentformat.openxml/3.3.0", "hashPath": "documentformat.openxml.3.3.0.nupkg.sha512"}, "DocumentFormat.OpenXml.Framework/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-R5CLzEoeyr7XDB7g3NTxRobcU19agaxVAhGZm+fZUShJGiU4bw8oUgnA2BNFepigJckfFMayOBMAbV3kDXNInA==", "path": "documentformat.openxml.framework/3.3.0", "hashPath": "documentformat.openxml.framework.3.3.0.nupkg.sha512"}, "ExcelDataReader/3.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AMv3oDETRHSRyXC17rBtKH45qIfFyo433LMeaMB3u4RNr/c9Luuc0Z+JMP6+3Cx9n4wXqFqcrEIVxrf/GgYnZg==", "path": "exceldatareader/3.7.0", "hashPath": "exceldatareader.3.7.0.nupkg.sha512"}, "ExcelDataReader.DataSet/3.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-zA2/CVzbMspkNg0qf0/Zp+eU6VxYP5PtiJSErLDP46d/Y7F6of/NCcSGeXjs97KDq7UiEf6XJe+89s/92n2GYg==", "path": "exceldatareader.dataset/3.7.0", "hashPath": "exceldatareader.dataset.3.7.0.nupkg.sha512"}, "Fido2/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-S0Bz1vfcKlO4Jase3AWp5XnQ746psf4oGx5kL+D2A10j1SsjoAOAIIpanSwfi0cEepDHgk1bClcOKY5TjOzGdA==", "path": "fido2/3.0.1", "hashPath": "fido2.3.0.1.nupkg.sha512"}, "Fido2.Models/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-mgjcuGETuYSCUEaZG+jQeeuuEMkDLc4GDJHBvKDdOz6oSOWp5adPdWP4btZx7Pi+9fu4szN3JIjJmby67MaILw==", "path": "fido2.models/3.0.1", "hashPath": "fido2.models.3.0.1.nupkg.sha512"}, "FluentValidation/11.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-XeHp9LvFvu1fsQ/NvDCymV02GOCB1nz7ZUhfpI3uMhCcHTkV1K5bMkv+Nc/kuNYyAsX5+5bcmUanIEMd5QN+Eg==", "path": "fluentvalidation/11.9.2", "hashPath": "fluentvalidation.11.9.2.nupkg.sha512"}, "FluentValidation.AspNetCore/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtFVgKnDFySyBlPS8bZbTKEEwJZnn11rXXJ2SQnjDhZ56rQqybBg9Joq4crRLz3y0QR8WoOq4iE4piV81w/Djg==", "path": "fluentvalidation.aspnetcore/11.3.0", "hashPath": "fluentvalidation.aspnetcore.11.3.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-PMAdnR1fX1c8DyUvu5YvRa0V0JHES8vzXnVX0OSS3z9W/SkuHqcGrtiSOdW1QehG3vZhXsKhqf3wgDzW/OYahA==", "path": "fluentvalidation.dependencyinjectionextensions/11.9.2", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.9.2.nupkg.sha512"}, "Humanizer/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-/FUTD3cEceAAmJSCPN9+J+VhGwmL/C12jvwlyM1DFXShEMsBzvLzLqSrJ2rb+k/W2znKw7JyflZgZpyE+tI7lA==", "path": "humanizer/2.14.1", "hashPath": "humanizer.2.14.1.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Humanizer.Core.af/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-BoQHyu5le+xxKOw+/AUM7CLXneM/Bh3++0qh1u0+D95n6f9eGt9kNc8LcAHLIOwId7Sd5hiAaaav0Nimj3peNw==", "path": "humanizer.core.af/2.14.1", "hashPath": "humanizer.core.af.2.14.1.nupkg.sha512"}, "Humanizer.Core.ar/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-3d1V10LDtmqg5bZjWkA/EkmGFeSfNBcyCH+TiHcHP+HGQQmRq3eBaLcLnOJbVQVn3Z6Ak8GOte4RX4kVCxQlFA==", "path": "humanizer.core.ar/2.14.1", "hashPath": "humanizer.core.ar.2.14.1.nupkg.sha512"}, "Humanizer.Core.az/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-8Z/tp9PdHr/K2Stve2Qs/7uqWPWLUK9D8sOZDNzyv42e20bSoJkHFn7SFoxhmaoVLJwku2jp6P7HuwrfkrP18Q==", "path": "humanizer.core.az/2.14.1", "hashPath": "humanizer.core.az.2.14.1.nupkg.sha512"}, "Humanizer.Core.bg/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-S+hIEHicrOcbV2TBtyoPp1AVIGsBzlarOGThhQYCnP6QzEYo/5imtok6LMmhZeTnBFoKhM8yJqRfvJ5yqVQKSQ==", "path": "humanizer.core.bg/2.14.1", "hashPath": "humanizer.core.bg.2.14.1.nupkg.sha512"}, "Humanizer.Core.bn-BD/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-U3bfj90tnUDRKlL1ZFlzhCHoVgpTcqUlTQxjvGCaFKb+734TTu3nkHUWVZltA1E/swTvimo/aXLtkxnLFrc0EQ==", "path": "humanizer.core.bn-bd/2.14.1", "hashPath": "humanizer.core.bn-bd.2.14.1.nupkg.sha512"}, "Humanizer.Core.cs/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-jWrQkiCTy3L2u1T86cFkgijX6k7hoB0pdcFMWYaSZnm6rvG/XJE40tfhYyKhYYgIc1x9P2GO5AC7xXvFnFdqMQ==", "path": "humanizer.core.cs/2.14.1", "hashPath": "humanizer.core.cs.2.14.1.nupkg.sha512"}, "Humanizer.Core.da/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-5o0rJyE/2wWUUphC79rgYDnif/21MKTTx9LIzRVz9cjCIVFrJ2bDyR2gapvI9D6fjoyvD1NAfkN18SHBsO8S9g==", "path": "humanizer.core.da/2.14.1", "hashPath": "humanizer.core.da.2.14.1.nupkg.sha512"}, "Humanizer.Core.de/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-9JD/p+rqjb8f5RdZ3aEJqbjMYkbk4VFii2QDnnOdNo6ywEfg/A5YeOQ55CaBJmy7KvV4tOK4+qHJnX/tg3Z54A==", "path": "humanizer.core.de/2.14.1", "hashPath": "humanizer.core.de.2.14.1.nupkg.sha512"}, "Humanizer.Core.el/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-Xmv6sTL5mqjOWGGpqY7bvbfK5RngaUHSa8fYDGSLyxY9mGdNbDcasnRnMOvi0SxJS9gAqBCn21Xi90n2SHZbFA==", "path": "humanizer.core.el/2.14.1", "hashPath": "humanizer.core.el.2.14.1.nupkg.sha512"}, "Humanizer.Core.es/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-e//OIAeMB7pjBV1HqqI4pM2Bcw3Jwgpyz9G5Fi4c+RJvhqFwztoWxW57PzTnNJE2lbhGGLQZihFZjsbTUsbczA==", "path": "humanizer.core.es/2.14.1", "hashPath": "humanizer.core.es.2.14.1.nupkg.sha512"}, "Humanizer.Core.fa/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-nzDOj1x0NgjXMjsQxrET21t1FbdoRYujzbmZoR8u8ou5CBWY1UNca0j6n/PEJR/iUbt4IxstpszRy41wL/BrpA==", "path": "humanizer.core.fa/2.14.1", "hashPath": "humanizer.core.fa.2.14.1.nupkg.sha512"}, "Humanizer.Core.fi-FI/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vnxxx4LUhp3AzowYi6lZLAA9Lh8UqkdwRh4IE2qDXiVpbo08rSbokATaEzFS+o+/jCNZBmoyyyph3vgmcSzhhQ==", "path": "humanizer.core.fi-fi/2.14.1", "hashPath": "humanizer.core.fi-fi.2.14.1.nupkg.sha512"}, "Humanizer.Core.fr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-2p4g0BYNzFS3u9SOIDByp2VClYKO0K1ecDV4BkB9EYdEPWfFODYnF+8CH8LpUrpxL2TuWo2fiFx/4Jcmrnkbpg==", "path": "humanizer.core.fr/2.14.1", "hashPath": "humanizer.core.fr.2.14.1.nupkg.sha512"}, "Humanizer.Core.fr-BE/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-o6R3SerxCRn5Ij8nCihDNMGXlaJ/1AqefteAssgmU2qXYlSAGdhxmnrQAXZUDlE4YWt/XQ6VkNLtH7oMqsSPFQ==", "path": "humanizer.core.fr-be/2.14.1", "hashPath": "humanizer.core.fr-be.2.14.1.nupkg.sha512"}, "Humanizer.Core.he/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-FPsAhy7Iw6hb+ZitLgYC26xNcgGAHXb0V823yFAzcyoL5ozM+DCJtYfDPYiOpsJhEZmKFTM9No0jUn1M89WGvg==", "path": "humanizer.core.he/2.14.1", "hashPath": "humanizer.core.he.2.14.1.nupkg.sha512"}, "Humanizer.Core.hr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-chnaD89yOlST142AMkAKLuzRcV5df3yyhDyRU5rypDiqrq2HN8y1UR3h1IicEAEtXLoOEQyjSAkAQ6QuXkn7aw==", "path": "humanizer.core.hr/2.14.1", "hashPath": "humanizer.core.hr.2.14.1.nupkg.sha512"}, "Humanizer.Core.hu/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-hAfnaoF9LTGU/CmFdbnvugN4tIs8ppevVMe3e5bD24+tuKsggMc5hYta9aiydI8JH9JnuVmxvNI4DJee1tK05A==", "path": "humanizer.core.hu/2.14.1", "hashPath": "humanizer.core.hu.2.14.1.nupkg.sha512"}, "Humanizer.Core.hy/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-sVIKxOiSBUb4gStRHo9XwwAg9w7TNvAXbjy176gyTtaTiZkcjr9aCPziUlYAF07oNz6SdwdC2mwJBGgvZ0Sl2g==", "path": "humanizer.core.hy/2.14.1", "hashPath": "humanizer.core.hy.2.14.1.nupkg.sha512"}, "Humanizer.Core.id/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-4Zl3GTvk3a49Ia/WDNQ97eCupjjQRs2iCIZEQdmkiqyaLWttfb+cYXDMGthP42nufUL0SRsvBctN67oSpnXtsg==", "path": "humanizer.core.id/2.14.1", "hashPath": "humanizer.core.id.2.14.1.nupkg.sha512"}, "Humanizer.Core.is/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-R67A9j/nNgcWzU7gZy1AJ07ABSLvogRbqOWvfRDn4q6hNdbg/mjGjZBp4qCTPnB2mHQQTCKo3oeCUayBCNIBCw==", "path": "humanizer.core.is/2.14.1", "hashPath": "humanizer.core.is.2.14.1.nupkg.sha512"}, "Humanizer.Core.it/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-jYxGeN4XIKHVND02FZ+Woir3CUTyBhLsqxu9iqR/9BISArkMf1Px6i5pRZnvq4fc5Zn1qw71GKKoCaHDJBsLFw==", "path": "humanizer.core.it/2.14.1", "hashPath": "humanizer.core.it.2.14.1.nupkg.sha512"}, "Humanizer.Core.ja/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-TM3ablFNoYx4cYJybmRgpDioHpiKSD7q0QtMrmpsqwtiiEsdW5zz/q4PolwAczFnvrKpN6nBXdjnPPKVet93ng==", "path": "humanizer.core.ja/2.14.1", "hashPath": "humanizer.core.ja.2.14.1.nupkg.sha512"}, "Humanizer.Core.ko-KR/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-CtvwvK941k/U0r8PGdEuBEMdW6jv/rBiA9tUhakC7Zd2rA/HCnDcbr1DiNZ+/tRshnhzxy/qwmpY8h4qcAYCtQ==", "path": "humanizer.core.ko-kr/2.14.1", "hashPath": "humanizer.core.ko-kr.2.14.1.nupkg.sha512"}, "Humanizer.Core.ku/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-vHmzXcVMe+LNrF9txpdHzpG7XJX65SiN9GQd/Zkt6gsGIIEeECHrkwCN5Jnlkddw2M/b0HS4SNxdR1GrSn7uCA==", "path": "humanizer.core.ku/2.14.1", "hashPath": "humanizer.core.ku.2.14.1.nupkg.sha512"}, "Humanizer.Core.lv/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-E1/KUVnYBS1bdOTMNDD7LV/jdoZv/fbWTLPtvwdMtSdqLyRTllv6PGM9xVQoFDYlpvVGtEl/09glCojPHw8ffA==", "path": "humanizer.core.lv/2.14.1", "hashPath": "humanizer.core.lv.2.14.1.nupkg.sha512"}, "Humanizer.Core.ms-MY/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-vX8oq9HnYmAF7bek4aGgGFJficHDRTLgp/EOiPv9mBZq0i4SA96qVMYSjJ2YTaxs7Eljqit7pfpE2nmBhY5Fnw==", "path": "humanizer.core.ms-my/2.14.1", "hashPath": "humanizer.core.ms-my.2.14.1.nupkg.sha512"}, "Humanizer.Core.mt/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-pEgTBzUI9hzemF7xrIZigl44LidTUhNu4x/P6M9sAwZjkUF0mMkbpxKkaasOql7lLafKrnszs0xFfaxQyzeuZQ==", "path": "humanizer.core.mt/2.14.1", "hashPath": "humanizer.core.mt.2.14.1.nupkg.sha512"}, "Humanizer.Core.nb/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-mbs3m6JJq53ssLqVPxNfqSdTxAcZN3njlG8yhJVx83XVedpTe1ECK9aCa8FKVOXv93Gl+yRHF82Hw9T9LWv2hw==", "path": "humanizer.core.nb/2.14.1", "hashPath": "humanizer.core.nb.2.14.1.nupkg.sha512"}, "Humanizer.Core.nb-NO/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-AsJxrrVYmIMbKDGe8W6Z6//wKv9dhWH7RsTcEHSr4tQt/80pcNvLi0hgD3fqfTtg0tWKtgch2cLf4prorEV+5A==", "path": "humanizer.core.nb-no/2.14.1", "hashPath": "humanizer.core.nb-no.2.14.1.nupkg.sha512"}, "Humanizer.Core.nl/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-24b0OUdzJxfoqiHPCtYnR5Y4l/s4Oh7KW7uDp+qX25NMAHLCGog2eRfA7p2kRJp8LvnynwwQxm2p534V9m55wQ==", "path": "humanizer.core.nl/2.14.1", "hashPath": "humanizer.core.nl.2.14.1.nupkg.sha512"}, "Humanizer.Core.pl/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-17mJNYaBssENVZyQHduiq+bvdXS0nhZJGEXtPKoMhKv3GD//WO0mEfd9wjEBsWCSmWI7bjRqhCidxzN+YtJmsg==", "path": "humanizer.core.pl/2.14.1", "hashPath": "humanizer.core.pl.2.14.1.nupkg.sha512"}, "Humanizer.Core.pt/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-8HB8qavcVp2la1GJX6t+G9nDYtylPKzyhxr9LAooIei9MnQvNsjEiIE4QvHoeDZ4weuQ9CsPg1c211XUMVEZ4A==", "path": "humanizer.core.pt/2.14.1", "hashPath": "humanizer.core.pt.2.14.1.nupkg.sha512"}, "Humanizer.Core.ro/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-psXNOcA6R8fSHoQYhpBTtTTYiOk8OBoN3PKCEDgsJKIyeY5xuK81IBdGi77qGZMu/OwBRQjQCBMtPJb0f4O1+A==", "path": "humanizer.core.ro/2.14.1", "hashPath": "humanizer.core.ro.2.14.1.nupkg.sha512"}, "Humanizer.Core.ru/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-zm245xUWrajSN2t9H7BTf84/2APbUkKlUJpcdgsvTdAysr1ag9fi1APu6JEok39RRBXDfNRVZHawQ/U8X0pSvQ==", "path": "humanizer.core.ru/2.14.1", "hashPath": "humanizer.core.ru.2.14.1.nupkg.sha512"}, "Humanizer.Core.sk/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ncw24Vf3ioRnbU4MsMFHafkyYi8JOnTqvK741GftlQvAbULBoTz2+e7JByOaasqeSi0KfTXeegJO+5Wk1c0Mbw==", "path": "humanizer.core.sk/2.14.1", "hashPath": "humanizer.core.sk.2.14.1.nupkg.sha512"}, "Humanizer.Core.sl/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-l8sUy4ciAIbVThWNL0atzTS2HWtv8qJrsGWNlqrEKmPwA4SdKolSqnTes9V89fyZTc2Q43jK8fgzVE2C7t009A==", "path": "humanizer.core.sl/2.14.1", "hashPath": "humanizer.core.sl.2.14.1.nupkg.sha512"}, "Humanizer.Core.sr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-rnNvhpkOrWEymy7R/MiFv7uef8YO5HuXDyvojZ7JpijHWA5dXuVXooCOiA/3E93fYa3pxDuG2OQe4M/olXbQ7w==", "path": "humanizer.core.sr/2.14.1", "hashPath": "humanizer.core.sr.2.14.1.nupkg.sha512"}, "Humanizer.Core.sr-Latn/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-nuy/ykpk974F8ItoQMS00kJPr2dFNjOSjgzCwfysbu7+gjqHmbLcYs7G4kshLwdA4AsVncxp99LYeJgoh1JF5g==", "path": "humanizer.core.sr-latn/2.14.1", "hashPath": "humanizer.core.sr-latn.2.14.1.nupkg.sha512"}, "Humanizer.Core.sv/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-E53+tpAG0RCp+cSSI7TfBPC+NnsEqUuoSV0sU+rWRXWr9MbRWx1+Zj02XMojqjGzHjjOrBFBBio6m74seFl0AA==", "path": "humanizer.core.sv/2.14.1", "hashPath": "humanizer.core.sv.2.14.1.nupkg.sha512"}, "Humanizer.Core.th-TH/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-eSevlJtvs1r4vQarNPfZ2kKDp/xMhuD00tVVzRXkSh1IAZbBJI/x2ydxUOwfK9bEwEp+YjvL1Djx2+kw7ziu7g==", "path": "humanizer.core.th-th/2.14.1", "hashPath": "humanizer.core.th-th.2.14.1.nupkg.sha512"}, "Humanizer.Core.tr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-rQ8N+o7yFcFqdbtu1mmbrXFi8TQ+uy+fVH9OPI0CI3Cu1om5hUU/GOMC3hXsTCI6d79y4XX+0HbnD7FT5khegA==", "path": "humanizer.core.tr/2.14.1", "hashPath": "humanizer.core.tr.2.14.1.nupkg.sha512"}, "Humanizer.Core.uk/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-2uEfujwXKNm6bdpukaLtEJD+04uUtQD65nSGCetA1fYNizItEaIBUboNfr3GzJxSMQotNwGVM3+nSn8jTd0VSg==", "path": "humanizer.core.uk/2.14.1", "hashPath": "humanizer.core.uk.2.14.1.nupkg.sha512"}, "Humanizer.Core.uz-Cyrl-UZ/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-TD3ME2sprAvFqk9tkWrvSKx5XxEMlAn1sjk+cYClSWZlIMhQQ2Bp/w0VjX1Kc5oeKjxRAnR7vFcLUFLiZIDk9Q==", "path": "humanizer.core.uz-cyrl-uz/2.14.1", "hashPath": "humanizer.core.uz-cyrl-uz.2.14.1.nupkg.sha512"}, "Humanizer.Core.uz-Latn-UZ/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-/kHAoF4g0GahnugZiEMpaHlxb+W6jCEbWIdsq9/I1k48ULOsl/J0pxZj93lXC3omGzVF1BTVIeAtv5fW06Phsg==", "path": "humanizer.core.uz-latn-uz/2.14.1", "hashPath": "humanizer.core.uz-latn-uz.2.14.1.nupkg.sha512"}, "Humanizer.Core.vi/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-rsQNh9rmHMBtnsUUlJbShMsIMGflZtPmrMM6JNDw20nhsvqfrdcoDD8cMnLAbuSovtc3dP+swRmLQzKmXDTVPA==", "path": "humanizer.core.vi/2.14.1", "hashPath": "humanizer.core.vi.2.14.1.nupkg.sha512"}, "Humanizer.Core.zh-CN/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-uH2dWhrgugkCjDmduLdAFO9w1Mo0q07EuvM0QiIZCVm6FMCu/lGv2fpMu4GX+4HLZ6h5T2Pg9FIdDLCPN2a67w==", "path": "humanizer.core.zh-cn/2.14.1", "hashPath": "humanizer.core.zh-cn.2.14.1.nupkg.sha512"}, "Humanizer.Core.zh-Hans/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-WH6IhJ8V1UBG7rZXQk3dZUoP2gsi8a0WkL8xL0sN6WGiv695s8nVcmab9tWz20ySQbuzp0UkSxUQFi5jJHIpOQ==", "path": "humanizer.core.zh-hans/2.14.1", "hashPath": "humanizer.core.zh-hans.2.14.1.nupkg.sha512"}, "Humanizer.Core.zh-Hant/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-VIXB7HCUC34OoaGnO3HJVtSv2/wljPhjV7eKH4+TFPgQdJj2lvHNKY41Dtg0Bphu7X5UaXFR4zrYYyo+GNOjbA==", "path": "humanizer.core.zh-hant/2.14.1", "hashPath": "humanizer.core.zh-hant.2.14.1.nupkg.sha512"}, "libsodium/********": {"type": "package", "serviceable": true, "sha512": "sha512-flArHoVdscSzyV8ZdPV+bqqY2TTFlaN+xZf/vIqsmHI51KVcD/mOdUPaK3n/k/wGKz8dppiktXUqSmf3AXFgig==", "path": "libsodium/********", "hashPath": "libsodium.********.nupkg.sha512"}, "log4net/2.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-qnnDf/ubJzwm2i1xH7nRMjEDoD+ctse7nZDqb+p7L1PvZc6ykpMoEesWr1/9hFqlsbII2v9e8yyQHJhoDQh7ZA==", "path": "log4net/2.0.17", "hashPath": "log4net.2.0.17.nupkg.sha512"}, "MailKit/4.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-rIqJm92qtHvk1hDchsJ95Hy7n46A7imE24ol++ikXBsjf3Bi1qDBu4H91FfY6LrYXJaxRlc2gIIpC8AOJrCbqg==", "path": "mailkit/4.12.1", "hashPath": "mailkit.4.12.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-afrTDM8viZRruJGNkGa0pMhNqgjWBLln3DBPYrQaklOQn+wE9B5ZvOpi7l8l68JEwsBUVKteKyiY1ivPlK6kQw==", "path": "microsoft.aspnetcore.authentication.openidconnect/9.0.0", "hashPath": "microsoft.aspnetcore.authentication.openidconnect.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M1dzTEl+2+RqT4vWcqEpWasPXHd58wC93U7QMlmPSmx+qixyVxCQjZ183wr7Wa68b4pF7wC501MU9rdA0ZNhMg==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9X4cx2IHNpYb9ka984BjDpJnKkindW17Z2kR/RI5pbTcbVUVMJjiAKnBhAqH24KtAEf1AU64LD60byzCn0/n8w==", "path": "microsoft.aspnetcore.cryptography.keyderivation/9.0.0", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iWGrOsmmgz7wKc27SzC/rWrTuzl/jqWKIl/+YP7OL3mrDcADS/W5WFtmGkfQCAVGv0aZyck4CVfAVdjZLyyY8w==", "path": "microsoft.aspnetcore.diagnostics.entityframeworkcore/9.0.0", "hashPath": "microsoft.aspnetcore.diagnostics.entityframeworkcore.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fwQkBQGaiRKDQWBc7PXxDiDXtsUsRPL88Jp0CqjqoDhd9p5uHSyuv6g3ALq2EbCvKcWk/7pOKsJiDomPvp/ptA==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/9.0.0", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.UI/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VCOH8nyG/zU9BXoL77GncmKL3szxWX0oasYVZP3pvWYRxqr9aHnWBu14JBa2SbbvhCA7vsSWfs0byeAwaiV4DA==", "path": "microsoft.aspnetcore.identity.ui/9.0.0", "hashPath": "microsoft.aspnetcore.identity.ui.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M0h+ChPgydX2xY17agiphnAVa/Qh05RAP8eeuqGGhQKT10claRBlLNO6d2/oSV8zy0RLHzwLnNZm5xuC/gckGA==", "path": "microsoft.aspnetcore.mvc.razor.extensions/6.0.0", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eonBqJduSWG7Sdk8Y8FZ99Raj4UgEQ8/8IMxm6fuv8WlD3r+ZkPVBX9zKMRBa4lAyq+sxU9pu1FDGT2kUtTD8w==", "path": "microsoft.aspnetcore.mvc.razor.runtimecompilation/9.0.0", "hashPath": "microsoft.aspnetcore.mvc.razor.runtimecompilation.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.24": {"type": "package", "serviceable": true, "sha512": "sha512-kBL6ljTREp/3fk8EKN27mrPy3WTqWUjiqCkKFlCKHUKRO3/9rAasKizX3vPWy4ZTcNsIPmVWUHwjDFmiW4MyNA==", "path": "microsoft.aspnetcore.razor.language/6.0.24", "hashPath": "microsoft.aspnetcore.razor.language.6.0.24.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build/17.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-ZmGA8vhVXFzC4oo48ybQKlEybVKd0Ntfdr+Enqrn5ES1R6e/krIK9hLk0W33xuT0/G6QYd3YdhJZh+Xle717Ag==", "path": "microsoft.build/17.10.4", "hashPath": "microsoft.build.17.10.4.nupkg.sha512"}, "Microsoft.Build.Framework/17.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-4qXCwNOXBR1dyCzuks9SwTwFJQO/xmf2wcMislotDWJu7MN/r3xDNoU8Ae5QmKIHPaLG1xmfDkYS7qBVzxmeKw==", "path": "microsoft.build.framework/17.10.4", "hashPath": "microsoft.build.framework.17.10.4.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.AnalyzerUtilities/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-gyQ70pJ4T7hu/s0+QnEaXtYfeG/JrttGnxHJlrhpxsQjRIUGuRhVwNBtkHHYOrUAZ/l47L98/NiJX6QmTwAyrg==", "path": "microsoft.codeanalysis.analyzerutilities/3.3.0", "hashPath": "microsoft.codeanalysis.analyzerutilities.3.3.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Features/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Gpas3l8PE1xz1VDIJNMkYuoFPXtuALxybP04caXh9avC2a0elsoBdukndkJXVZgdKPwraf0a98s7tjqnEk5QIQ==", "path": "microsoft.codeanalysis.csharp.features/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.features.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Elfie/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-r12elUp4MRjdnRfxEP+xqVSUUfG3yIJTBEJGwbfvF5oU4m0jb9HC0gFG28V/dAkYGMkRmHVi3qvrnBLQSw9X3Q==", "path": "microsoft.codeanalysis.elfie/1.0.0", "hashPath": "microsoft.codeanalysis.elfie.1.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Features/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-sCVzMtSETGE16KeScwwlVfxaKRbUMSf/cgRPRPMJuou37SLT7XkIBzJu4e7mlFTzpJbfalV5tOcKpUtLO3eJAg==", "path": "microsoft.codeanalysis.features/4.8.0", "hashPath": "microsoft.codeanalysis.features.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/6.0.24": {"type": "package", "serviceable": true, "sha512": "sha512-xIAjR6l/1PO2ILT6/lOGYfe8OzMqfqxh1lxFuM4Exluwc2sQhJw0kS7pEyJ0DE/UMYu6Jcdc53DmjOxQUDT2Pg==", "path": "microsoft.codeanalysis.razor/6.0.24", "hashPath": "microsoft.codeanalysis.razor.6.0.24.nupkg.sha512"}, "Microsoft.CodeAnalysis.Scripting.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-ysiNNbAASVhV9wEd5oY2x99EwaVYtB13XZRjHsgWT/R1mQkxZF8jWsf7JWaZxD1+jNoz1QCQ6nbe+vr+6QvlFA==", "path": "microsoft.codeanalysis.scripting.common/4.8.0", "hashPath": "microsoft.codeanalysis.scripting.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-ojg2XWmih4ubPPtrhRqqXk0SM6wC2ZSTkNNEAlYBhMo4IsRHjLazFc0abzcZCNfw1JyWcqY7vGutWTv8ZaFD9g==", "path": "microsoft.data.sqlclient/5.2.1", "hashPath": "microsoft.data.sqlclient.5.2.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512"}, "Microsoft.DiaSymReader/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QcZrCETsBJqy/vQpFtJc+jSXQ0K5sucQ6NUFbTNVHD4vfZZOwjZ/3sBzczkC4DityhD3AVO/+K/+9ioLs1AgRA==", "path": "microsoft.diasymreader/2.0.0", "hashPath": "microsoft.diasymreader.2.0.0.nupkg.sha512"}, "Microsoft.DotNet.Scaffolding.Shared/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9pfRsTzUANgI6J7nFjYip50ifcvmORjMmFByXmdYa//x8toziydhbg0cMylP1S2mRf4/96VKSAfpayYrO3m4Ww==", "path": "microsoft.dotnet.scaffolding.shared/9.0.0", "hashPath": "microsoft.dotnet.scaffolding.shared.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON><PERSON>uybgcpW32y985eOYxSoZ9IiL0UTYQlY0y1Pt1iHAnpZj/dQHREpSpry1RNvk8YjAeoAkWFdem5conqB9zQ==", "path": "microsoft.entityframeworkcore/9.0.2", "hashPath": "microsoft.entityframeworkcore.9.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-oVSjNSIYHsk0N66eqAWgDcyo9etEFbUswbz7SmlYR6nGp05byHrJAYM5N8U2aGWJWJI6WvIC2e4TXJgH6GZ6HQ==", "path": "microsoft.entityframeworkcore.abstractions/9.0.2", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-w4jzX7XI+L3erVGzbHXpx64A3QaLXxqG3f1vPpGYYZGpxOIHkh7e4iLLD7cq4Ng1vjkwzWl5ZJp0Kj/nHsgFYg==", "path": "microsoft.entityframeworkcore.analyzers/9.0.2", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pqo8I+yHJ3VQrAoY0hiSncf+5P7gN/RkNilK5e+/K/yKh+yAWxdUAI6t0TG26a9VPlCa9FhyklzyFvRyj3YG9A==", "path": "microsoft.entityframeworkcore.design/9.0.0", "hashPath": "microsoft.entityframeworkcore.design.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-r7O4N5uaM95InVSGUj7SMOQWN0f1PBF2Y30ow7Jg+pGX5GJCRVd/1fq83lQ50YMyq+EzyHac5o4CDQA2RsjKJQ==", "path": "microsoft.entityframeworkcore.relational/9.0.2", "hashPath": "microsoft.entityframeworkcore.relational.9.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Y7/3kgz6C5kRFeELLZ5VeIeBlxB31x/ywscbN4r1JqTXIy8WWGo0CqzuOxBy4UzaTzpifElAZvv4fyD3ZQK5w==", "path": "microsoft.entityframeworkcore.sqlserver/9.0.0", "hashPath": "microsoft.entityframeworkcore.sqlserver.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qjw+3/CaWiWnyVblvKHY11rQKH5eHQDSbtxjgxVhxGJrOpmjZ3JxtD0pjwkr4y/ELubsXr6xDfBcRJSkX/9hWQ==", "path": "microsoft.entityframeworkcore.tools/9.0.0", "hashPath": "microsoft.entityframeworkcore.tools.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GMCX3zybUB22aAADjYPXrWhhd1HNMkcY5EcFAJnXy/4k5pPpJ6TS4VRl37xfrtosNyzbpO2SI7pd2Q5PvggSdg==", "path": "microsoft.extensions.ambientmetadata.application/9.2.0", "hashPath": "microsoft.extensions.ambientmetadata.application.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-a7QhA25n+BzSM5r5d7JznfyluMBGI7z3qyLlFviZ1Eiqv6DdiK27sLZdP/rpYirBM6UYAKxu5TbmfhIy13GN9A==", "path": "microsoft.extensions.caching.abstractions/9.0.2", "hashPath": "microsoft.extensions.caching.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-AlEfp0DMz8E1h1Exi8LBrUCNmCYcGDfSM4F/uK1D1cYx/R3w0LVvlmjICqxqXTsy7BEZaCf5leRZY2FuPEiFaw==", "path": "microsoft.extensions.caching.memory/9.0.2", "hashPath": "microsoft.extensions.caching.memory.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Te+N4xphDlGIS90lKJMZyezFiMWKLAtYV2/M8gGJG4thH6xyC7LWhMzgz2+tWMehxwZlBUq2D9DvVpjKBZFTPQ==", "path": "microsoft.extensions.compliance.abstractions/9.2.0", "hashPath": "microsoft.extensions.compliance.abstractions.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-EBZW+u96tApIvNtjymXEIS44tH0I/jNwABHo4c33AchWOiDWCq2rL3klpnIo+xGrxoVGJzPDISV6hZ+a9C9SzQ==", "path": "microsoft.extensions.configuration/9.0.2", "hashPath": "microsoft.extensions.configuration.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-I0O/270E/lUNqbBxlRVjxKOMZyYjP88dpEgQTveml+h2lTzAP4vbawLVwjS9SC7lKaU893bwyyNz0IVJYsm9EA==", "path": "microsoft.extensions.configuration.abstractions/9.0.2", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-krJ04xR0aPXrOf5dkNASg6aJjsdzexvsMRL6UNOUjiTzqBvRr95sJ1owoKEm89bSONQCfZNhHrAFV9ahDqIPIw==", "path": "microsoft.extensions.configuration.binder/9.0.2", "hashPath": "microsoft.extensions.configuration.binder.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZffbJrskOZ40JTzcTyKwFHS5eACSWp2bUQBBApIgGV+es8RaTD4OxUG7XxFr3RIPLXtYQ1jQzF2DjKB5fZn7Qg==", "path": "microsoft.extensions.dependencyinjection/9.0.2", "hashPath": "microsoft.extensions.dependencyinjection.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-MNe7GSTBf3jQx5vYrXF0NZvn6l7hUKF6J54ENfAgCO8y6xjN1XUmKKWG464LP2ye6QqDiA1dkaWEZBYnhoZzjg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-WcwfTpl3IcPcaahTVEaJwMUg1eWog1SkIA6jQZZFqMXiMX9/tVkhNB6yzUQmBdGWdlWDDRKpOmK7T7x1Uu05pQ==", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.2.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-kwFWk6DPaj1Roc0CExRv+TTwjsiERZA730jQIPlwCcS5tMaCAQtaGfwAK0z8CMFpVTiT+MgKXpd/P50qVCuIgg==", "path": "microsoft.extensions.diagnostics/9.0.2", "hashPath": "microsoft.extensions.diagnostics.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-kFwIZEC/37cwKuEm/nXvjF7A/Myz9O7c7P9Csgz6AOiiDE62zdOG5Bu7VkROu1oMYaX0wgijPJ5LqVt6+JKjVg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.2", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-et5JevHsLv1w1O1Zhb6LiUfai/nmDRzIHnbrZJdzLsIbbMCKTZpeHuANYIppAD//n12KvgOne05j4cu0GhG9gw==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.2.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-IcOBmTlr2jySswU+3x8c3ql87FRwTVPQgVKaV5AXzPT5u0VItfNU8SMbESpdSp5STwxT/1R99WYszgHWsVkzhg==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.2", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Ev1goLIvggLF6uCs6oZvdr9JM+2b1Zj+4FLdBWNW5iw3tm2BymVIb0yMsjnQTBWL7YUmqVWH3u45hSqOfvuqg==", "path": "microsoft.extensions.fileproviders.embedded/9.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3+ZUSpOSmie+o8NnLIRqCxSh65XL/ExU7JYnFOg58awDRlY3lVpZ9A369jkoZL1rpsq7LDhEfkn2ghhGaY1y5Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jGFKZiXs2HNseK3NK/rfwHNNovER71jSj4BD1a/649ml9+h6oEtYd0GSALZDNW8jZ2Rh+oAeadOa6sagYW1F2A==", "path": "microsoft.extensions.filesystemglobbing/9.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-PvjZW6CMdZbPbOwKsQXYN5VPtIWZQqdTRuBPZiW3skhU3hymB17XSlLVC4uaBbDZU+/3eHG3p80y+MzZxZqR7Q==", "path": "microsoft.extensions.hosting.abstractions/9.0.2", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-34+kcwxPZr3Owk9eZx268+gqGNB8G/8Y96gZHomxam0IOH08FhPBjPrLWDtKdVn4+sVUUJnJMpECSTJi4XXCcg==", "path": "microsoft.extensions.http/9.0.2", "hashPath": "microsoft.extensions.http.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Eeup1LuD5hVk5SsKAuX1D7I9sF380MjrNG10IaaauRLOmrRg8rq2TA8PYTXVBXf3MLkZ6m2xpBqRbZdxf8ygkg==", "path": "microsoft.extensions.http.diagnostics/9.2.0", "hashPath": "microsoft.extensions.http.diagnostics.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-2y5a9Iijc9iTUN1M7rH2+kUMJPuuxTgfUyL9iAOqe4ueuWtTfG1SVX/oAj35q46OV4kSgCeJC82dLQ96xOo/RQ==", "path": "microsoft.extensions.http.polly/9.0.2", "hashPath": "microsoft.extensions.http.polly.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Km+YyCuk1IaeOsAzPDygtgsUOh3Fi89hpA18si0tFJmpSBf9aKzP9ffV5j7YOoVDvRWirpumXAPQzk1inBsvKw==", "path": "microsoft.extensions.http.resilience/9.2.0", "hashPath": "microsoft.extensions.http.resilience.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+cQjUs8PIheIMALzrf/e4gW6A/yOK8XYBxeEmAfLvVIaV9lsBGvVT0zjEZ1KPQDJ9nUeQ9uAw077J7LPUwv8wA==", "path": "microsoft.extensions.identity.core/9.0.0", "hashPath": "microsoft.extensions.identity.core.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XG3opf0KgWoYAUdLRhrIvI46W+/E45Ov8rzgwr0omrq5u06MCrsuMm0nPmd+pIWjMXRxbBk1uL47zGyW1lI5Hw==", "path": "microsoft.extensions.identity.stores/9.0.0", "hashPath": "microsoft.extensions.identity.stores.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-loV/0UNpt2bD+6kCDzFALVE63CDtqzPeC0LAetkdhiEr/tTNbvOlQ7CBResH7BQBd3cikrwiBfaHdyHMFUlc2g==", "path": "microsoft.extensions.logging/9.0.2", "hashPath": "microsoft.extensions.logging.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dV9s2Lamc8jSaqhl2BQSPn/AryDIH2sSbQUyLitLXV0ROmsb+SROnn2cH939JFbsNrnf3mIM3GNRKT7P0ldwLg==", "path": "microsoft.extensions.logging.abstractions/9.0.2", "hashPath": "microsoft.extensions.logging.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-pnwYZE7U6d3Y6iMVqADOAUUMMBGYAQPsT3fMwVr/V1Wdpe5DuVGFcViZavUthSJ5724NmelIl1cYy+kRfKfRPQ==", "path": "microsoft.extensions.logging.configuration/9.0.2", "hashPath": "microsoft.extensions.logging.configuration.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nWx7uY6lfkmtpyC2dGc0IxtrZZs/LnLCQHw3YYQucbqWj8a27U/dZ+eh72O3ZiolqLzzLkVzoC+w/M8dZwxRTw==", "path": "microsoft.extensions.objectpool/9.0.2", "hashPath": "microsoft.extensions.objectpool.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-zr98z+AN8+isdmDmQRuEJ/DAKZGUTHmdv3t0ZzjHvNqvA44nAgkXE9kYtfoN6581iALChhVaSw2Owt+Z2lVbkQ==", "path": "microsoft.extensions.options/9.0.2", "hashPath": "microsoft.extensions.options.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-OPm1NXdMg4Kb4Kz+YHdbBQfekh7MqQZ7liZ5dYUd+IbJakinv9Fl7Ck6Strbgs0a6E76UGbP/jHR532K/7/feQ==", "path": "microsoft.extensions.options.configurationextensions/9.0.2", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-puBMtKe/wLuYa7H6docBkLlfec+h8L35DXqsDKKJgW0WY5oCwJ3cBJKcDaZchv6knAyqOMfsl6VUbaR++E5LXA==", "path": "microsoft.extensions.primitives/9.0.2", "hashPath": "microsoft.extensions.primitives.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Resilience/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-dyaM+Jeznh/i21bOrrRs3xceFfn0571EOjOq95dRXmL1rHDLC4ExhACJ2xipRBP6g1AgRNqmryi+hMrVWWgmlg==", "path": "microsoft.extensions.resilience/9.2.0", "hashPath": "microsoft.extensions.resilience.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-4+bw7W4RrAMrND9TxonnSmzJOdXiPxljoda8OPJiReIN607mKCc0t0Mf28sHNsTujO1XQw28wsI0poxeeQxohw==", "path": "microsoft.extensions.telemetry/9.2.0", "hashPath": "microsoft.extensions.telemetry.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-kEl+5G3RqS20XaEhHh/nOugcjKEK+rgVtMJra1iuwNzdzQXElelf3vu8TugcT7rIZ/T4T76EKW1OX/fmlxz4hw==", "path": "microsoft.extensions.telemetry.abstractions/9.2.0", "hashPath": "microsoft.extensions.telemetry.abstractions.9.2.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.60.3": {"type": "package", "serviceable": true, "sha512": "sha512-jve1RzmSpBhGlqMzPva6VfRbLMLZZc1Q8WRVZf8+iEruQkBgDTJPq8OeTehcY4GGYG1j6UB1xVofVE+n4BLDdw==", "path": "microsoft.identity.client/4.60.3", "hashPath": "microsoft.identity.client.4.60.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.60.3": {"type": "package", "serviceable": true, "sha512": "sha512-X1Cz14/RbmlLshusE5u2zfG+5ul6ttgou19BZe5Mdw1qm6fgOI9/imBB2TIsx2UD7nkgd2+MCSzhbukZf7udeg==", "path": "microsoft.identity.client.extensions.msal/4.60.3", "hashPath": "microsoft.identity.client.extensions.msal.4.60.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-4dDpw7DJ2yx2vFM/w0sceXoByUhrU68eMdlXyzsPTWPtAfgCbkuMl7jfLBLegmgerbOzGNMm7zq5xwr4+7yTSg==", "path": "microsoft.identitymodel.abstractions/8.4.0", "hashPath": "microsoft.identitymodel.abstractions.8.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-lyMsODXEMNvV0oTUehuz6wkohldFqQg5su/2Hdzr2xS1kdvilqyywkoVnpbJVb7zYr7TA+6rVCTRV/0f2uSBPQ==", "path": "microsoft.identitymodel.jsonwebtokens/8.4.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-vIKIBDMD6l2MeCwkdeh1XHSHNVz9C/qp9PaHMG6SF3OUSAh/2XMVj2zaVc2hs27QRfhh5xR3tvMSs8R96WS2IA==", "path": "microsoft.identitymodel.logging/8.4.0", "hashPath": "microsoft.identitymodel.logging.8.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-cDvZZ8JYto6L1I64sWW17JGwYGuFZ5Qm+WZG+wLk0QHjtuSosujDVAc4nr/sx6+n88q1mdW93rGEl7TCniNp5Q==", "path": "microsoft.identitymodel.protocols/8.4.0", "hashPath": "microsoft.identitymodel.protocols.8.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-n/wBvhj7oq3rZyImUhkUv6bUjwPWK+jG1R3BsCfahLtwoOzIWSPLmzMQ61HCtd1BiCU43IdkX9M+cD1xMmE3xQ==", "path": "microsoft.identitymodel.tokens/8.4.0", "hashPath": "microsoft.identitymodel.tokens.8.4.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-DJHjP1cTSu+sMt0vdNFLH9/wLHkq3EZk6BPZKI/R9anry41Ng/TOOZM0iXo+stXlY3LivTW/70Y26oMVGgTsTg==", "path": "microsoft.net.http.headers/9.0.2", "hashPath": "microsoft.net.http.headers.9.0.2.nupkg.sha512"}, "Microsoft.NET.StringTools/17.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-wyABaqY+IHCMMSTQmcc3Ca6vbmg5BaEPgicnEgpll+4xyWZWlkQqUwafweUd9VAhBb4jqplMl6voUHQ6yfdUcg==", "path": "microsoft.net.stringtools/17.10.4", "hashPath": "microsoft.net.stringtools.17.10.4.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.22": {"type": "package", "serviceable": true, "sha512": "sha512-aBvunmrdu/x+4CaA/UP1Jx4xWGwk4kymhoIRnn2Vp+zi5/KOPQJ9EkSXHRUr01WcGKtYl3Au7XfkPJbU1G2sjQ==", "path": "microsoft.openapi/1.6.22", "hashPath": "microsoft.openapi.1.6.22.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W9ho78o/92MUDz04r7Al4dMx7djaqtSJE1cR7fMjy+Mm0StL5pVKXF24qnAFWJlip7KEpAa1QP35davXvuis9w==", "path": "microsoft.visualstudio.web.codegeneration/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1VIEZs8DNnefMa0eVDZucz/dk28Sg0QRiNiRJj7SdU8E6UiNJxnkzA748aqA6Qqi8OMTHTBKhzx0Hj9ykIi6/Q==", "path": "microsoft.visualstudio.web.codegeneration.core/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.core.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nO5MUL3iC0WjtAVea5d4v6kVcoL9ae/PnkC6NeEJhWazHKdKj7xfv6D2QvBx8uCIj8FUu9QpvvdN6m/xMp//EQ==", "path": "microsoft.visualstudio.web.codegeneration.design/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.design.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-F4+A6CaXmof/QoeWpqaMMeoVinfUSIMKa5xLOrwsZxGfYl6Qryhb06bkJ8yJaF05WefMM/wnj73oI3Ms2bBh7g==", "path": "microsoft.visualstudio.web.codegeneration.entityframeworkcore/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.entityframeworkcore.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-euoX0M4JnbzSUcFXfDq+GSSdXNRbKGUBTK+8gcnzHmhY3sHgHn9bgeeZDp+LGuoUQaP+WrWA8Nq92gCTcZLWSA==", "path": "microsoft.visualstudio.web.codegeneration.templating/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.templating.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-O8uehWLzgQhq3H2f+dxEkuYF8wWoBrT7iKtQXnHAc96qlVdLSARSxt3hlxqFSzK3ZkHp2P6lHt76LRH6J0PDrw==", "path": "microsoft.visualstudio.web.codegeneration.utils/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.utils.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WJhdsFXkpA0XR6PCjoxe9pRIqT8NV8Ggojv2cwaeCwxApzTAbLnglwADteeF7WlgHnr1VmJ+xdgzzNAAcJ9+Rg==", "path": "microsoft.visualstudio.web.codegenerators.mvc/9.0.0", "hashPath": "microsoft.visualstudio.web.codegenerators.mvc.9.0.0.nupkg.sha512"}, "MimeKit/4.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-PFUHfs6BZxKYM/QPJksAwXphbJf0SEfdSfsoQ6p6yvFRaJPofFJMBiotWhFRrdSUzfp6C6K49EjBIqIwZ2TJqA==", "path": "mimekit/4.12.0", "hashPath": "mimekit.4.12.0.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NSec.Cryptography/22.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-lEntcPYd7h3aZ8xxi/y/4TML7o8w0GEGqd+w4L1omqFLbdCBmhxJAeO2YBmv/fXbJKgKCQLm7+TD4bR605PEUQ==", "path": "nsec.cryptography/22.4.0", "hashPath": "nsec.cryptography.22.4.0.nupkg.sha512"}, "NuGet.Common/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-T3bCiKUSx8wdYpcqr6Dbx93zAqFp689ee/oa1tH22XI/xl7EUzQ7No/WlE1FUqvEX1+Mqar3wRNAn2O/yxo94g==", "path": "nuget.common/6.11.0", "hashPath": "nuget.common.6.11.0.nupkg.sha512"}, "NuGet.Configuration/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-73QprQqmumFrv3Ooi4YWpRYeBj8jZy9gNdOaOCp4pPInpt41SJJAz/aP4je+StwIJvi5HsgPPecLKekDIQEwKg==", "path": "nuget.configuration/6.11.0", "hashPath": "nuget.configuration.6.11.0.nupkg.sha512"}, "NuGet.DependencyResolver.Core/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoiPKPooA+IF+iCsX1ykwi3M0e+yBL34QnwIP3ujhQEn1dhlP/N1XsYAnKkJPxV15EZCahuuS4HtnBsZx+CHKA==", "path": "nuget.dependencyresolver.core/6.11.0", "hashPath": "nuget.dependencyresolver.core.6.11.0.nupkg.sha512"}, "NuGet.Frameworks/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ew/mrfmLF5phsprysHbph2+tdZ10HMHAURavsr/Kx1WhybDG4vmGuoNLbbZMZOqnPRdpyCTc42OKWLoedxpYtA==", "path": "nuget.frameworks/6.11.0", "hashPath": "nuget.frameworks.6.11.0.nupkg.sha512"}, "NuGet.LibraryModel/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-KUV2eeMICMb24OPcICn/wgncNzt6+W+lmFVO5eorTdo1qV4WXxYGyG1NTPiCY+Nrv5H/Ilnv9UaUM2ozqSmnjw==", "path": "nuget.librarymodel/6.11.0", "hashPath": "nuget.librarymodel.6.11.0.nupkg.sha512"}, "NuGet.Packaging/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-VmUv2LedVuPY1tfNybORO2I9IuqOzeV7I5JBD+PwNvJq2bAqovi4FCw2cYI0g+kjOJXBN2lAJfrfnqtUOlVJdQ==", "path": "nuget.packaging/6.11.0", "hashPath": "nuget.packaging.6.11.0.nupkg.sha512"}, "NuGet.ProjectModel/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0KtmDH6fas97WsN73yV2h1F5JT9o6+Y0wlPK+ij9YLKaAXaF6+1HkSaQMMJ+xh9/jCJG9G6nau6InOlb1g48g==", "path": "nuget.projectmodel/6.11.0", "hashPath": "nuget.projectmodel.6.11.0.nupkg.sha512"}, "NuGet.Protocol/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-p5B8oNLLnGhUfMbcS16aRiegj11pD6k+LELyRBqvNFR/pE3yR1XT+g1XS33ME9wvoU+xbCGnl4Grztt1jHPinw==", "path": "nuget.protocol/6.11.0", "hashPath": "nuget.protocol.6.11.0.nupkg.sha512"}, "NuGet.Versioning/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/GGlIj2dd7svplFmASWEueu62veKW0MrMtBaZ7QG8aJTSGv2yE+pgUGhXRcQ4nxNOEq/wLBrz1vkth/1SND7A==", "path": "nuget.versioning/6.11.0", "hashPath": "nuget.versioning.6.11.0.nupkg.sha512"}, "OpenIddict/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-BqA/DUgek/dhOL9DW2omBo+3J3vU4dXLfuhbCZ6IIDZq+BgD4MkPzdaZhJhgoWriMzccPCR7RPTgmzTvvX7H2w==", "path": "openiddict/6.1.1", "hashPath": "openiddict.6.1.1.nupkg.sha512"}, "OpenIddict.Abstractions/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-iNc3ox0tuR+mY2RPDqtTFhnPfxG5KHUVy4fakCbf9U8/bLhavfG3R6g/8O1SG/TIIHXzeDMmsubwCoWn9CM4Sg==", "path": "openiddict.abstractions/6.1.1", "hashPath": "openiddict.abstractions.6.1.1.nupkg.sha512"}, "OpenIddict.AspNetCore/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Z45o+OmliseWJgi/pgeczaXQL5SuN/ohPcF8mI2YCSRWHYJYwuuVN7c3ztLBklMLeRIgkVUFotDW+Qk9pHJphQ==", "path": "openiddict.aspnetcore/6.1.1", "hashPath": "openiddict.aspnetcore.6.1.1.nupkg.sha512"}, "OpenIddict.Client/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-nF47pWG/LDBLUPbQxsiPl3eEDDsYw/a17tpqHXrKQXPmGWNLPvEu/sugjj5ZdzutjivUWctL3MWldSGIuev/gA==", "path": "openiddict.client/6.1.1", "hashPath": "openiddict.client.6.1.1.nupkg.sha512"}, "OpenIddict.Client.AspNetCore/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-N8ZlhSlqzgAZ0XrGQWhWBkzPzwS24+LZwswZsO6wCzVcQc6ZwuU5SiUD+DH1Gm85cn8IGjrAMtwM6jXbNYqmGQ==", "path": "openiddict.client.aspnetcore/6.1.1", "hashPath": "openiddict.client.aspnetcore.6.1.1.nupkg.sha512"}, "OpenIddict.Client.DataProtection/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Mf10RTgBZHFCiLL547hQRdY3lYGxUhp71dKAImbZ1UwN5fRv6+u6rHkcYWMNnmdtIIyEgNV15QykJJ6+LDJynw==", "path": "openiddict.client.dataprotection/6.1.1", "hashPath": "openiddict.client.dataprotection.6.1.1.nupkg.sha512"}, "OpenIddict.Client.SystemIntegration/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-g5wUAAhRMJA4e2bwj4sWJjnzcYuHEF/6bMe4d/8rS9ULOv05bEMmde4Bt0+20S0oCfjorigXpIa5eO1nQMaNSQ==", "path": "openiddict.client.systemintegration/6.1.1", "hashPath": "openiddict.client.systemintegration.6.1.1.nupkg.sha512"}, "OpenIddict.Client.SystemNetHttp/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtBndAPvf7jt8u8y77s+031XwiIwUCOBWQ9Gcoqz1j53bFFJDTo1rHg1eewvoaI9A5V6UZ1PLrMsaF8RJ3lpog==", "path": "openiddict.client.systemnethttp/6.1.1", "hashPath": "openiddict.client.systemnethttp.6.1.1.nupkg.sha512"}, "OpenIddict.Client.WebIntegration/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Bttg78K/KA4Af7l3b+B4fJ3I9cBmZTI9Goztp0iUUWh+rNK5Ka23RDVoetSF1Asplo1pscvipxsbnwpOPxpI/w==", "path": "openiddict.client.webintegration/6.1.1", "hashPath": "openiddict.client.webintegration.6.1.1.nupkg.sha512"}, "OpenIddict.Core/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-mE+AnsnROxI9jnIwXBg76/+s3slPayHie9A4PR5I2MOASi/CIqg248iNBRdB1ALHSyiULDO88/c/j4G2YOjFrw==", "path": "openiddict.core/6.1.1", "hashPath": "openiddict.core.6.1.1.nupkg.sha512"}, "OpenIddict.EntityFrameworkCore/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-y+nNH9K4hlsnwh48MWcr9wN7gS+xAOh9c3ZdBnjLa5gtQq8dT0T3kanpDWLMBOyvVi5UbRNgVIxvRpUp3RT2oA==", "path": "openiddict.entityframeworkcore/6.1.1", "hashPath": "openiddict.entityframeworkcore.6.1.1.nupkg.sha512"}, "OpenIddict.EntityFrameworkCore.Models/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-QiwyNqXO0HElp3lCjHquV1bUR8MlYOEoVGd+H0XILBPXdcKBcrHLPiRTGjgDzTQmwWk6XSHmD6e7YnGhhtKpSg==", "path": "openiddict.entityframeworkcore.models/6.1.1", "hashPath": "openiddict.entityframeworkcore.models.6.1.1.nupkg.sha512"}, "OpenIddict.Quartz/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Qg5hhTWco+o8cFcG72l7f2oPmHrlYiq/yjv55rmlYTeZLQSZ2z254dFM/Qw1QSHZyUCI9K819mDwKwBihKlacA==", "path": "openiddict.quartz/6.1.1", "hashPath": "openiddict.quartz.6.1.1.nupkg.sha512"}, "OpenIddict.Server/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-eP0CiuRTrdJOuQE1aM4PkeWQnuNTNk0C7w083Nt8t8He5KdQagSMRLsOsRE1xVMR1NPG2Cp8GOX/47B0QOaT8w==", "path": "openiddict.server/6.1.1", "hashPath": "openiddict.server.6.1.1.nupkg.sha512"}, "OpenIddict.Server.AspNetCore/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-fYeXmTDFHwLkCgiNmUX0IIwxpim0KJbFjH/jpdRjv30sI5Cksyl9YNrWndmjcGqsMUwX8mS0ChRr90igScjsaw==", "path": "openiddict.server.aspnetcore/6.1.1", "hashPath": "openiddict.server.aspnetcore.6.1.1.nupkg.sha512"}, "OpenIddict.Server.DataProtection/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-qBijXxbV3/tD/P9geTEoZDLfeFmqzXjq2Z0hqz5TvDfZQj+HmCl2VxOBblOIDIdXPjJXibuS+AwbuX3XbRZmqw==", "path": "openiddict.server.dataprotection/6.1.1", "hashPath": "openiddict.server.dataprotection.6.1.1.nupkg.sha512"}, "OpenIddict.Validation/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-EswfSriq61xV4wru48RLCunSOCIULoTUqFcEG6JEstnWQoptcuppUfEWpbnpvBFO3htU9WCC9ybZXEUIvNEkAQ==", "path": "openiddict.validation/6.1.1", "hashPath": "openiddict.validation.6.1.1.nupkg.sha512"}, "OpenIddict.Validation.AspNetCore/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-t6bjzcQUI80ViTlJ8GZgh7NoTuDRZnYOKoR3HoN+z8oqsvdaDPlxtODehGBIBDm+RvfwD2FUQQWLa1+uYNGPCw==", "path": "openiddict.validation.aspnetcore/6.1.1", "hashPath": "openiddict.validation.aspnetcore.6.1.1.nupkg.sha512"}, "OpenIddict.Validation.DataProtection/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-y3ukWnsr+9rO5t9Iy8awxlA3SPQ4YDlKVnrMpgROIWAFfzTt8v2IjFQH8RXKq7gRMDu9ehgVaHI61JR3T7KNBQ==", "path": "openiddict.validation.dataprotection/6.1.1", "hashPath": "openiddict.validation.dataprotection.6.1.1.nupkg.sha512"}, "OpenIddict.Validation.ServerIntegration/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Dm120S1lrXWLFyDP5dEkgTZA3M2dYW2AOEAaUPw+bP57vou8GQh55eEpFfT6giCZg0aF9VOPD9Zwd9gHlpoCTQ==", "path": "openiddict.validation.serverintegration/6.1.1", "hashPath": "openiddict.validation.serverintegration.6.1.1.nupkg.sha512"}, "OpenIddict.Validation.SystemNetHttp/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ShwUslQA5wFyd28BkH3LBQTU6Veow4Abl7TdhUhW3VdiHqS4aGVJSiUoMZnJljxDx8DA/w5UD8WO2S+6GTfXYw==", "path": "openiddict.validation.systemnethttp/6.1.1", "hashPath": "openiddict.validation.systemnethttp.6.1.1.nupkg.sha512"}, "Polly/7.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-bw00Ck5sh6ekduDE3mnCo1ohzuad946uslCDEENu3091+6UKnBuKLo4e+yaNcCzXxOZCXWY2gV4a35+K1d4LDA==", "path": "polly/7.2.4", "hashPath": "polly.7.2.4.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "Polly.Extensions/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "path": "polly.extensions/8.4.2", "hashPath": "polly.extensions.8.4.2.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}, "Quartz/3.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-0S4/gYb/FRQfek65OPAmszbdEmtc4Fq/jSeA5XFnvMIjYL9Zr8g9jWoKneEFzAFJGlESjFULtaUFzValHvqu4A==", "path": "quartz/3.13.1", "hashPath": "quartz.3.13.1.nupkg.sha512"}, "Quartz.Extensions.DependencyInjection/3.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-I3uXVIYMEo00TrEzzHG3koWNTGdinPGjvgBWse7SqMt3ZW7qlKBXB4a5bJsxkKSMjAs4i2Ykx2cJSi/r6zhiCw==", "path": "quartz.extensions.dependencyinjection/3.13.1", "hashPath": "quartz.extensions.dependencyinjection.3.13.1.nupkg.sha512"}, "Quartz.Extensions.Hosting/3.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-pwGofFs0VoIwq8JFXBEeEKFrA/6wJDFekLzQA5oev7+Ow0+lqBlZlzBpPd10hkgVAItL+uZ2SEjP8Ual3QZTww==", "path": "quartz.extensions.hosting/3.13.1", "hashPath": "quartz.extensions.hosting.3.13.1.nupkg.sha512"}, "runtime.any.System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-23g6rqftKmovn2cLeGsuHUYm0FD7pdutb0uQMJpZ3qTvq+zHkgmt6J65VtRry4WDGYlmkMa4xDACtaQ94alNag==", "path": "runtime.any.system.collections/4.3.0", "hashPath": "runtime.any.system.collections.4.3.0.nupkg.sha512"}, "runtime.any.System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sMDBnad4rp4t7GY442Jux0MCUuKL4otn5BK6Ni0ARTXTSpRNBzZ7hpMfKSvnVSED5kYJm96YOWsqV0JH0d2uuw==", "path": "runtime.any.system.globalization/4.3.0", "hashPath": "runtime.any.system.globalization.4.3.0.nupkg.sha512"}, "runtime.any.System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "path": "runtime.any.system.io/4.3.0", "hashPath": "runtime.any.system.io.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "path": "runtime.any.system.reflection/4.3.0", "hashPath": "runtime.any.system.reflection.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "path": "runtime.any.system.reflection.primitives/4.3.0", "hashPath": "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512"}, "runtime.any.System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lxb89SMvf8w9p9+keBLyL6H6x/TEmc6QVsIIA0T36IuyOY3kNvIdyGddA2qt35cRamzxF8K5p0Opq4G4HjNbhQ==", "path": "runtime.any.system.resources.resourcemanager/4.3.0", "hashPath": "runtime.any.system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "path": "runtime.any.system.runtime/4.3.0", "hashPath": "runtime.any.system.runtime.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "path": "runtime.any.system.text.encoding/4.3.0", "hashPath": "runtime.any.system.text.encoding.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "path": "runtime.any.system.threading.tasks/4.3.0", "hashPath": "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512"}, "runtime.win.System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hHHP0WCStene2jjeYcuDkETozUYF/3sHVRHAEOgS3L15hlip24ssqCTnJC28Z03Wpo078oMcJd0H4egD2aJI8g==", "path": "runtime.win.system.diagnostics.debug/4.3.0", "hashPath": "runtime.win.system.diagnostics.debug.4.3.0.nupkg.sha512"}, "runtime.win.System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RkgHVhUPvzZxuUubiZe8yr/6CypRVXj0VBzaR8hsqQ8f+rUo7e4PWrHTLOCjd8fBMGWCrY//fi7Ku3qXD7oHRw==", "path": "runtime.win.system.runtime.extensions/4.3.0", "hashPath": "runtime.win.system.runtime.extensions.4.3.0.nupkg.sha512"}, "Serilog/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gmoWVOvKgbME8TYR+gwMf7osROiWAURterc6Rt2dQyX7wtjZYpqFiA/pY6ztjGQKKV62GGCyOcmtP1UKMHgSmA==", "path": "serilog/4.2.0", "hashPath": "serilog.4.2.0.nupkg.sha512"}, "Serilog.AspNetCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "path": "serilog.aspnetcore/9.0.0", "hashPath": "serilog.aspnetcore.9.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "path": "serilog.extensions.hosting/9.0.0", "hashPath": "serilog.extensions.hosting.9.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "path": "serilog.extensions.logging/9.0.0", "hashPath": "serilog.extensions.logging.9.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "path": "serilog.formatting.compact/3.0.0", "hashPath": "serilog.formatting.compact.3.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "path": "serilog.settings.configuration/9.0.0", "hashPath": "serilog.settings.configuration.9.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "path": "serilog.sinks.debug/3.0.0", "hashPath": "serilog.sinks.debug.3.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Seq/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-z5ig56/qzjkX6Fj4U/9m1g8HQaQiYPMZS4Uevtjg1I+WWzoGSf5t/E+6JbMP/jbZYhU63bA5NJN5y0x+qqx2Bw==", "path": "serilog.sinks.seq/8.0.0", "hashPath": "serilog.sinks.seq.8.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-vJv19UpWm6OOgnS9QLDnWARNVasXUfj8SFvlG7UVALm4nBnfwRnEky7C0veSDqMUmBeMPC6Ec3d6G1ts/J04Uw==", "path": "swashbuckle.aspnetcore/7.2.0", "hashPath": "swashbuckle.aspnetcore.7.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-y27fNDfIh1vGhJjXYynLcZjl7DLOW1bSO2MDsY9wB4Zm1fdxpPsuBSiR4U+0acWlAqLmnuOPKr/OeOgwRUkBlw==", "path": "swashbuckle.aspnetcore.swagger/7.2.0", "hashPath": "swashbuckle.aspnetcore.swagger.7.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pMrTxGVuXM7t4wqft5CNNU8A0++Yw5kTLmYhB6tbEcyBfO8xEF/Y8pkJhO6BZ/2MYONrRYoQTfPFJqu8fOf5WQ==", "path": "swashbuckle.aspnetcore.swaggergen/7.2.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.7.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hgrXeKzyp5OGN8qVvL7A+vhmU7mDJTfGpiMBRL66IcfLOyna8UTLtn3cC3CghamXpRDufcc9ciklTszUGEQK0w==", "path": "swashbuckle.aspnetcore.swaggerui/7.2.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.7.2.0.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PdkuMrwDhXoKFo/JxISIi9E8L+QGn9Iquj2OKDWHB6Y/HnUOuBouF7uS3R4Hw3FoNmwwMo6hWgazQdyHIIs27A==", "path": "system.configuration.configurationmanager/9.0.0", "hashPath": "system.configuration.configurationmanager.9.0.0.nupkg.sha512"}, "System.Data.DataSetExtensions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-221clPs1445HkTBZPL+K9sDBdJRB8UN8rgjO3ztB0CQ26z//fmJXtlsr6whGatscsKGBrhJl5bwJuKSA8mwFOw==", "path": "system.data.datasetextensions/4.5.0", "hashPath": "system.data.datasetextensions.4.5.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qd01+AqPhbAG14KtdtIqFk+cxHQFZ/oqRSCoxU1F+Q6Kv0cl726sl7RzU9yLFGd4BUOKdN4XojXF0pQf/R6YeA==", "path": "system.diagnostics.eventlog/9.0.0", "hashPath": "system.diagnostics.eventlog.9.0.0.nupkg.sha512"}, "System.Formats.Asn1/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VRDjgfqV0hCma5HBQa46nZTRuqfYMWZClwxUtvLJVTCeDp9Esdvr91AfEWP98IMO8ooSv1yXb6/oCc6jApoXvQ==", "path": "system.formats.asn1/9.0.0", "hashPath": "system.formats.asn1.9.0.0.nupkg.sha512"}, "System.Formats.Cbor/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mGaLOoiw7KurJagOOcIsWUoCT5ACIiGxKlCcbYQASefBGXjnCcKTq5Hdjb94eEAKg38zXKlHw4c6EjzgBl9dIw==", "path": "system.formats.cbor/6.0.0", "hashPath": "system.formats.cbor.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Packaging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "path": "system.io.packaging/8.0.1", "hashPath": "system.io.packaging.8.0.1.nupkg.sha512"}, "System.IO.Pipelines/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-UIBaK7c/A3FyQxmX/747xw4rCUkm1BhNiVU617U5jweNJssNjLJkPUGhBsrlDG0BpKWCYKsncD+Kqpy4KmvZZQ==", "path": "system.io.pipelines/9.0.2", "hashPath": "system.io.pipelines.9.0.2.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/*******": {"type": "package", "serviceable": true, "sha512": "sha512-rtfRierS5ZjG4xBfDKerRPImwDbav7q2hgf88jUZKfIjQb16PIQqzFCpPVWMb+7fS2ECXnPSmmBbssPz7WUg6g==", "path": "system.linq.dynamic.core/*******", "hashPath": "system.linq.dynamic.core.*******.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "path": "system.private.uri/4.3.0", "hashPath": "system.private.uri.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ptvgrFh7PvWI8bcVqG5rsA/weWM09EnthFHR5SCnS6IN+P4mj6rE1lBDC4U8HL9/57htKAqy4KQ3bBj84cfYyQ==", "path": "system.reflection.metadata/8.0.0", "hashPath": "system.reflection.metadata.8.0.0.nupkg.sha512"}, "System.Reflection.MetadataLoadContext/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SZxrQ4sQYnIcdwiO3G/lHZopbPYQ2lW0ioT4JezgccWUrKaKbHLJbAGZaDfkYjWcta1pWssAo3MOXLsR0ie4tQ==", "path": "system.reflection.metadataloadcontext/8.0.0", "hashPath": "system.reflection.metadataloadcontext.8.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "path": "system.runtime.caching/8.0.0", "hashPath": "system.runtime.caching.8.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>J<PERSON>+x/F6fmRQ7N6K8paasTw9PDZp4t7G76UjGNlSDgoHPF0h08vTzLYbLZpOLEJSg35d5wy2jCXGo84EN05DpQ==", "path": "system.security.cryptography.protecteddata/9.0.0", "hashPath": "system.security.cryptography.protecteddata.9.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Dataflow/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7V0I8tPa9V7UxMx/+7DIwkhls5ouaEMQx6l/GwGm1Y8kJQ61On9B/PxCXFLbgu5/C47g0BP2CUYs+nMv1+Oaqw==", "path": "system.threading.tasks.dataflow/8.0.0", "hashPath": "system.threading.tasks.dataflow.8.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Z.EntityFramework.Extensions.EFCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-D1+zqtocyQb7bCuHV/Nfkvu6dCB30Zg3WyGznZTsw7QxK2Qqi5805yysKqrtjUL+BxIxBoQYglPnzubQuwuErg==", "path": "z.entityframework.extensions.efcore/*********", "hashPath": "z.entityframework.extensions.efcore.*********.nupkg.sha512"}, "Z.EntityFramework.Plus.EFCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-yKbHDlH2kLbFZ7Hcb+OJSdRGrhWk7R99itDpTHrkwT/2i6ApgjgBRJSnv8MWuyxJk4M9cqqPDzcA12gsZPvsKA==", "path": "z.entityframework.plus.efcore/*********", "hashPath": "z.entityframework.plus.efcore.*********.nupkg.sha512"}, "Z.Expressions.Eval/6.2.10": {"type": "package", "serviceable": true, "sha512": "sha512-JngT7Ze4Pn/p9ci/0d40CJy/HN3BKegPt1FvmMjgc4tmWZR9uCLLYf9E6SY+wEFQO0EIaauVkHkcMkleZtCNDg==", "path": "z.expressions.eval/6.2.10", "hashPath": "z.expressions.eval.6.2.10.nupkg.sha512"}, "Application.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Antiforgery/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.KeyDerivation.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Results/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpLogging/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OutputCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RateLimiting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RequestDecompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticAssets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Features/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Embedded.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Core.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Stores.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/1*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Asn1.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Tar/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.JavaScript/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.OpenSsl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encodings.Web.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.RateLimiting.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}, "runtimes": {"win-x64": ["win", "any", "base"]}}