﻿"restore":{"projectUniqueName":"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Migrator\\Application.Migrator.csproj","projectName":"Application.Migrator","projectPath":"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Migrator\\Application.Migrator.csproj","outputPath":"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Migrator\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net9.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Infrastructure\\Application.Infrastructure.csproj":{"projectPath":"D:\\CUCKHQS_FULL\\cuckhqs\\Application.Infrastructure\\Application.Infrastructure.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.EntityFrameworkCore":{"target":"Package","version":"[9.0.2, )"},"Microsoft.EntityFrameworkCore.Design":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[9.0.0, )"},"Microsoft.EntityFrameworkCore.SqlServer":{"target":"Package","version":"[9.0.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}