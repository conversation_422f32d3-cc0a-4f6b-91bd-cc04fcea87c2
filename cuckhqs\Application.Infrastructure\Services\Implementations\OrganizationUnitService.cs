﻿using System.Xml.Linq;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category.Employee;
using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Repositories.Abstractions.IEmployee;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;
using Microsoft.Extensions.Configuration;

namespace Application.Infrastructure.Services.Implementations;

public class OrganizationUnitService : IOrganizationUnitService
{
    private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);
    private readonly IUnitOfWork _unitOfWork;
    private readonly IConfiguration _configuration;

    public OrganizationUnitService(
          IUnitOfWork unitOfWork,
          IConfiguration configuration)
    {
        _unitOfWork = unitOfWork;
        _configuration = configuration;
    }
    public async Task<List<OrganizationUnitDbResponse>> GetAllOrganizationUnitAsync()
    {
        try
        {
            var result = await DatabaseSql.ExecuteProcToList<OrganizationUnitDbResponse>("sp_GetAllActiveOrganizationUnits", new List<SqlParameter>(), _configuration);
            return result.ToList();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw new Exception(e.Message);
        }
    }
    public async Task<OrganizationUnitTreeResponse> GetOrganizationUnitTreeAsync()
    {
        try
        {
            var allUnits = await GetAllOrganizationUnitAsync();
            return BuildTree(allUnits);
        }
        catch (Exception e)
        {
            log.Error("Failed to build organization unit tree", e);
            throw;
        }
    }

    private OrganizationUnitTreeResponse BuildTree(List<OrganizationUnitDbResponse> units)
    {
        var lookup = units
            .Select(unit => new OrganizationUnitTreeResponse
            {
                Id = unit.Id,
                ParentId = unit.ParentId,
                IsRoot = unit.IsRoot,
                OrganizationUnitCode = unit.OrganizationUnitCode,
                OrganizationUnitName = unit.OrganizationUnitName,
                ShortOrganizationUnitName = unit.ShortOrganizationUnitName,
                FullOrganizationUnitName = unit.FullOrganizationUnitName,
                ParentCode = unit.ParentCode,
                Classify = unit.Classify,
                ClassifyGroup = unit.ClassifyGroup
            })
            .ToLookup(x => x.ParentId);

        var rootNodes = lookup[1].ToList(); // Get nodes with null ParentId
        AddChildren(rootNodes[0], lookup);
        //if (!rootNodes.Any())
        //{
        //    rootNodes = lookup[1].ToList(); // Fallback to ParentId = 0 if no null ParentId exists
        //}

        //foreach (var node in rootNodes)
        //{
        //    AddChildren(node, lookup);
        //}

        return rootNodes[0];
    }

    private void AddChildren(OrganizationUnitTreeResponse node, ILookup<int?, OrganizationUnitTreeResponse> lookup)
    {
        if (lookup.Contains(node.Id))
        {
            node.Children.AddRange(lookup[node.Id]);
            //foreach (var child in node.Children)
            //{
            //    AddChildren(child, lookup);
            //}
        }
    }

    public async Task<List<Organization_Unit_Tree_Response>> OrganizationUnitCascadeEmployee()
    {
        try
        {
            var result = await DatabaseSql.ExecuteProcToList<Employee_Receiced_Response>("sp_Get_Employee_Receiced", new List<SqlParameter>(), _configuration);

            var treeData = result
                .GroupBy(e => new { e.OrganizationUnitId, e.OrganizationUnitName })
                .Select(g => new Organization_Unit_Tree_Response
                {
                    OrganizationUnitId = g.Key.OrganizationUnitId,
                    OrganizationUnitName = g.Key.OrganizationUnitName,
                    Employees = g.Select(e => new Employee_Receiced_Response
                    {
                        Id = e.Id,
                        Fullname = e.Fullname,
                        OrganizationUnitId = e.OrganizationUnitId,
                        OrganizationUnitName = e.OrganizationUnitName
                    }).ToList()
                })
                .ToList();

            return treeData;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw new Exception(e.Message);
        }
    }

    public async Task<BaseSearchResponse<OrganizationUnitResponse>> SearchOrganizationUnitAsync(SearchOrganizationUnitRequest request)
    {
        try
        {
            int? parent_id ;
            var org = await _unitOfWork.Organizations.GetAllOrganiztionUnitAsync();
            var parentIds = org?.Select(x => x.ParentId).Where(id => id != null).Distinct().ToList() ?? new List<int?>();
            if (request.ParentId == null || request.ParentId == 0)
            {
                int org_id_root = org?.Where(e => e.IsRoot == true)
                          .Select(e => e.Id)
                          .FirstOrDefault() ?? 0;
                parent_id = org?.Where(e => e.ParentId == org_id_root)
                          .Select(e => e.Id)
                          .FirstOrDefault() ?? 0;
            }else parent_id = request.ParentId;
                IQueryable<OrganizationUnitResponse> query = _unitOfWork.Organizations
                                                               .AsQueryable()
                                                               .AsNoTracking()
                                                               .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                                                      x.OrganizationUnitCode.Contains(request.keyword) ||
                                                                     x.Name.Contains(request.keyword) ||
                                                                     x.ShortOrganizationUnitName.Contains(request.keyword)) &&
                                                                   x.ParentId == parent_id)
                                                               .Select(s => new OrganizationUnitResponse()
                                                               {
                                                                   Id = s.Id,
                                                                   ParentId = s.ParentId,
                                                                   IsRoot = s.IsRoot,
                                                                   OrganizationUnitCode = s.OrganizationUnitCode,
                                                                   ParentCode = s.ParentCode,
                                                                   Classify = s.Classify,
                                                                   ClassifyGroup = s.ClassifyGroup,
                                                                   TrainingMaterialCode = s.TrainingMaterialCode,
                                                                   OrganizationUnitName = s.Name,
                                                                   ShortOrganizationUnitName = s.ShortOrganizationUnitName,
                                                                   Tel = s.Tel,
                                                                   Fax = s.Fax,
                                                                   Email = s.Email,
                                                                   Address = s.Address,
                                                                   Website = s.Website,
                                                                   Director = s.Director,
                                                                   AccountNumber = s.AccountNumber,
                                                                   BankName = s.BankName,
                                                                   Description = s.Description,
                                                                   Active = s.Active == true ? "Có" : "Không",
                                                                   SortOrder = s.SortOrder,
                                                                   CreatedDate = s.CreatedDate,
                                                                   ModifiedDate = s.ModifiedDate,
                                                                   IPAddress = s.IPAddress,
                                                                   ModifiedBy = s.ModifiedBy,
                                                                   CreatedBy = s.CreatedBy,
                                                                   FullOrganizationUnitName = s.FullOrganizationUnitName,
                                                                   Expandable = parentIds.Contains(s.Id)
                                                               });
            return await BaseSearchResponse<OrganizationUnitResponse>.GetResponse(query, request);
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại CreateSoftwaresAsync: " + ex.ToString());
            throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
        }
    }

    public async Task<List<OrganizationUnitResponse>> GetOrganizationUnitById(int Id)
    {
        try
        {
            var org = await _unitOfWork.Organizations.GetAllOrganiztionUnitAsync();
            var parentIds = org?.Select(x => x.ParentId).Where(id => id != null).Distinct().ToList() ?? new List<int?>();

            var Emm = await _unitOfWork.Organizations
           .AsQueryable()
           .AsNoTracking()
           .Where(s => s.Id == Id)
           .ToListAsync();

            var Org_id = Emm.Select(s => new OrganizationUnitResponse
            {
                Id = s.Id,
                ParentId = s.ParentId,
                IsRoot = s.IsRoot,
                OrganizationUnitCode = s.OrganizationUnitCode,
                ParentCode = s.ParentCode,
                Classify = s.Classify,
                ClassifyGroup = s.ClassifyGroup,
                TrainingMaterialCode = s.TrainingMaterialCode,
                OrganizationUnitName = s.Name,
                ShortOrganizationUnitName = s.ShortOrganizationUnitName,
                Tel = s.Tel,
                Fax = s.Fax,
                Email = s.Email,
                Address = s.Address,
                Website = s.Website,
                Director = s.Director,
                AccountNumber = s.AccountNumber,
                BankName = s.BankName,
                Description = s.Description,
                Active = s.Active == true ?"Có":"Không",
                SortOrder = s.SortOrder,
                CreatedDate = s.CreatedDate,
                ModifiedDate = s.ModifiedDate,
                IPAddress = s.IPAddress,
                ModifiedBy = s.ModifiedBy,
                CreatedBy = s.CreatedBy,
                FullOrganizationUnitName = s.FullOrganizationUnitName,
                Expandable = parentIds.Contains(s.Id)
            }).ToList();
      
            return Org_id;
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại CreateSoftwaresAsync: " + ex.ToString());
            throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
        }
    }

    public async Task<OrganizationUnitResponse> CreateOrganizationUnitAsync(CreateOrganizationUnitRequest request)
    {
        try
        {
            var entity = CreateOrganizationUnitRequest.Create(request);

            await _unitOfWork.Organizations.AddAsync(entity);
            await _unitOfWork.CommitChangesAsync();
            return OrganizationUnitResponse.Create(entity);
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
            throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
        }
    }

    public async Task<bool> UpdateOrganizationUnitAsync(UpdateOrganizationUnitRequest request)
    {
        try
        {
            var findRecord = await _unitOfWork.Organizations.AsQueryable()
                                                    .AsNoTracking()
                                                    .FirstOrDefaultAsync(x => x.Id == request.Id);

            if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

            var updateRecord = UpdateOrganizationUnitRequest.Create(request);

            await _unitOfWork.Organizations.UpdateAsync(request.Id, updateRecord);
            await _unitOfWork.CommitChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
            throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
        }
    }

    public async Task<string> DeleteOrganizationUnitAsync(DeleteOrganizationUnitRequest request)
    {
        try
        {
            var org = await _unitOfWork.Organizations.GetAllOrganiztionUnitAsync();
            var hasParentReference = org.Any(o => o.ParentId.HasValue && request.Ids.Contains(o.ParentId.Value));
            if (hasParentReference)
            {
                return "Xóa không thành công: Một hoặc nhiều đơn vị có đơn vị con";
            }
            var record = await _unitOfWork.Organizations.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

            _unitOfWork.Organizations.RemoveRange(record);
            await _unitOfWork.CommitChangesAsync();
            return "Xóa thành công";
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
            throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
        }
    }
}